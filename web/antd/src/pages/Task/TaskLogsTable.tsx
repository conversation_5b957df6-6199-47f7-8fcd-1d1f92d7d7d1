import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Button,
    Card,
    Form,
    Input,
    Select,
    DatePicker,
    Table,
    Tag,
    Space,
    Typography,
    Modal,
    Progress,
    Tooltip,
    message,
    Divider,
} from 'antd';
import {
    SearchOutlined,
    ReloadOutlined,
    EyeOutlined,
    PlayCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined,
    VideoCameraOutlined,
    FileImageOutlined,
    AudioOutlined,
    FileTextOutlined,
    QuestionCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TableProps } from 'antd/es/table';
import { API, showError, timestamp2string } from '../../helpers';
import { ITEMS_PER_PAGE } from '../../constants';

const { RangePicker } = DatePicker;
const { Text } = Typography;

interface TaskRecord {
    id: string;
    task_id: string;
    platform: string;
    action: string;
    status: string;
    progress: string;
    submit_time: number;
    start_time: number;
    finish_time: number;
    fail_reason?: string;
    result_url?: string;
    channel_id?: number;
    user_id?: number;
}

interface TaskLogsTableProps {}

const TaskLogsTable: React.FC<TaskLogsTableProps> = () => {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [tasks, setTasks] = useState<TaskRecord[]>([]);
    const [total, setTotal] = useState(0);
    const [current, setCurrent] = useState(1);
    const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
    const [modalVisible, setModalVisible] = useState(false);
    const [modalContent, setModalContent] = useState('');
    const [modalTitle, setModalTitle] = useState('');

    // 渲染任务类型
    const renderTaskType = (action: string) => {
        const typeMap: Record<string, { color: string; icon: React.ReactNode; text: string }> = {
            'video_generation': { color: 'blue', icon: <VideoCameraOutlined />, text: t('tasks.videoGeneration') },
            'image_generation': { color: 'green', icon: <FileImageOutlined />, text: t('tasks.imageGeneration') },
            'music_generation': { color: 'purple', icon: <AudioOutlined />, text: t('tasks.musicGeneration') },
            'text_generation': { color: 'orange', icon: <FileTextOutlined />, text: t('tasks.textGeneration') },
            // 兼容历史数据
            'music': { color: 'purple', icon: <AudioOutlined />, text: t('tasks.musicGeneration') },
        };

        const config = typeMap[action] || { color: 'default', icon: <QuestionCircleOutlined />, text: t('tasks.unknown') };

        return (
            <Tag color={config.color} icon={config.icon}>
                {config.text}
            </Tag>
        );
    };

    // 渲染平台
    const renderPlatform = (platform: string) => {
        const platformMap: Record<string, { color: string; text: string }> = {
            'vertex': { color: 'blue', text: 'Vertex AI' },
            'suno': { color: 'green', text: 'Suno' },
            'kling': { color: 'orange', text: 'Kling' },
            'jimeng': { color: 'purple', text: 'Jimeng' },
            'mj': { color: 'magenta', text: 'Midjourney' },
            'openai': { color: 'cyan', text: 'OpenAI' },
        };

        const config = platformMap[platform] || { color: 'default', text: platform || t('tasks.unknown') };

        return (
            <Tag color={config.color}>
                {config.text}
            </Tag>
        );
    };

    // 渲染任务状态
    const renderStatus = (status: string) => {
        const statusMap: Record<string, { color: string; icon: React.ReactNode; text: string }> = {
            'completed': { color: 'success', icon: <CheckCircleOutlined />, text: t('tasks.success') },
            'failed': { color: 'error', icon: <CloseCircleOutlined />, text: t('tasks.failed') },
            'in_progress': { color: 'processing', icon: <PlayCircleOutlined />, text: t('tasks.inProgress') },
            'submitted': { color: 'warning', icon: <ClockCircleOutlined />, text: t('tasks.submitted') },
            'queued': { color: 'default', icon: <ClockCircleOutlined />, text: t('tasks.queued') },
            'pending': { color: 'default', icon: <ExclamationCircleOutlined />, text: t('tasks.notStarted') },
            // 兼容旧格式
            'SUCCESS': { color: 'success', icon: <CheckCircleOutlined />, text: t('tasks.success') },
            'FAILURE': { color: 'error', icon: <CloseCircleOutlined />, text: t('tasks.failed') },
            'IN_PROGRESS': { color: 'processing', icon: <PlayCircleOutlined />, text: t('tasks.inProgress') },
            'SUBMITTED': { color: 'warning', icon: <ClockCircleOutlined />, text: t('tasks.submitted') },
            'QUEUED': { color: 'default', icon: <ClockCircleOutlined />, text: t('tasks.queued') },
            'NOT_START': { color: 'default', icon: <ExclamationCircleOutlined />, text: t('tasks.notStarted') },
        };

        const config = statusMap[status] || { color: 'default', icon: <QuestionCircleOutlined />, text: t('tasks.unknown') };

        return (
            <Tag color={config.color} icon={config.icon}>
                {config.text}
            </Tag>
        );
    };

    // 渲染进度
    const renderProgress = (progress: string, status: string) => {
        if (!progress || progress === '-') {
            return '-';
        }

        const percent = parseInt(progress.replace('%', ''));
        if (isNaN(percent)) {
            return progress;
        }

        return (
            <Progress
                percent={percent}
                size="small"
                status={status === 'FAILURE' ? 'exception' : status === 'SUCCESS' ? 'success' : 'active'}
                style={{ minWidth: '120px' }}
            />
        );
    };

    // 渲染时间
    const renderTime = (timestamp: number) => {
        if (!timestamp) return '-';
        return timestamp2string(timestamp);
    };

    // 渲染持续时间
    const renderDuration = (startTime: number, finishTime: number) => {
        if (!startTime || !finishTime) return '-';
        const duration = finishTime - startTime;
        const color = duration > 60 ? 'red' : 'green';
        return (
            <Tag color={color}>
                {duration}s
            </Tag>
        );
    };

    // 渲染结果/详情
    const renderResult = (record: TaskRecord) => {
        const { status, result_url, fail_reason } = record;

        if (status === 'completed' && result_url) {
            return (
                <Space>
                    <Button
                        type="link"
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={() => window.open(result_url, '_blank')}
                    >
                        {t('tasks.viewResult')}
                    </Button>
                </Space>
            );
        }

        if (status === 'failed' && fail_reason) {
            return (
                <Button
                    type="link"
                    size="small"
                    onClick={() => {
                        setModalTitle(t('tasks.errorDetails'));
                        setModalContent(fail_reason);
                        setModalVisible(true);
                    }}
                >
                    {t('tasks.viewError')}
                </Button>
            );
        }

        return '-';
    };

    const columns: ColumnsType<TaskRecord> = [
        {
            title: t('tasks.taskId'),
            dataIndex: 'task_id',
            key: 'task_id',
            width: 200,
            ellipsis: true,
            render: (text: string, record: TaskRecord) => (
                <Tooltip title={text}>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            setModalTitle(t('tasks.taskDetails'));
                            setModalContent(JSON.stringify(record, null, 2));
                            setModalVisible(true);
                        }}
                    >
                        {text}
                    </Button>
                </Tooltip>
            ),
        },
        {
            title: t('tasks.platform'),
            dataIndex: 'platform',
            key: 'platform',
            width: 120,
            render: renderPlatform,
        },
        {
            title: t('tasks.type'),
            dataIndex: 'action',
            key: 'action',
            width: 140,
            render: renderTaskType,
        },
        {
            title: t('tasks.status'),
            dataIndex: 'status',
            key: 'status',
            width: 120,
            render: renderStatus,
        },
        {
            title: t('tasks.progress'),
            dataIndex: 'progress',
            key: 'progress',
            width: 150,
            render: (progress: string, record: TaskRecord) => renderProgress(progress, record.status),
        },
        {
            title: t('tasks.submitTime'),
            dataIndex: 'submit_time',
            key: 'submit_time',
            width: 180,
            render: renderTime,
        },
        {
            title: t('tasks.duration'),
            key: 'duration',
            width: 100,
            render: (_, record: TaskRecord) => renderDuration(record.start_time, record.finish_time),
        },
        {
            title: t('tasks.result'),
            key: 'result',
            width: 120,
            render: (_, record: TaskRecord) => renderResult(record),
        },
    ];

    // 加载任务列表
    const loadTasks = async (page = 1, size = pageSize) => {
        setLoading(true);
        try {
            const values = form.getFieldsValue();
            const params = new URLSearchParams({
                page: page.toString(),
                page_size: size.toString(),
            });

            if (values.platform) {
                params.append('task_type', values.platform); // 后端期望的是task_type参数
            }
            if (values.status) {
                params.append('status', values.status);
            }
            // 注意：当前后端不支持task_id和时间范围查询，这些参数会被忽略
            // TODO: 需要后端支持更多查询参数

            const response = await API.get(`/api/task/?${params.toString()}`);
            const { success, message: msg, data } = response.data;

            if (success) {
                setTasks(data.items || []);
                setTotal(data.total || 0);
                setCurrent(page);
                setPageSize(size);
            } else {
                showError(msg);
            }
        } catch (error) {
            showError(t('tasks.loadError'));
        } finally {
            setLoading(false);
        }
    };

    // 搜索
    const handleSearch = () => {
        loadTasks(1, pageSize);
    };

    // 重置
    const handleReset = () => {
        form.resetFields();
        loadTasks(1, pageSize);
    };

    // 刷新
    const handleRefresh = () => {
        loadTasks(current, pageSize);
    };

    // 分页变化
    const handleTableChange: TableProps<TaskRecord>['onChange'] = (pagination) => {
        const { current: page = 1, pageSize: size = ITEMS_PER_PAGE } = pagination;
        loadTasks(page, size);
    };

    useEffect(() => {
        loadTasks();
    }, []);

    return (
        <Card
            title={
                <Space>
                    <EyeOutlined />
                    {t('tasks.title')}
                </Space>
            }
            extra={
                <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRefresh}
                    loading={loading}
                >
                    {t('common.refresh')}
                </Button>
            }
        >
            <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
                style={{ marginBottom: 16 }}
            >
                {/* 暂时隐藏任务ID搜索，等待后端支持
                <Form.Item name="task_id">
                    <Input
                        placeholder={t('tasks.taskIdPlaceholder')}
                        prefix={<SearchOutlined />}
                        style={{ width: 200 }}
                    />
                </Form.Item>
                */}
                <Form.Item name="platform">
                    <Select
                        placeholder={t('tasks.typePlaceholder')}
                        style={{ width: 150 }}
                        allowClear
                    >
                        <Select.Option value="video_generation">{t('tasks.videoGeneration')}</Select.Option>
                        <Select.Option value="image_generation">{t('tasks.imageGeneration')}</Select.Option>
                        <Select.Option value="music_generation">{t('tasks.musicGeneration')}</Select.Option>
                        <Select.Option value="text_generation">{t('tasks.textGeneration')}</Select.Option>
                    </Select>
                </Form.Item>
                <Form.Item name="status">
                    <Select
                        placeholder={t('tasks.statusPlaceholder')}
                        style={{ width: 120 }}
                        allowClear
                    >
                        <Select.Option value="SUCCESS">{t('tasks.success')}</Select.Option>
                        <Select.Option value="FAILURE">{t('tasks.failed')}</Select.Option>
                        <Select.Option value="IN_PROGRESS">{t('tasks.inProgress')}</Select.Option>
                        <Select.Option value="SUBMITTED">{t('tasks.submitted')}</Select.Option>
                        <Select.Option value="QUEUED">{t('tasks.queued')}</Select.Option>
                    </Select>
                </Form.Item>
                {/* 暂时隐藏时间范围搜索，等待后端支持
                <Form.Item name="dateRange">
                    <RangePicker
                        showTime
                        placeholder={[t('tasks.startTime'), t('tasks.endTime')]}
                        style={{ width: 300 }}
                    />
                </Form.Item>
                */}
                <Form.Item>
                    <Space>
                        <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                            {t('common.search')}
                        </Button>
                        <Button onClick={handleReset}>
                            {t('common.reset')}
                        </Button>
                    </Space>
                </Form.Item>
            </Form>

            <Table
                columns={columns}
                dataSource={tasks}
                rowKey="id"
                loading={loading}
                pagination={{
                    current,
                    pageSize,
                    total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                        t('common.pagination.total', {
                            start: range[0],
                            end: range[1],
                            total,
                        }),
                    pageSizeOptions: ['10', '20', '50', '100'],
                }}
                onChange={handleTableChange}
                scroll={{ x: 'max-content' }}
            />

            <Modal
                title={modalTitle}
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={[
                    <Button key="close" onClick={() => setModalVisible(false)}>
                        {t('common.close')}
                    </Button>,
                ]}
                width={800}
            >
                <pre style={{ whiteSpace: 'pre-wrap', maxHeight: 400, overflow: 'auto' }}>
                    {modalContent}
                </pre>
            </Modal>
        </Card>
    );
};

export default TaskLogsTable;
