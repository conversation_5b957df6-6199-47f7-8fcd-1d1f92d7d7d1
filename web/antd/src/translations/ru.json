{"message": {"copyModelSuccess": "Модель имени скопирована в буфер обмена!", "copyFailed": "Копирование не удалось, пожалуйста, скопируйте вручную.", "logoutSuccess": "Вы успешно вышли из системы.", "loginSuccess": {"default": "Успешный вход в систему", "welcomeBack": "Добро пожаловать обратно!"}, "removeLocalStorage": {"confirm": "Удалить локальный кэш?", "success": "Очистка локального кэша прошла успешно."}, "loadData": {"error": "Не удалось загрузить данные {{name}}."}, "noNotice": "Нет объявлений.", "verification": {"turnstileChecking": "Turnstile проверяет пользовательскую среду!", "pleaseWait": "Пожалуйста, попробуйте позже."}, "clipboard": {"inviteCodeDetected": "Обнаружен код приглашения, он был автоматически введен!", "clickToCopy": "Нажмите, чтобы скопировать", "copySuccess": "Копирование успешно завершено."}}, "common": {"yes": "да", "no": "Нет", "copyAll": "Скопировать всё", "all": "всё", "more": "больше", "unlimited": "Без ограничений", "enabled": "Открыть", "disabled": "Закрыть", "save": "Сохранить", "cancel": "Отмена", "create": "Создание", "usd": "доллар США", "day": "{{count}} д<PERSON><PERSON><PERSON>", "day_plural": "{{count}} д<PERSON><PERSON><PERSON>", "days": "небо", "seconds": "секунда", "times": "Time", "submit": "Отправить", "bind": "Привязка", "unknown": "Неизвестно", "loading": "Загрузка...", "copyFailed": "Копирование не удалось.", "people": "человек", "ok": "определенно", "close": "Закрыть", "copied": "Скопировано", "expand": "развернуть", "collapse": "Собрать обратно", "none": "нет", "remark": "Примечание", "selectPlaceholder": "Пожалуйста, выберите {{name}}.", "on": "открыть", "off": "закрыть", "name": "идентификатор", "displayName": "Отображаемое имя", "description": "Описание", "ratio": "Увеличение", "unnamed": "Неназванный канал", "groups": "Группировка", "captchaPlaceholder": "Пожалуйста, введите код подтверждения.", "confirm": "Подтверждение", "permissions": "Разрешения", "actions": "Действия", "createdTime": "Время создания", "expiredTime": "Время истечения", "search": "Поиск", "reset": "Сброс", "refresh": "Обновить", "pagination": {"total": "Всего {{total}} элементов"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "Нажмите, чтобы открыть ссылку."}, "userRole": {"normal": "Обычный пользователь", "agent": "агент", "admin": "Администратор", "superAdmin": "Супер администратор", "loading": "Загрузка..."}, "channelStatus": {"enabled": "Включить", "disabled": "Запретить", "waitingRestart": "Ожидание перезагрузки", "waiting": "ожидание", "autoStoppedTitle": "Канал автоматически повторил попытку более максимального количества раз или сработало условие автоматического отключения.", "stopped": "Отключить", "partiallyDisabled": "Частичное отключение", "unknown": "Неизвестно", "reason": "Причина"}, "channelBillingTypes": {"payAsYouGo": "Постоплатная тарификация", "payPerRequest": "Постоплатный расчет", "unknown": "Неизвестный способ"}, "tokenStatus": {"normal": "нормально", "disabled": "Запретить", "expired": "истекший", "exhausted": "истощение", "unknown": "Неизвестно"}, "userStatus": {"normal": "нормально", "banned": "блокировка", "unknown": "Неизвестно"}, "redemptionStatus": {"normal": "нормально", "disabled": "Запретить", "redeemed": "Обменяно", "expired": "истекший", "unknown": "Неизвестно"}, "duration": {"request": "запрос", "firstByte": "первый байт", "total": "Итого", "seconds": "секунда", "lessThanOneSecond": "<1 секунда"}, "streamType": {"stream": "потоковый", "nonStream": "Непотоковый"}, "noSet": {"title": "Администратор не настроил {{name}}", "name": {"about": "о", "chat": "Ди<PERSON><PERSON><PERSON><PERSON>"}}, "buttonText": {"add": "новый", "cancel": "Отмена", "confirm": "Подтверждение", "delete": "Удалить", "edit": "Редактировать", "save": "Сохранить", "updateBalance": "Обновить баланс", "test": "Тестирование", "multiple": "Множественный выбор"}, "channelPage": {"title": "Управление каналами"}, "channelStatusCount": {"title": "Статистика состояния канала", "summary": "Включено {{enabled}} | Отключено {{disabled}} | Повторная попытка {{retry}} | Остановлено {{stopped}}", "statusEnabled": "Включено", "statusDisabled": "Отключено", "statusRetry": "Повторная попытка", "statusStopped": "Остановлено", "statusPartially": "Частично отключено"}, "header": {"routes": {"status": "состояние", "home": "Главная страница", "chat": "Ди<PERSON><PERSON><PERSON><PERSON>", "pptGen": "Генерация PPT", "chart": "статистика", "agency": "агент", "channel": "<PERSON><PERSON><PERSON><PERSON>", "ability": "Канальная способность", "channelGroup": "Канал группа", "token": "токен", "log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logDetail": "детали", "midjourney": "рисование", "user": "пользователь", "config": "Конфигурация", "packagePlanAdmin": "комплексное предложение", "redemption": "Код для обмена", "group": "Группировка", "query": "Запрос", "about": "о", "setting": {"default": "Настройки", "operation": "Настройки операционной деятельности", "system": "Системные настройки", "global": "Глобальные настройки", "advance": "Настройки характеристик", "sensitive": "Конфигурация чувствительных слов", "verification": "Конфигурация кода проверки", "update": "Проверка обновлений"}, "account": {"default": "аккаунт", "profile": "Личный кабинет", "cardTopup": "<PERSON><PERSON><PERSON><PERSON><PERSON> карты", "onlineTopup": "Онлайн пополнение", "recharge": "Пополнение баланса", "balanceTransfer": "Перенос остатка", "pricing": "Описание расходов", "packagePlan": {"list": "Покупка пакета", "record": "Запись о покупке"}, "notificationSettings": "Настройки уведомлений"}, "tools": {"default": "инструмент", "fileUpload": "Загрузка файла", "keyExtraction": "Извлечение ключа", "multiplierCalculator": "Калькулятор кратности", "shortLink": "Генерация коротких ссылок", "testConnection": "Тест доступа", "customPrompts": "Управление подсказками", "redis": "Визуализация Redis", "ratioCompare": "Сравнение коэффициентов", "serverLog": "Просмотрщик журналов сервера"}, "onlineTopupRecord": "Запись о пополнении счета", "channelScores": "Оценка канала", "dynamicRouter": "Динамический маршрут", "task": "Асинхронные задачи", "agencyJoin": "Партнерство агентства"}, "dropdownMenu": {"profile": "Личный кабинет", "recharge": "Пополнение баланса", "agencyCenter": "Центр агентов", "checkin": "Регистрация", "darkMode": {"enable": "Темный режим", "disable": "Дневной режим"}, "fullScreen": {"default": "Переключить на полный экран", "enable": "Полноэкранный режим", "disable": "Выйти из полноэкранного режима"}, "logout": "Выйти из системы"}, "checkin": {"default": "Регистрация", "success": "Успешная регистрация", "failed": "Не удалось выполнить регистрацию.", "verification": "Пожалуйста, завершите проверку."}, "avatarProps": {"login": "вход"}}, "settings": {"public": {"titles": {"default": "Общественные настройки"}, "SystemName": "Название системы", "ServerAddress": "Адрес обслуживания", "TopUpLink": "Ссылка для пополнения счёта", "ChatLink": "Ссылка на диалог", "Logo": "Системный логотип", "HomePageContent": "Главная страница содержимого", "About": "О содержании", "Notice": "Содержание объявления", "Footer": "Содержимое нижнего колонтитула", "RegisterInfo": "Уведомление о регистрации", "HeaderScript": "Пользовательский заголовок", "SiteDescription": "Описание сайта", "PrivacyPolicy": "Политика конфиденциальности", "ServiceAgreement": "Соглашение об услугах", "FloatButton": {"FloatButtonEnabled": "Открыть", "DocumentInfo": "Документальная информация", "WechatInfo": "Сообщение в WeChat", "QqInfo": "Q Q информация"}, "CustomThemeConfig": "Пользовательская тема", "AppList": "Ссылки-партнеры"}}, "home": {"default": {"title": "Добро пожаловать!", "subtitle": "Title", "start": "Начать использовать", "description": {"title": "Новые функции:", "part1": "Совершенно новый интерфейс, удобный и быстрый.", "part2": "Оптимизация механизма планирования, высокая эффективность и стабильность.", "part3": "Разработано для бизнеса, безопасно и надежно.", "part4": "Больше продвинутых функций ждет тебя."}}}, "dailyUsageChart": {"title": "Ежедневное использование модели", "yAxisName": "Использование (USD)", "loadingTip": "Ежедневное использование", "fetchError": "Ошибка при получении данных о ежедневном использовании:"}, "modelUsageChart": {"title": "Использование модели", "hourlyTitle": "Использование модели в час.", "dailyTitle": "Использование модели каждый день", "weeklyTitle": "Использование модели каждую неделю", "monthlyTitle": "Использование модели каждый месяц"}, "granularity": {"hour": "каждый час", "day": "Каждый день", "week": "каждую неделю", "month": "ежемесячно", "all": "всё"}, "abilitiesTable": {"title": "Канальная способность", "export": "Экспортировать", "group": "Группа", "model": "модель", "channelId": "Номер канала", "enabled": "Включено", "weight": "весовой коэффициент", "priority": "приоритет", "billingType": "Тип расчета", "functionCallEnabled": "Включение вызова функций", "imageSupported": "Поддержка изображений", "yes": "да", "no": "Нет", "perToken": "Оплата по токенам", "perRequest": "Оплата по запросу", "noDataToExport": "Нет данных для экспорта.", "exportConfirm": "Вы уверены, что хотите экспортировать данные с текущей страницы?", "exportSuccess": "Экспорт успешен.", "toggleSuccess": "Переключение успешно.", "toggleError": "Смена не удалась.", "selectOrInputGroup": "Выберите или введите группу пользователей."}, "logsTable": {"retry": "Повторить попытку", "retryChannelList": "Список каналов повторной попытки", "retryDurations": "Детали времени повторной попытки", "channel": "<PERSON><PERSON><PERSON><PERSON>", "duration": "время затраты", "startTime": "Время начала", "endTime": "Время окончания", "retryCount": "Количество попыток", "retryDetails": "Подробности повторной попытки", "totalRetryTime": "Общее время повторной попытки", "seconds": "секунда", "tokenGroup": "Группировка токенов", "selectGroup": "Выбор группы", "dailyModelUsageStats": "Обзор вызовов данных", "time": "время", "moreInfo": "Больше информации", "ip": "IP", "remoteIp": "удаленный IP", "ipTooltip": "IP: {{ip}}  \nУдаленный IP: {{remoteIp}}", "requestId": "Запрос ID", "username": "имя пользователя", "userId": "Пользовательский ID", "tokenName": "Название токена", "token": "токен", "type": "тип", "typeUnknown": "Неизвестно", "type充值": "пополнение", "type消费": "потребление", "type管理": "управление", "type系统": "система", "type邀请": "приглашение", "type提示": "Подсказка", "type警告": "Предупреждение", "type错误": "Ошибка", "type签到": "Регистрация", "type日志": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type退款": "возврат денег", "type邀请奖励金划转": "Перевод приглашения вознаграждения", "type代理奖励": "Text", "type下游错误": "Ошибки на стороне потребителя", "type测试渠道": "Тестовый канал", "typeRecharge": "пополнение", "typeConsumption": "потребление", "typeManagement": "управление", "typeSystem": "система", "typeInvitation": "приглашение", "typePrompt": "Подсказка", "typeWarning": "Предупреждение", "typeError": "Ошибка", "typeCheckin": "Регистрация", "typeLog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeRefund": "возврат средств", "typeInviteReward": "Перевод бонуса за приглашение", "typeAgencyBonus": "Text", "typeDownstreamError": "Ошибки на стороне потребителя", "typeChannelTest": "Тестовый канал", "channelId": "Идентификатор канала", "channelName": "Название канала", "model": "модель", "modelPlaceholder": "Введите/выберите название модели", "info": "информация", "isStream": "потоковый", "isStreamPlaceholder": "Ввод/выбор, является ли потоковым", "prompt": "Подсказка", "completion": "дополнить", "consumption": "потребление", "consumptionRange": "Диапазон потребительских расходов", "description": "Description", "action": "операция", "details": "подробности", "tokenKey": "То<PERSON><PERSON><PERSON>-ключ", "requestDuration": "Время запроса", "firstByteDuration": "Время первого байта", "totalDuration": "Общее время затрачено", "lessThanOneSecond": "<1 секунда", "modelInvocation": "Вызов модели", "modelUsage": "Использование модели", "totalQuota": "Общий лимит потребления: {{quota}}", "totalRpm": "Количество запросов в минуту: {{rpm}}", "totalTpm": "Количество токенов в минуту: {{tpm}}", "totalMpm": "Сумма/минуту: {{mpm}}", "dailyEstimate": "Ожидаемые ежедневные расходы: {{estimate}}", "currentStats": "Текущий RPM: {{rpm}} Текущий TPM: {{tpm}} Текущий MPM: ${{mpm}} Оценка дневных расходов: ${{dailyEstimate}}", "statsTooltip": "Только статистика неархивированных журналов, RPM: количество запросов в минуту, TPM: количество токенов в минуту, MPM: деньги, потребляемые в минуту, предполагаемое дневное потребление основано на текущем MPM.", "showAll": "Все показать", "exportConfirm": "Экспортировать журналы этой страницы?", "export": "Экспортировать", "statsData": "Статистические данные", "today": "в тот день", "lastHour": "1 час", "last3Hours": "3 часа", "lastDay": "1 день", "last3Days": "3 дня", "last7Days": "7 дней", "lastMonth": "1 месяц", "last3Months": "3 месяца", "excludeModels": "исключительная модель", "selectModelsToExclude": "Выберите модели, которые нужно исключить.", "excludeErrorCodes": "Исключить код ошибки", "excludeErrorCodesPlaceholder": "Выберите коды ошибок, которые нужно исключить.", "errorCode": "Код ошибки", "errorCodePlaceholder": "Введите/выберите код ошибки", "timezoneTip": "Текущий часовой пояс: {timezone}", "timezoneNote": "Time", "timezoneDescription": "Статистические данные сгруппированы по датам в соответствии с вашим текущим часовым поясом. Разные часовые пояса могут привести к различиям в периодах группировки данных. Чтобы внести изменения, перейдите в личный кабинет и измените настройки часового пояса.", "goToProfile": "Перейти в личный кабинет", "realtimeQuota": "Time", "viewTotalQuota": "Посмотреть общие расходы", "viewTotalQuotaTip": "Посмотреть общую сумму исторических расходов (поиск может занять несколько секунд)", "loadingTotalQuota": "Запрашиваю общую сумму расходов, пожалуйста, подождите...", "totalQuotaTitle": "Историческая статистика общего потребления", "loadTotalQuotaError": "Не удалось получить общую сумму расходов.", "requestLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON> запросов - {{requestId}}", "noRequestLogs": "Нет запросов в журнале.", "metricsExplanation": "Только статистика неархивированных журналов, RPM: количество запросов в минуту, TPM: количество токенов в минуту, MPM: деньги, потребляемые в минуту, предполагаемое дневное потребление основано на текущем MPM.", "autoRefresh": "Автообновление", "autoRefreshTip": "Нажмите, чтобы включить/выключить автоматическое обновление. После включения данные будут автоматически обновляться через заданные интервалы времени.", "autoRefreshOn": "Автообновление включено.", "autoRefreshOff": "Автообновление отключено.", "refreshInterval": "Интервал обновления", "stopRefresh": "Остановите обновление.", "secondsWithValue": "{{seconds}} секунд", "minutesWithValue": "{{minutes}} минут"}, "mjLogs": {"logId": "ID журнала", "submitTime": "Время подачи", "type": "тип", "channelId": "ID канала", "userId": "Пользовательский ID", "taskId": "Идентификатор задачи", "submit": "Отправить", "status": "состояние", "progress": "Прогресс", "duration": "время затраты", "result": "результат", "prompt": "Подсказка", "promptEn": "ПромптЭн", "failReason": "Причины неудачи", "startTime": "Время начала", "endTime": "Время окончания", "today": "в тот день", "lastHour": "1 час", "last3Hours": "3 часа", "lastDay": "1 день", "last3Days": "3 дня", "last7Days": "7 дней", "lastMonth": "1 месяц", "last3Months": "3 месяца", "selectTaskType": "Выберите тип задачи", "selectSubmitStatus": "Выбор состояния подачи", "submitSuccess": "Успешная подача", "queueing": "Стоит в очереди.", "duplicateSubmit": "Повторная отправка", "selectTaskStatus": "Выберите статус задачи", "success": "Успех", "waiting": "ожидание", "queued": "в очереди", "executing": "исполнение", "failed": "неудача", "seconds": "секунда", "unknown": "Неизвестно", "viewImage": "Нажмите, чтобы посмотреть", "markdownFormat": "Формат Markdown", "midjourneyTaskId": "ID задачи Midjourney", "copiedAsMarkdown": "Скопировано в формате Markdown", "copyFailed": "Копирование не удалось.", "copiedMidjourneyTaskId": "Копия ID задачи Midjourney.", "drawingLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON> рисования", "onlyUnarchived": "Только статистика неархивированных журналов", "imagePreview": "Предварительный просмотр изображения", "copiedImageUrl": "Адрес изображения скопирован.", "copy": "Копировать", "download": "Скачать", "resultImage": "Результат изображения", "downloadError": "Не удалось загрузить изображение.", "mode": "модель", "selectMode": "Выбор режима", "relax": "Легкий режим", "fast": "Быстрый режим", "turbo": "Режим высокой скорости", "actions": "операция", "refresh": "Обновить", "refreshSuccess": "Статус задачи успешно обновлён.", "refreshFailed": "Ошибка обновления статуса задачи", "refreshError": "Произошла ошибка при обновлении статуса задачи.", "tasks": {"title": "Список задач", "taskId": "ID задачи", "platform": "Платформа", "type": "Тип", "status": "Статус", "progress": "Прогресс", "submitTime": "Время отправки", "startTime": "Время начала", "endTime": "Время окончания", "duration": "Продолжительность", "result": "Результат", "taskIdPlaceholder": "Введите ID задачи", "platformPlaceholder": "Выберите платформу", "typePlaceholder": "Выберите тип", "statusPlaceholder": "Выберите статус", "videoGeneration": "Генерация видео", "imageGeneration": "Генерация изображения", "musicGeneration": "Генерация музыки", "textGeneration": "Генерация текста", "unknown": "Неизвестно", "success": "Успех", "failed": "Ошибка", "inProgress": "В процессе", "submitted": "Отправлено", "queued": "В очереди", "notStarted": "Не начато", "viewResult": "Посмотреть результат", "viewError": "Посмотреть ошибку", "taskDetails": "Детали задачи", "errorDetails": "Детали ошибки", "loadError": "Ошибка загрузки списка задач"}, "viewVideo": "查看视频", "videoPreview": "Text", "copyVideoUrl": "Text", "copiedVideoUrl": "Text", "downloadVideo": "下载视频", "videoNotSupported": "Text", "videoUrl": "视频地址", "videoUrls": "Text"}, "mjTaskType": {"IMAGINE": "Создание изображения", "UPSCALE": "Увеличить", "VARIATION": "преобразование", "REROLL": "Перегенерировать", "DESCRIBE": "Изображение порождает текст.", "BLEND": "смешанная графика", "OUTPAINT": "<PERSON>ум", "DEFAULT": "Неизвестно"}, "mjCode": {"submitSuccess": "Успешная подача", "queueing": "Стоит в очереди.", "duplicateSubmit": "Повторная отправка", "unknown": "Неизвестно"}, "mjStatus": {"success": "Успех", "waiting": "ожидание", "queued": "в очереди", "executing": "исполнение", "failed": "неудача", "unknown": "Неизвестно"}, "tokensTable": {"title": "Управление токенами", "table": {"title": "Управление токенами", "toolBar": {"add": "Создать токен", "delete": "Удалить токен", "deleteConfirm": "Action", "export": "Экспортировать", "exportConfirm": "Экспортировать токен текущей страницы?"}, "action": "операция"}, "modal": {"title": {"add": "Создать токен", "edit": "Редактировать токен"}, "field": {"name": "Название токена", "description": "Описание токена", "type": {"default": "Способ расчета оплаты", "type1": "Постоплатная тарификация", "type2": "Постоплатный расчет", "type3": "Смешанная тарификация", "type4": "Приоритет по объему", "type5": "по второстепенному приоритету"}, "status": "состояние", "statusEnabled": "нормально", "statusDisabled": "Запретить", "statusExpired": "истекший", "statusExhausted": "истощение", "models": "Доступные модели", "usedQuota": "лимит расхода", "remainQuota": "Остаток лимита", "createdTime": "Дата создания", "expiredTime": "срок годности", "all": "всё", "more": "больше", "notEnabled": "Неактивировано", "unlimited": "Без ограничений", "daysLeft": "Истекает через {{days}} дней.", "expired": "Просрочено {{days}} дней", "userId": "Пользовательский ID", "key": "API-к<PERSON><PERSON><PERSON>", "neverExpire": "Никогда не истекает"}, "delete": {"title": "Удалить", "content": "Вы уверены, что хотите удалить API-ключ {{name}}?"}, "footer": {"cancel": "Отмена", "confirm": "Подтверждение", "update": "Обновление"}, "bridge": {"title": "Быстрое подключение каналов", "placeholder": "Пожалуйста, введите адрес службы {{name}}."}, "copy": {"title": "Ручное копирование"}}, "dropdown": {"onlineChat": "<PERSON>н<PERSON><PERSON><PERSON>н-диалог", "disableToken": "Запретить токен", "enableToken": "Активировать токен", "editToken": "Редактировать токен", "requestExample": "Пример запроса", "tokenLog": "<PERSON><PERSON><PERSON><PERSON><PERSON> токенов", "shareToken": "Токен для совместного использования", "quickIntegration": "Однокнопочное подключение"}, "error": {"fetchModelsFailed": "Не удалось получить модель: {{message}}", "batchDeleteFailed": "Ошибка массового удаления: {{message}}", "deleteTokenFailed": "Не удалось удалить токен: {{message}}", "refreshTokenFailed": "Не удалось обновить токен: {{message}}", "enableTokenFailed": "Не удалось активировать токен: {{message}}", "disableTokenFailed": "Не удалось отключить токен: {{message}}", "fetchDataFailed": "Не удалось получить данные: {{message}}"}, "success": {"batchDelete": "Успешно удалено {{count}} токенов.", "shareTextCopied": "Success", "tokenCopied": "Токен скопирован в буфер обмена.", "deleteToken": "Токен успешно удалён.", "refreshToken": "Успешное обновление токена.", "enableToken": "Активация токена прошла успешно.", "disableToken": "Токен успешно отключен.", "export": "Экспорт токена текущей страницы успешен."}, "warning": {"copyFailed": "Копирование не удалось, пожалуйста, скопируйте вручную.", "invalidServerAddress": "Пожалуйста, введите правильный адрес сервера."}, "info": {"openingBridgePage": "Открывается страница подключения, токен скопирован для вас."}, "export": {"name": "Название", "key": "<PERSON><PERSON><PERSON><PERSON>", "billingType": "Способ расчета оплаты", "status": "состояние", "models": "Доступные модели", "usedQuota": "лимит расхода", "remainQuota": "Остаток лимита", "createdTime": "Дата создания", "expiredTime": "срок годности", "unlimited": "Без ограничений", "neverExpire": "Никогда не истекает"}, "billingType": {"1": "Постоплатная тарификация", "2": "Постоплатный расчет", "3": "Смешанная тарификация", "4": "Приоритет по объему", "5": "по второстепенному приоритету"}, "bridge": {"quickIntegration": "Однонажатное подключение"}}, "editTokenModal": {"editTitle": "Редактировать токен", "createTitle": "Создание токена", "defaultTokenName": "Токен {{username}} {{date}}", "tokenName": "Название токена", "unlimitedQuota": "безлимитный лимит", "remainingQuota": "Остаток лимита", "authorizedQuota": "<PERSON>и<PERSON>ит авторизации", "quotaLimitNote": "Максимально доступный лимит токена ограничен балансом счета.", "quickOptions": "Быстрые опции", "neverExpire": "Никогда не истекает", "expiryTime": "срок годности", "billingMode": "Модель выставления счетов", "selectGroup": "Выбор группы", "switchGroup": "Выбор группы", "switchGroupTooltip": "Выберите группу, к которой относится токен; разные группы имеют разные цены и функциональные права. Если не выбрать, будет использоваться группа, к которой принадлежит текущий пользователь по умолчанию.", "switchGroupHint": "Выбор группы повлияет на коэффициент тарификации токенов и доступные модели, пожалуйста, выберите в соответствии с реальными потребностями.", "importantFeature": "важный", "tokenRemark": "Заметка о токене", "discordProxy": "Дискорд прокси", "enableAdvancedOptions": "Включить расширенные параметры", "generationAmount": "Количество продукции", "availableModels": "Доступные модели", "selectModels": "Выберите/поиск/добавьте доступные модели, оставьте пустым для неограниченного доступа.", "activateOnFirstUse": "Первичное использование активации", "activateOnFirstUseTooltip": "При активации срока действия после первого использования, если этот параметр включен и активация выполнена при первом использовании, срок действия токена, настроенный выше, будет заменен.", "activationValidPeriod": "Срок действия активации", "activationValidPeriodTooltip": "Срок действия токена после первого использования (в днях)", "ipWhitelist": "IP белый список", "ipWhitelistPlaceholder": "IP-адреса (диапазоны), поддерживающие IPV4 и IPV6, разделенные запятыми.", "rateLimiter": "ограничитель тока", "rateLimitPeriod": "период ограничения потока", "rateLimitPeriodTooltip": "Период ограничения потока (единица: секунды)", "rateLimitCount": "лимит количества запросов", "rateLimitCountTooltip": "Количество доступных раз в период ограничения потока", "promptMessage": "Message", "promptMessageTooltip": "Сообщение об ограничении потока превышено.", "promotionPosition": "Рекламное место", "promotionPositionStart": "Начало", "promotionPositionEnd": "конец", "promotionPositionRandom": "случайный", "promotionContent": "Рекламный контент", "currentGroup": "Текущая группа", "searchGroupPlaceholder": "Поиск названия группы, описания или множителя...", "mjTranslateConfig": "MJ перевод конфигурации", "mjTranslateConfigTip": "Только перевод конфигурации, действующей для подсказок Midjourney.", "mjTranslateBaseUrlPlaceholder": "Пожалуйста, введите базовый URL для службы перевода.", "mjTranslateApiKeyPlaceholder": "Пожалуйста, введите API-ключ для услуги перевода.", "mjTranslateModelPlaceholder": "Пожалуйста, введите название модели, используемой для переводческих услуг.", "mjTranslateBaseUrlRequired": "При включении перевода необходимо предоставить базовый URL.", "mjTranslateApiKeyRequired": "Для активации перевода необходимо предоставить ключ API.", "mjTranslateModelRequired": "При включении перевода необходимо указать название модели."}, "addTokenQuotaModal": {"title": "Управление балансом токенов {{username}}", "defaultReason": "Операции администратора", "enterRechargeAmount": "Пожалуйста, введите сумму пополнения.", "enterRemark": "Пожалуйста, введите сообщение для примечания.", "confirmOperation": "Подтвердить действие", "confirmContent": "Подтвердить {{username}}{{action}}{{amount}} долларов США {{updateExpiry}}?", "recharge": "пополнение", "deduct": "вычеты", "andUpdateExpiry": "и обновить срок действия баланса на {{days}} дней", "alertMessage": "Ввод отрицательного числа может уменьшить баланс пользователя.", "rechargeAmount": "Сумма пополнения", "operationReason": "Причина операции", "finalBalance": "Окончательный баланс"}, "billingType": {"1": "Постоплатная тарификация", "2": "Постоплатный расчет", "3": "Смешанная тарификация", "4": "Приоритет по объему", "5": "по второстепенному приоритету", "payAsYouGo": "Постоплатная тарификация", "payPerRequest": "Постоплатный расчет", "hybrid": "Смешанная тарификация", "payAsYouGoPriority": "Приоритет по объему", "payPerRequestPriority": "по второстепенному приоритету", "unknown": "Неизвестный способ"}, "packagePlanAdmin": {"title": "комплексное предложение", "table": {"title": "Управление пакетами", "toolBar": {"add": "Создать новый пакет", "delete": "Удалить пакет"}, "action": {"edit": "Редактировать", "delete": "Удалить", "detail": "подробности", "recovery": "выставить на продажу", "offline": "Снять с продажи"}}, "modal": {"title": {"add": "Создать новый пакет", "edit": "Редактировать пакет"}, "field": {"name": "Название пакета", "type": {"default": "Тип набора", "type1": "Пакет лимитов", "type2": "пакет по количеству посещений", "type3": "Пакет по времени"}, "group": "Text", "description": "Описание набора", "price": "Цены на наборы", "valid_period": "Срок действия", "first_buy_discount": "Скидка для первых покупателей", "rate_limit_num": "ограничение количества раз", "rate_limit_duration": "ограниченный период", "inventory": "Наличие наборов", "available_models": "Доступные модели", "quota": "Пакетный лимит", "times": "Количество наборов"}, "footer": {"cancel": "Отмена", "confirm": "Подтверждение", "update": "Обновление"}}}, "login": {"title": "вход", "username": "имя пользователя", "password": "пароль", "login": "вход", "otherLoginMethods": "Другие способы входа", "register": "Зарегистрировать аккаунт", "accountLogin": "Вход в аккаунт", "phoneLogin": "Вход по номеру телефона", "usernamePlaceholder": "имя пользователя", "usernameRequired": "Пожалуйста, введите имя пользователя!", "passwordPlaceholder": "пароль", "passwordRequired": "Пожалуйста, введите пароль!", "passwordMaxLength": "Длина пароля не может превышать 20 символов!", "phonePlaceholder": "номер телефона", "phoneRequired": "Пожалуйста, введите номер телефона!", "phoneFormatError": "Неверный формат номера телефона!", "smsCodePlaceholder": "СМС-код подтверждения", "smsCodeCountdown": "Повторно получить через {{count}} секунд.", "getSmsCode": "Получить код подтверждения", "agreementText": "Я согласен.", "privacyPolicy": "«Политика конфиденциальности»", "and": "и", "serviceAgreement": "《Соглашение о предоставлении услуг》", "alreadyLoggedIn": "Вы вошли в систему.", "weakPasswordWarning": "Ваш пароль слишком простой, пожалуйста, измените его вовремя!", "welcomeMessage": "Добро пожаловать!", "captchaError": "Неверный код подтверждения.", "credentialsError": "Неправильное имя пользователя или пароль.", "resetPassword": "Сбросить пароль", "captchaExpired": "Код подтверждения не существует или истек.", "loginFailed": "Ошибка входа: {{message}}", "captchaRequired": "Пожалуйста, введите код подтверждения!", "captchaPlaceholder": "Код подтверждения", "smsSent": "Код подтверждения отправлен успешно.", "smsSendFailed": "Не удалось отправить SMS-код подтверждения.", "agreementWarning": "Пожалуйста, сначала согласитесь с «Политикой конфиденциальности» и «Соглашением об услугах».", "turnstileWarning": "Пожалуйста, попробуйте еще раз, Turnstile проверяет среду пользователя!", "loginSuccess": "Успешный вход в систему"}, "register": {"title": "Регистрация", "usernameRequired": "Пожалуйста, введите имя пользователя!", "usernameNoAt": "Имя пользователя не может содержать символ @.", "usernameNoChinese": "Имя пользователя не может содержать китайские символы.", "usernameLength": "Длина имени пользователя должна составлять от 4 до 12 символов.", "usernamePlaceholder": "имя пользователя", "passwordRequired": "Пожалуйста, введите пароль!", "passwordLength": "Длина пароля должна составлять от 8 до 20 символов.", "passwordPlaceholder": "пароль", "confirmPasswordRequired": "Пожалуйста, подтвердите пароль!", "passwordMismatch": "Пароли, введенные дважды, не совпадают!", "confirmPasswordPlaceholder": "Подтвердите пароль", "emailInvalid": "Пожалуйста, введите действительный адрес электронной почты!", "emailRequired": "Пожалуйста, введите адрес электронной почты!", "emailPlaceholder": "Электронный адрес", "emailCodeRequired": "Пожалуйста, введите код подтверждения электронной почты!", "emailCodePlaceholder": "Код подтверждения электронной почты", "enterCaptcha": "Пожалуйста, введите код подтверждения.", "resendEmailCode": "Повторная отправка через {{seconds}} секунд.", "getEmailCode": "Получить код подтверждения", "phoneRequired": "Пожалуйста, введите номер телефона!", "phoneInvalid": "Неправильный формат номера телефона!", "phonePlaceholder": "номер телефона", "smsCodeRequired": "Пожалуйста, введите SMS-код подтверждения!", "smsCodePlaceholder": "СМС-код подтверждения", "resendSmsCode": "Повторная отправка через {{seconds}} секунд.", "getSmsCode": "Получить код подтверждения", "captchaRequired": "Пожалуйста, введите код подтверждения!", "captchaPlaceholder": "Код подтверждения", "inviteCodePlaceholder": "Пригласительный код (по желанию)", "submit": "Регистрация", "successMessage": "Регистрация прошла успешно.", "failMessage": "Регистрация не удалась.", "emailCodeSent": "Код подтверждения электронной почты был отправлен.", "smsCodeSent": "СМС-код подтверждения отправлен.", "confirm": "Подтверждение", "emailVerifyTitle": "Проверка электронной почты", "smsVerifyTitle": "СМС-подтверждение", "registerVerifyTitle": "Регистрация подтверждения"}, "profile": {"timezone": "Часовой пояс", "phoneNumber": "номер телефона", "emailAddress": "Электронный адрес", "wechatAccount": "Учетная запись WeChat", "telegramAccount": "Аккаунт Telegram", "bindTelegram": "Привязать Telegram", "balanceValidPeriod": "Срок действия остатка", "lastLoginIP": "Последний IP-адрес входа", "lastLoginTime": "Время последнего входа", "inviteCode": "Пригласительный код", "inviteLink": "Пригласительная ссылка", "generate": "генерация", "pendingEarnings": "Ожидаемые доходы", "transfer": "перевод", "totalEarnings": "Общий доход", "accountBalance": "Ба<PERSON><PERSON><PERSON><PERSON> счета", "totalConsumption": "накопленные расходы", "callCount": "Количество вызовов", "invitedUsers": "Пригласить пользователя", "promotionInfo": "Рекламная информация", "inviteDescription": "Один раз пригласил, пожизненная скидка, чем больше приглашений, тем больше скидка.", "userInfo": "Информация о пользователе", "availableModels": "Доступные модели", "modelNameCopied": "Модель имени скопирована", "noAvailableModels": "Нет доступных моделей.", "accountOptions": "Настройки аккаунта", "changePassword": "Изменить пароль", "systemToken": "Системный токен", "unsubscribe": "аннулировать", "educationCertification": "Образовательная аккредитация", "timezoneUpdateSuccess": "Обновление часового пояса прошло успешно.", "inviteLinkCopied": "Ссылка для приглашения скопирована.", "inviteLinkCopyFailed": "Не удалось скопировать ссылку для приглашения.", "inviteLinkGenerationFailed": "Ошибка при создании ссылки для приглашения.", "allModelsCopied": "Все модели скопированы в буфер обмена.", "copyAllModels": "Скопировать все модели", "totalModels": "Количество доступных моделей", "expired": "Истек срок действия", "validPeriod": "Срок действия", "longTermValid": "Долгосрочная эффективность", "failedToLoadModels": "Не удалось загрузить список моделей.", "accessTokens": "Text", "accessTokensManagement": "Text", "accessTokenDescription": "Text", "tokenNameLabel": "Title", "tokenNamePlaceholder": "Title", "presetPermissions": "预设权限", "detailPermissions": "Text", "validityPeriod": "Text", "validityPeriodExtra": "Text", "remarkLabel": "备注", "remarkPlaceholder": "Please enter...", "createNewToken": "Text", "tokenCreatedSuccess": "Success", "tokenSavePrompt": "Text", "copyToken": "复制令牌", "readPermission": "Text", "writePermission": "写入权限", "deletePermission": "Text", "tokenManagement": "令牌管理", "channelManagement": "Text", "logView": "日志查看", "statisticsView": "Text", "userManagement": "用户管理", "quotaManagement": "Text", "readOnlyPermission": "只读权限", "writeOnlyPermission": "Text", "readWritePermission": "读写权限", "standardPermission": "Text", "fullPermission": "完全权限", "selectPermission": "Option", "tokenStatus": "状态", "tokenEnabled": "Text", "tokenDisabled": "禁用", "enableToken": "Text", "disableToken": "禁用", "deleteToken": "Text", "deleteTokenConfirm": "Action", "disableTokenConfirm": "Action", "enableTokenConfirm": "Text", "tokenExpiryNever": "Text", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Description", "noPermission": "无权进行此操作"}, "topup": {"onlineRecharge": "Онлайн пополнение", "cardRedemption": "Код обмена активации", "accountBalance": "Ба<PERSON><PERSON><PERSON><PERSON> счета", "rechargeReminder": "Text", "reminder1": "1. Баланс можно использовать для вызовов модели, покупки пакетов и т.д.", "reminder2": "Если после оплаты сумма не поступила, пожалуйста, свяжитесь с нашей службой поддержки.", "reminder3": "3. Баланс не поддерживает вывод средств, но может быть переведен внутри одной группы пользователей.", "reminder4WithTransfer": "После успешного пополнения счета срок действия баланса будет сброшен на", "reminder4WithoutTransfer": "После успешного пополнения счета срок действия баланса будет сброшен на", "days": "небо", "paymentSuccess": "Оплата прошла успешно.", "paymentError": "Ошибка при оплате", "paymentAmount": "Сумма платежа:", "purchaseAmount": "Сумма покупки: $", "yuan": "юань", "or": "или", "usd": "доллар США", "cny": "юань", "enterAmount": "Пожалуйста, введите сумму пополнения!", "amountPlaceholder": "Пожалуйста, введите сумму пополнения, от {{min}} долларов США.", "amountUpdateError": "Ошибка при обновлении суммы", "alipay": "Text", "wechat": "微信", "visaMastercard": "Виза / Мастеркард", "cardFormatError": "Неверный формат кода обмена.", "redeemSuccess": "{{amount}} успешно обменян!", "redeemError": "Ошибка обмена, пожалуйста, попробуйте позже.", "enterCardKey": "Пожалуйста, введите код обмена.", "cardKeyPlaceholder": "Пожалуйста, введите код обмена.", "buyCardKey": "Купить код для обмена", "redeem": "Немедленно аннулировать", "record": {"title": "Запись о пополнении счета", "amount": "Сумма пополнения", "payment": "Сумма платежа", "paymentMethod": "Способ оплаты", "orderNo": "номер заказа", "status": "состояние", "createTime": "Дата создания", "statusSuccess": "Успех", "statusPending": "В процессе обработки", "statusFailed": "неудача"}, "paymentMethodAlipay": "Text", "paymentMethodWxpay": "微信", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentMethodPaypal": "Пей<PERSON>ал", "paymentMethodAdmin": "Администратор", "paymentMethodRedeem": "Код для обмена", "alipayF2F": "Text"}, "pricing": {"fetchErrorMessage": "Произошла ошибка при получении информации о цене, пожалуйста, свяжитесь с администратором.", "availableModelErrorMessage": "Произошла ошибка при получении доступной модели, пожалуйста, свяжитесь с администратором.", "modelName": "Название модели", "billingType": "Тип расчета", "price": "Цена", "ratio": "Увеличение", "promptPriceSame": "Цена предложения: такая же, как и исходная ставка.", "completionPriceSame": "Дополнительная цена: такая же, как и исходная ставка.", "promptPrice": "Цена за подсказку: $ {{price}} / 1M токенов", "completionPrice": "Дополнительная цена: $ {{price}} / 1M токенов", "promptRatioSame": "Коэффициент подсказки: такой же, как и исходный коэффициент.", "completionRatioSame": "Дополнительный множитель: такой же, как и исходный множитель.", "promptRatio": "Коэффициент подсказки: {{ratio}}", "completionRatio": "Заполните коэффициент: {{ratio}}", "payAsYouGo": "Оплата по мере использования - Чат", "fixedPrice": "$ {{price}} / раз", "payPerRequest": "Платежи по мере использования - Чат", "dynamicPrice": "$ {{price}} / раз", "payPerRequestAPI": "Оплата по мере использования - API", "loadingTip": "Получение информации о ценах...", "userGroupRatio": "Ваш коэффициент группировки пользователей составляет: {{ratio}}", "readFailed": "Ошибка чтения", "billingFormula": "Плата за оплату по объему = коэффициент конверсии × множитель группы × множитель модели × (число токенов подсказки + число токенов дополнения × множитель дополнения) / 500000 (единица: доллар США)", "billingFormula1": "Коэффициент конверсии = (новый коэффициент пополнения / исходный коэффициент пополнения) × (новый коэффициент группировки / исходный коэффициент группировки)", "generatedBy": "Эта страница была автоматически сгенерирована {{systemName}}.", "modalTitle": "Детали цены", "perMillionTokens": "/1M токенов", "close": "Закрыть", "searchPlaceholder": "Поиск названия модели", "viewGroups": "Просмотреть группы", "copiedToClipboard": "Скопировано в буфер обмена.", "copyFailed": "Копирование не удалось.", "groupName": "Название группы", "availableGroups": "Доступные группы для модели {{model}}", "noGroupsAvailable": "Нет доступных групп.", "modelGroupsErrorMessage": "Не удалось получить данные группировки модели.", "currentGroup": "Текущая группа", "copyModelName": "Скопировать название модели", "groupRatio": "Групповое соотношение", "closeModal": "Закрыть", "groupsForModel": "Модель доступна для группировки.", "actions": "операция", "filterByGroup": "Фильтрация по группам", "groupSwitched": "Переключено на группу: {{group}}", "showAdjustedPrice": "Отображение цены после корректировки группировки (текущий коэффициент: {{ratio}})"}, "guestQuery": {"usageTime": "использование времени", "modelName": "Название модели", "promptTooltip": "Ввод потребляет токены", "completionTooltip": "Вывод потребляемых токенов", "quotaConsumed": "лимит расхода", "pasteConfirm": "Обнаружен действительный токен в буфере обмена, хотите вставить?", "queryFailed": "Запрос не удался.", "tokenExpired": "Этот токен истек.", "tokenExhausted": "Квота этого токена исчерпана.", "invalidToken": "Пожалуйста, введите правильный токен.", "focusRequired": "Пожалуйста, убедитесь, что страница находится в фокусе.", "queryFirst": "Пожалуйста, сначала проверьте.", "tokenInfoText": "Общая сумма токенов: {{totalQuota}}  \nПотребление токенов: {{usedQuota}}  \nОстаток токенов: {{remainQuota}}  \nКоличество вызовов: {{callCount}}  \nСрок действия до: {{validUntil}}", "unlimited": "Без ограничений", "neverExpire": "Никогда не истекает", "infoCopied": "Информация о токене скопирована в буфер обмена.", "copyFailed": "Копирование не удалось.", "noDataToExport": "Нет данных для экспорта.", "prompt": "Подсказка", "completion": "дополнить", "disabled": "Запросы посетителей не включены.", "tokenQuery": "Запрос токена", "tokenPlaceholder": "Пожалуйста, введите токен для запроса (sk-xxx)", "tokenInfo": "Информация о токене", "copyInfo": "Копировать информацию", "totalQuota": "Общая сумма токенов", "usedQuota": "Расход токенов", "remainQuota": "Баланс токена", "callCount": "Количество вызовов", "validUntil": "Срок действия до", "currentRPM": "Текущий RPM", "currentTPM": "Текущий TPM", "callLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON> вызовов", "exportLogs": "Экспортирова<PERSON><PERSON> журнал"}, "agencyProfile": {"fetchError": "Error", "fetchCommissionError": "Не удалось получить список комиссий.", "systemPreset": "Системные настройки", "lowerRatioWarning": "Ставка ниже установленной системой.", "lowerRatioMessage": "Следующие тарифы ниже установленного системой значения, пожалуйста, своевременно измените их:", "cancelRatioEdit": "Отмена изменения тарифов", "updateSuccess": "Обновление успешно выполнено.", "updateError": "Error", "updateFailed": "Обновление не удалось:", "customPriceUpdateSuccess": "Настройка цены обновлена успешно.", "customPriceUpdateError": "Не удалось обновить настраиваемую цену:", "time": "время", "type": "тип", "agencyCommission": "Комиссия агенту", "unknownType": "Неизвестный тип", "amount": "сумма", "balance": "бал<PERSON><PERSON><PERSON>", "description": "Описание", "group": "Группировка", "customRate": "Пользовательская ставка", "systemDefaultRate": "Системная ставка по умолчанию", "action": "операция", "save": "Сохранить", "cancel": "Отмена", "edit": "Редактировать", "agencyConsole": "Консоль агента", "agencyInfo": "Информация о дистрибьюторах", "editInfo": "Редактировать информацию", "agencyName": "Название агента", "agencyLevel": "Уровень агента", "level1": "Уровень 1", "subordinateUsers": "нижний пользователь", "totalSales": "Общий объем продаж", "commissionIncome": "Комиссионный доход", "cumulativeEarnings": "накопленная прибыль", "agencyFunctions": "Text", "hideSubordinateUsers": "Скрыть подчиненных пользователей", "viewSubordinateUsers": "Просмотреть подчиненных пользователей", "hideCommissionDetails": "Скрыть детали комиссии", "viewCommissionDetails": "Просмотреть детали комиссии", "hideCustomPrice": "Скрыть пользовательскую цену", "setCustomPrice": "Установить пользовательскую цену", "subordinateUsersList": "Список подчиненных пользователей", "commissionRecords": "Запись комиссии", "customPriceSettings": "Настройка пользовательской цены", "saveChanges": "Сохранить изменения", "editAgencyInfo": "Редактировать информацию о агенте.", "logo": "Лого<PERSON>ип", "setAgencyLogo": "Text", "customHomepage": "Настраиваемая главная страница", "aboutContent": "О содержании", "newHomepageConfig": "Новая конфигурация главной страницы", "customAnnouncement": "Пользовательское уведомление", "customRechargeGroupRateJson": "Настройка тарифов групп пополнения в формате JSON", "customRechargeRate": "Настройка тарифов на пополнение счёта", "viewSystemDefaultRate": "Просмотреть системные тарифы по умолчанию.", "rateComparison": "Сравнение тарифов", "comparisonResult": "Результаты сравнения", "higherThanSystem": "выше системы", "lowerThanSystem": "ниже системы", "equalToSystem": "равно системе", "unknown": "Неизвестно", "notAnAgentYet": "Вы еще не являетесь агентом.", "becomeAnAgent": "Стать агентом", "startYourOnlineBusiness": "🌟 Легко начните свой онлайн-бизнес", "becomeOurAgent": "Станьте нашим агентом и наслаждайтесь беззаботным опытом предпринимательства:", "noInventory": "💼 Нет необходимости в запасах, ноль давления по обороту капитала.", "instantCommission": "💰 Мгновенное распределение прибыли от продаж, получение значительных вознаграждений пропорционально.", "easyManagement": "🖥️ Не требуется знание технологий для создания сайтов, легко управляйте своим интернет-магазином.", "flexibleDomainChoice": "🌐 Гибкий выбор доменных имен", "youCan": "Вы можете:", "useOwnDomain": "🏠 Используйте свой собственный домен", "orUseOurSubdomain": "🎁 Или мы можем предоставить вам эксклюзивный поддомен.", "convenientStart": "Text", "actNow": "🚀 Действуйте немедленно!", "contactAdmin": "Свяжитесь с администратором сайта, чтобы начать ваше путешествие в качестве агента! 📞", "applyNow": "Подать заявку немедленно", "contactCooperation": "Консультационное сотрудничество", "understandPolicy": "Изучите политику агентов и детали сотрудничества.", "provideDomain": "Предоставить доменное имя", "configDomain": "Предоставьте ваш домен, и мы поможем вам с настройкой.", "promoteAndEarn": "Продвижение и прибыль", "startPromoting": "Начните продвигать свой агентский сайт и зарабатывайте комиссию.", "noDeploymentWorries": "Не беспокойтесь о сложных развертываниях облачных сервисов, платежных каналах и проблемах с запасами.", "easySetup": "Просто предоставьте доменное имя, настройте его согласно инструкции, и вы сможете легко начать бизнес по предоставлению API для предприятий.", "customizeContent": "Вы можете настроить цены, информацию о сайте, SEO, логотип и другие элементы.", "commissionBenefits": "В качестве агента вы получите долю от пополнений пользователей, система автоматически вычитает затраты, оставшуюся сумму можно вывести в любое время.", "joinNowBenefit": "Присоединяйтесь к нам сейчас и вместе пожинайте плоды эпохи ИИ!", "groups": {"student": "студент университета", "studentDesc": "У меня достаточно времени, и я надеюсь, что с помощью рекламных мероприятий смогу легко увеличить доход, чтобы покрыть часть расходов на жизнь и развлечения.", "partTime": "Подработка или дополнительная работа", "partTimeDesc": "Не требуется много времени, достаточно просто продвигать в свободное от работы время, чтобы легко зарабатывать дополнительный доход.", "mediaWorker": "Работник в сфере самопубликации", "mediaWorkerDesc": "Имея определенную базу подписчиков, достаточно просто прикрепить ссылку в конце статьи или поста, чтобы легко получить дополнительный доход.", "freelancer": "фрилан<PERSON><PERSON>р", "freelancerDesc": "Имея много свободного времени, можно легко увеличить дополнительный доход, участвуя только в продажах."}, "stories": {"story1": {"name": "Господи<PERSON>", "role": "студент университета"}, "story2": {"name": "Госпожа Ли", "role": "учитель средней школы"}, "story3": {"name": "Господин <PERSON>ю", "role": "электронная коммерция"}, "story4": {"name": "Господи<PERSON>н", "role": "самостоятельные медиа"}, "story5": {"name": "Господи<PERSON><PERSON>у", "role": "Научный работник"}, "story6": {"name": "госпожа Ван", "role": "Блогер на Xiaohongshu"}, "story7": {"name": "Госпож<PERSON> Хуан", "role": "самостоятельные медиа"}, "story8": {"name": "Господин <PERSON>ю", "role": "IT-отрасль"}}, "earnedAmount": "Заработано {{amount}}", "applyForAgentNow": "Немедленно подайте заявку на получение статуса агента.", "businessLinesConnected": "Подключено более 40 бизнес-линий.", "agencyJoin": "Text", "becomeExclusiveAgent": "Станьте нашим эксклюзивным агентом.", "startBusinessJourney": "Легко начните ваше бизнес-путешествие~", "welcomeToAgencyPage": "Добро пожаловать на нашу страницу агентов!", "earningsTitle": "Более ста человек уже заработали более 3000 юаней.", "becomeAgentSteps": "Степени становления агентом", "agencyRules": "Правила представительства", "suitableGroups": "Подходящая аудитория", "agencyImages": {"becomeAgent": "Стать агентом", "agencyBusiness": "Text"}, "rules": {"howToEstablishRelation": "Как пользователю установить со мной агентские отношения?", "howToEstablishRelationAnswer": "Зарегистрируйтесь на вашем агентском сайте, чтобы стать вашим пользователем.", "canSetPrice": "Могу ли я установить цену?", "canSetPriceAnswer": "Можно! Но ваша цена продажи должна быть выше закупочной цены на 10%.", "commissionShare": "Сколько я могу получить процентов?", "commissionShareAnswer": {"assumption": "Предположим: ваша закупочная цена составляет $1=1 юань, ваша продажная цена составляет $1=2 юаня, а ваша комиссия составляет 90%.", "example": "Пользователь покупает на вашем сайте за 10 долларов, тратит 20 юаней.", "calculation": "Вы можете получить: (2-1)*10*0.9 = 9 юаней", "explanation": "Интерпретация: (Цена продажи - Закупочная цена) * Объем продаж * Процент комиссии"}}}, "error": {"title": "Ошибка", "content": "Произошла ошибка."}, "loading": {"title": "Загрузка...", "content": "Загрузка..."}, "notfound": {"title": "404", "content": "Страница не найдена"}, "servererror": {"title": "500", "content": "Ошибка сервера"}, "unauthorized": {"title": "401", "content": "Неавторизованный"}, "forbidden": {"title": "403", "content": "Доступ запрещен"}, "networkerror": {"title": "Сетевая ошибка", "content": "Сетевая ошибка"}, "timeout": {"title": "Title", "content": "Таймаут запроса"}, "noresult": {"title": "Нет результатов.", "content": "Нет результатов"}, "nopermission": {"title": "Нет доступа", "content": "Нет доступа"}, "channelBridge": {"title": "Быстрое подключение каналов", "channelPlatform": "канальная платформа", "billingMethod": "Способ расчета оплаты", "channelName": "Название канала", "remark": "Примечание", "availableGroups": "Доступные группы", "availableModels": "Доступные модели", "channelKey": "<PERSON><PERSON><PERSON><PERSON> канала", "proxyAddress": "Адрес подключения", "cancel": "Отмена", "submit": "Отправить", "gpt35Models": "Модель GPT-3.5", "gpt4Models": "Модель GPT-4", "clear": "Очистить", "customModelName": "Пользовательское имя модели", "add": "Добавить", "moreConfigReminder": "Дополнительные настройки можно редактировать после сохранения канала.", "quickIntegration": "Однокнопочное подключение", "selectBillingMethod": "Пожалуйста, выберите способ оплаты.", "enterChannelName": "Пожалуйста, введите название канала.", "enterChannelRemark": "Пожалуйста, введите примечание к каналу.", "selectAvailableGroups": "Пожалуйста, выберите группу, которая может использовать этот канал.", "selectAvailableModels": "Выберите/поиск доступных моделей для этого канала.", "enterChannelKey": "Пожалуйста, введите ключ канала.", "proxyAddressPlaceholder": "Этот параметр является необязательным и используется для вызова API через прокси-сервер. Пожалуйста, введите адрес прокси-сервера.", "includes16kModels": "Содержит 16k модель.", "excludes32kModels": "Не включает модель 32k.", "cleared": "Очистить.", "addCustomModel": "Добавить пользовательскую модель", "clipboardTokenDetected": "Обнаружен действительный токен в буфере обмена, хотите вставить?", "channelIntegrationSuccess": "Канал успешно подключен!", "channelIntegrationFailed": "Ошибка подключения канала:"}, "about": {"loading": "Получите последние обновления...", "noContent": "Администратор не установил содержимое страницы \"О нас\".", "loadFailed": "Не удалось загрузить информацию о содержимом..."}, "onlineTopupRecord": {"title": "Запись о пополнении счета", "columns": {"id": "ИД", "username": "пользователь", "amount": "Сумма пополнения", "money": "Сумма платежа", "paymentMethod": "Способ оплаты", "tradeNo": "номер заказа", "status": "состояние", "createTime": "Дата создания"}, "status": {"success": "Успех", "pending": "В процессе обработки", "failed": "неудача"}, "paymentMethod": {"alipay": "Text", "wxpay": "微信", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paypal": "Пей<PERSON>ал"}}, "logContentDetail": {"description": "Описание информации", "downstreamError": "Ошибки на стороне потребителя", "originalError": "Исходная ошибка", "requestParams": "параметры запроса", "copy": "Копировать"}, "viewMode": {"switchTo": "Переключиться на перспективу {{mode}}", "cost": "стоимость", "usage": "Использование"}, "agenciesTable": {"title": "Управление агентами", "addAgency": "Новые агенты", "columns": {"id": "ИД", "userId": "Пользовательский ID", "name": "Название", "domain": "доменное имя", "commissionRate": "Комиссионный процент", "salesVolume": "Объем продаж", "userCount": "Количество пользователей", "commissionIncome": "Комиссионный доход", "historicalCommission": "накопленная прибыль", "actions": "операция"}, "confirm": {"deleteTitle": "Вы уверены, что хотите удалить этого агента?", "updateName": "Обновление названия агента...", "updateSuccess": "Обновление успешно выполнено.", "updateFailed": "Обновление не удалось.", "deleteSuccess": "Удаление прошло успешно!"}, "messages": {"getListFailed": "Не удалось получить список агентов: {{message}}", "deleteSuccess": "Удаление прошло успешно!", "loadingData": "Загрузка..."}}, "units": {"times": "Time", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} раз ({{percent}}%)"}, "dailyUsage": {"total": "Итого", "totalCost": "Общие затраты", "tooltipTitle": {"cost": "Состояние затрат", "usage": "Использование"}, "yAxisName": {"cost": "Стоимость (USD)", "usage": "Использование (USD)"}}, "dailyUsageByModel": {"total": "Итого", "tooltipTotal": "Итого: $ {{value}}", "switchTo": "переключиться на", "cost": "стоимость", "usage": "Использование", "perspective": "угол зрения", "granularity": {"hour": "по часам", "day": "по дням", "week": "по неделям", "month": "по месяцам"}}, "checkinModal": {"title": "Пожалуйста, завершите проверку.", "captchaPlaceholder": "Код подтверждения", "confirm": "определенно", "close": "Закрыть"}, "balanceTransfer": {"title": "Перевод между счетами", "accountInfo": {"balance": "Ба<PERSON><PERSON><PERSON><PERSON> счета", "transferFee": "Комиссия за перевод средств", "groupNote": "Переводы возможны только между одинаковыми группами пользователей."}, "form": {"receiverId": "ID получателя", "receiverUsername": "Имя пользователя получателя", "remark": "Примечания", "amount": "Сумма перевода", "expectedFee": "Ожидаемое списание средств", "submit": "Инициировать перевод"}, "result": {"success": "Перевод успешно выполнен.", "continueTransfer": "Продолжить перевод средств.", "viewRecord": "Просмотреть записи"}, "warning": {"disabled": "Функция перевода отключена администратором, временно недоступна."}, "placeholder": {"autoCalculate": "Автоматический расчет суммы перевода"}}, "channelsTable": {"title": "Управление каналами", "columns": {"id": "ИД", "name": "Название", "type": "тип", "key": "<PERSON><PERSON><PERSON><PERSON>", "base": "Адрес интерфейса", "models": "модель", "weight": "весовой коэффициент", "priority": "приоритет", "retryInterval": "Интервал повтора", "responseTime": "Время отклика", "rpm": "RPM", "status": "Статус", "quota": "бал<PERSON><PERSON><PERSON>", "expireTime": "срок годности", "group": "Группировка", "billingType": "Тип расчета", "actions": "операция", "fusing": "предохранитель", "sort": "приоритет", "createdTime": "Time", "disableReason": "禁用原因"}, "status": {"all": "всё", "normal": "нормально", "enabled": "нормальное состояние", "manualDisabled": "Ручное отключение", "waitingRetry": "Ожидание перезагрузки", "suspended": "Приостановить использование", "specified": "Установить статус", "allDisabled": "Запретить", "specifiedDisabled": "Укажите типы, которые нужно отключить.", "partiallyDisabled": "Status"}, "placeholder": {"selectGroup": "Выберите/поиск группы", "selectStatus": "Выберите состояние канала", "inputSelectModel": "Введите/выберите название модели", "selectFusingStatus": "Выберите состояние автоматического разрыва."}, "quota": {"usageAmount": "Расход: {amount}", "remainingAmount": "Осталось: {amount}", "customTotalAmount": "Индивидуальная сумма: {amount}", "updateNotSupported": "Временно не поддерживается обновление баланса, пожалуйста, используйте пользовательский баланс.", "details": "подробности", "sufficient": "достаточный"}, "actions": {"edit": "Редактировать", "copy": "Клонированный канал", "delete": "Удалить канал", "enable": "Включить", "disable": "Запретить", "test": "Тестирование", "advancedTest": "Высокий уровень тестирования", "viewLog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "viewAbility": "Просмотр возможностей", "cleanUsage": "Очистить использованное", "updateBalance": "Обновить баланс", "copyKey": "Скопировать ключ"}, "confirm": {"deleteTitle": "Подтверждение удаления", "deleteContent": "Вы уверены, что хотите удалить канал {{name}} (#{{id}})?", "cleanUsageTitle": "Подтверждение очистки использования", "cleanUsageContent": "Вы уверены, что хотите очистить сумму расходов канала {{name}} (#{{id}})?", "testTitle": "Подтверждение теста", "testContent": "Вы уверены, что хотите протестировать канал {{status}}?", "testNote": "Примечание: эта функция требует настройки в [Конфигурация] -> [Ретрансляция] -> [Настройки мониторинга] -> [Отключить канал при неудаче, включить канал при успехе]. Если соответствующие настройки не включены, каналы не будут автоматически отключены или включены после завершения тестирования.", "deleteDisabledTitle": "Подтверждение удаления", "deleteDisabledContent": "Вы уверены, что хотите удалить все каналы {{type}}?"}, "messages": {"operationSuccess": "Операция выполнена успешно.", "operationSuccessWithSort": "Операция успешна, порядок каналов может измениться, рекомендуется сортировать по ID!", "operationFailed": "Операция не удалась: {{message}}", "testRunning": "Канал {{name}}(#{{id}}) в процессе тестирования, пожалуйста, подождите...", "testSuccess": "<PERSON><PERSON><PERSON><PERSON> «{{name}}(#{{id}})» {{model}} тест прошел успешно, время отклика {{time}} секунд", "testFailed": "<PERSON><PERSON><PERSON><PERSON> «{{name}}(#{{id}})» {{model}} тестирование не прошло. Код состояния: {{code}}, причина: {{reason}}, нажмите для просмотра деталей.", "testStarted": "Начните тестирование канала {{status}}, пожалуйста, обновите страницу позже, чтобы увидеть результаты. Применение результатов теста зависит от ваших настроек мониторинга.", "testOperationFailed": "Тест не удался.", "deleteSuccess": "Успешно удалено {{count}} каналов", "deleteFailed": "Удаление не удалось: {{message}}", "modelPrefix": "модель {{model}}", "channelInfo": "Информация о каналах", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "Ба<PERSON><PERSON><PERSON><PERSON> канала «{{name}}» успешно обновлён.", "updateBalanceFailed": "Обновление баланса канала «{{name}}» не удалось: {{message}}", "updateAllBalanceStarted": "Начать обновление баланса всех каналов в нормальном состоянии.", "updateAllBalanceSuccess": "Все каналы успешно обновили баланс.", "fetchGroupError": "Ошибка получения данных группы каналов: {{response}}", "fetchChannelError": "Не удалось получить данные канала: {{message}}", "selectChannelFirst": "Пожалуйста, сначала выберите канал для удаления.", "deleteDisabledSuccess": "Удалены все каналы {{type}}, всего {{count}} штук.", "deleteOperationFailed": "Удаление не удалось.", "copySuccess": "Копирование успешно завершено.", "copyFailed": "Копирование не удалось: {{message}}", "emptyKey": "Ключ пустой", "testSuccessWithWarnings": "Message", "viewDetails": "Message", "fetchChannelDetailError": "Message", "topupSuccess": "充值成功", "topupFailed": "Message"}, "popover": {"channelInfo": "Информация о каналах"}, "menu": {"deleteManualDisabled": "Удалить вручную отключенный канал", "deleteWaitingRetry": "Удалить канал ожидания перезагрузки", "deleteSuspended": "Удалить приостановленные каналы использования", "testAll": "Тестирование всех каналов", "testNormal": "Тестирование нормального канала", "testManualDisabled": "Тестирование ручного отключения канала", "testWaitingRetry": "Тестирование ожидания перезапуска канала", "testSuspended": "Тестирование приостановлено для использования канала.", "deleteDisabledAccount": "Text", "deleteQuotaExceeded": "Text", "deleteRateLimitExceeded": "删除频率限制渠道", "deleteInvalidKey": "Text", "deleteConnectionError": "删除连接错误渠道"}, "tooltip": {"testNote": "Необходимо настроить [Конфигурация] -> [Ретрансляция] -> [Настройки мониторинга] -> [Отключить канал при неудаче, включить канал при успехе]. Если эта опция не включена, то каналы не будут автоматически отключаться или включаться после завершения тестирования скорости."}, "disableReasons": {"account_deactivated": "Text", "quota_exceeded": "配额超限", "rate_limit_exceeded": "Text", "invalid_key": "无效密钥", "connection_error": "Error"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "лимит", "times": "Time"}, "serverLogViewer": {"title": "Просмотрщик журналов сервера", "connecting": "Подключение к серверу...", "downloadSelect": "Выберите файл журнала для загрузки.", "nginxConfig": "Описание конфигурации WebSocket для Nginx", "directAccess": "Если вы используете доменное имя для доступа и не настроили поддержку WebSocket, просмотрщик журналов не будет работать. В этом случае вы можете получить доступ напрямую через IP-адрес сервера и порт (например: http://your-ip:9527).", "domainAccess": "Чтобы получить доступ через доменное имя, необходимо добавить следующую конфигурацию в Nginx для поддержки WebSocket:", "buttons": {"pause": "приостановить", "resume": "Продо<PERSON><PERSON>ай", "clear": "Очистить"}, "errors": {"fetchFailed": "Не удалось получить список файлов журналов.", "downloadFailed": "Не удалось загрузить файл журнала.", "wsError": "Ошибка подключения WebSocket"}}, "channelScore": {"score": "Очки", "successRate": "Уровень успеха", "avgResponseTime": "Среднее время отклика", "title": "Оценка канала", "hourlyTitle": "Часовой балл канала", "dailyTitle": "Оценка канала за день", "weeklyTitle": "Канал недельный балл", "monthlyTitle": "Оценка канала за месяц", "allTimeTitle": "Общая оценка канала", "infoTooltip": "Description", "tableView": "Табличный вид", "chartView": "Вид диаграммы", "refresh": "Обновить", "selectModel": "Выбор модели", "allModels": "Все модели", "sortByScore": "Сортировать по баллам", "sortBySuccessRate": "Сортировать по коэффициенту успеха", "sortByResponseTime": "Сортировать по времени отклика", "noData": "Нет данных", "totalItems": "Всего {{total}} элементов", "fetchError": "Не удалось получить данные о баллах канала.", "aboutScoring": "О расчете очков", "scoringExplanation": "Оценка канала рассчитывается на основе коэффициента успеха и времени отклика, максимальный балл составляет 1.", "successRateWeight": "Вес успеха (70%)", "successRateExplanation": "Чем выше вероятность успеха, тем выше оценка.", "responseTimeWeight": "Вес времени отклика (30%)", "responseTimeExplanation": "Время отклика менее 1000 мс получает максимальный балл, превышение этого времени приводит к пропорциональному снижению баллов.", "columns": {"rank": "<PERSON>ей<PERSON>инг", "channelId": "ID канала", "channelName": "Название канала", "model": "модель", "totalRequests": "Общее количество запросов", "successRequests": "Количество успешных запросов", "failedRequests": "Количество неудачных запросов", "successRate": "Уровень успеха", "avgResponseTime": "Среднее время отклика", "score": "Text", "actions": "операция"}, "actions": {"viewDetails": "Посмотреть детали", "test": "Тестовый канал", "edit": "Редактирование канала"}, "tooltips": {"excellent": "отличный", "good": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "average": "обычно", "poor": "плохой", "veryPoor": "очень плохо"}, "scoringExplanation100": "Оценка канала рассчитывается на основе коэффициента успешности и времени отклика, максимальный балл составляет 100."}, "menu": {"channelScores": "Оценка канала"}, "relay": {"dispatchOptions": "Опции планирования", "preciseWeightCalculation": "Точное вычисление веса", "preciseWeightCalculationTip": "После активации будет использоваться более точный алгоритм для расчета веса канала, что может увеличить нагрузку на процессор.", "channelMetricsEnabled": "Включить статистику по показателям каналов", "channelMetricsEnabledTip": "После включения будут собираться показатели, такие как успешность каналов и время отклика, для оценки производительности каналов. При отключении эти данные не будут собираться, что позволит снизить использование системных ресурсов.", "channelScoreRoutingEnabled": "Включить маршрутизацию на основе оценок каналов", "channelScoreRoutingEnabledTip": "После активации система автоматически будет настраивать приоритет распределения запросов на основе исторической производительности каналов, при этом каналы с лучшей производительностью получат более высокую вероятность распределения запросов.", "globalIgnoreBillingTypeFilteringEnabled": "Text", "globalIgnoreBillingTypeFilteringEnabledTip": "Text", "globalIgnoreFunctionCallFilteringEnabled": "Text", "globalIgnoreFunctionCallFilteringEnabledTip": "Text", "globalIgnoreImageSupportFilteringEnabled": "Text", "globalIgnoreImageSupportFilteringEnabledTip": "Text"}, "dynamicRouter": {"title": "Управление динамическими маршрутами", "reloadRoutes": "Перезагрузить маршрут", "exportConfig": "Экспорт конфигурации", "clearConfig": "Сбросить настройки", "importantNotice": "Важное уведомление", "reloadLimitation": "1. Перезагрузка маршрутов может только обновить конфигурацию существующих маршрутов, но не может добавить или удалить маршруты. Для полной перезагрузки структуры маршрутов перезапустите приложение.", "exportDescription": "2. Экспорт конфигурации позволит экспортировать настройки из текущей базы данных в файл router.json, исключая пустые и нулевые значения.", "clearDescription": "3. Очистка конфигурации удалит все динамические маршруты из базы данных, и после перезапуска приложения они будут загружены заново из файла router.json.", "routeGroups": "Маршрутная группа", "upstreamConfig": "Верхнее конфигурирование", "endpointConfig": "Конфигурация конечной точки", "editRouteGroup": "Редактировать группу маршрутов", "editUpstream": "Редактировать конфигурацию upstream", "editEndpoint": "Редактирование конфигурации конечной точки", "editJSON": "Редактировать JSON", "confirmClear": "Подтвердите очистку конфигурации.", "confirmClearMessage": "Эта операция очистит все динамические маршруты в базе данных, и при следующем перезапуске приложения они будут загружены заново из конфигурационного файла. Вы уверены, что хотите продолжить?", "configCleared": "Конфигурация динамического маршрута очищена, пожалуйста, перезапустите приложение, чтобы применить изменения.", "configExported": "Конфигурация успешно экспортирована в файл.", "configReloaded": "Конфигурация маршрутизатора успешно перезагружена."}, "notification": {"title": "Title", "subscriptionEvents": "订阅事件", "notificationMethods": "Message", "alertSettings": "预警设置", "emailConfig": "Message", "customEmails": "Message", "addEmail": "添加邮箱", "removeEmail": "Message", "emailPlaceholder": "Message", "emailTooltip": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Description", "alertExplanationTitle": "预警说明", "alertExplanation": "Message", "selectEvents": "Message", "eventsDescription": "Description", "selectMethods": "选择接收通知的方式", "methodsDescription": "Description", "description": "Description", "recommended": "Message", "important": "Message", "testRecommendation": "Message", "testNotification": "测试通知", "testMessage": "Message", "testSuccess": "测试通知发送成功", "testFailed": "Message", "saveSuccess": "通知设置保存成功", "saveFailed": "Message", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Пожалуйста, введите токен Telegram бота"}, "qywxbotConfig": "Настройка бота WeChat Enterprise", "qywxbotGuide": "Message", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkConfig": "Настройка бота DingTalk", "dingtalkGuide": "Message", "feishuConfig": "Настройка бота Feishu", "feishuGuide": "Message", "webhookConfig": "Webhook配置", "webhookGuide": "Message", "webhookUrl": "调用地址", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "Настройка бота Telegram", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "额度即将过期", "security_alert": "Message", "system_announcement": "系统公告", "promotional_activity": "Message", "model_pricing_update": "模型价格更新", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Message", "detailedDocumentation": "详细文档：", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "企业微信群机器人配置说明", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Посетите официальный сайт WxPusher для регистрации аккаунта", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "设置机器人名称和描述", "feishuStep4": "Message", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "复制获得的Bot Token", "telegramStep5": "Message", "telegramStep6": "访问 https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "消息格式支持：", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "注意事项：", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Chat ID格式说明：", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "• 群组中需要先将机器人添加为成员", "telegramChannelPermission": "Message", "webhookCallUrl": "Адрес вызова", "webhookConfigurationGuide": "Руководство по настройке Webhook", "webhookDataFormatExample": "Пример формата данных：", "webhookConfigurationInstructions": "Инструкции по настройке：", "webhookRequestMethod": "• Метод запроса：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• Метод аутентификации：<PERSON><PERSON>（необязательно, после заполнения добавит Authorization: Bearer {token} в заголовок запроса）", "webhookTimeout": "• Время ожидания：30 секунд", "webhookRetryMechanism": "• Механизм повтора：Повторяет попытку 2 раза после неудачи", "webhookTip": "💡 Совет：Убедитесь, что ваша конечная точка Webhook может принимать POST-запросы и возвращать коды состояния 2xx", "telegramStep3Detailed": "Message", "telegramPersonalChatDetailed": "Message", "telegramGroupChatDetailed": "Message", "telegramChannelDetailed": "Message", "telegramQuickChatIdTitle": "Message", "telegramQuickStep1": "Message", "telegramQuickStep2": "Message", "telegramQuickStep3": "Message"}, "legal": {"privacyPolicy": {"title": "Title", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "信息收集", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Text"}}, "informationUsage": {"title": "Title", "description": "Description", "items": ["提供和维护我们的服务", "Description", "改进服务质量和用户体验", "Description", "防止欺诈和滥用"]}, "informationSharing": {"title": "Title", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "数据安全", "description": "Description", "items": ["数据加密传输和存储", "Text", "定期安全审计和更新", "Text"]}, "dataRetention": {"title": "数据保留", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "您的权利", "description": "Description", "items": ["User", "删除您的账户和相关数据", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["维持用户会话", "Text", "Text"]}, "thirdPartyServices": {"title": "第三方服务", "description": "Description", "items": ["Text", "GitHub OAuth：用于用户身份验证", "Text"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "服务描述", "description": "Description", "items": ["API 密钥管理", "Description", "使用统计和监控", "Description", "Description"]}, "userAccount": {"title": "用户账户", "description": "Description", "items": ["Text", "User", "User", "及时更新账户信息", "User"]}, "usageRules": {"title": "使用规则", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "服务可用性", "description": "Description", "items": ["Text", "Text", "Text", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "高级功能可能需要付费", "Text", "Text"]}, "intellectualProperty": {"title": "知识产权", "description": "Description", "items": ["Text", "您获得有限的使用许可", "Text", "Text"]}, "privacyProtection": {"title": "隐私保护", "description": "Description", "items": ["Text", "采取合理措施保护数据安全", "Text"]}, "disclaimer": {"title": "免责声明", "description": "Description", "items": ["Text", "Text", "不对间接损失承担责任", "Text"]}, "serviceTermination": {"title": "服务终止", "description": "Description", "items": ["您违反这些条款", "Text", "Text", "法律要求"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["重大变更会提前通知", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "Text", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "Text"}}}