{"message": {"copyModelSuccess": "Đã sao chép tên mô hình vào clipboard!", "copyFailed": "<PERSON><PERSON> chép thất b<PERSON>i, vui lòng sao chép thủ công", "logoutSuccess": "<PERSON><PERSON><PERSON> xuất thành công", "loginSuccess": {"default": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "welcomeBack": "<PERSON><PERSON><PERSON> mừng trở lại"}, "removeLocalStorage": {"confirm": "Có xóa bộ nhớ cache cục bộ không?", "success": "Xóa bộ nhớ cache cục bộ thành công"}, "loadData": {"error": "<PERSON><PERSON><PERSON> dữ liệu {{name}} thất b<PERSON>i"}, "noNotice": "<PERSON><PERSON>n tại không có nội dung thông báo", "verification": {"turnstileChecking": "Turnstile đang kiểm tra môi trường người dùng!", "pleaseWait": "<PERSON><PERSON> lòng thử lại sau"}, "clipboard": {"inviteCodeDetected": "<PERSON><PERSON><PERSON> hiện mã mời, đã tự động điền vào!", "clickToCopy": "<PERSON><PERSON><PERSON><PERSON> để sao chép", "copySuccess": "<PERSON><PERSON> chép thành công"}}, "common": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "copyAll": "<PERSON><PERSON> ch<PERSON>p tất cả", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "more": "<PERSON><PERSON><PERSON><PERSON>", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Tắt", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "create": "Tạo", "usd": "USD", "day": "{{count}} ngày", "day_plural": "{{count}} ngày", "days": "ng<PERSON>y", "seconds": "giây", "times": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "bind": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "loading": "<PERSON><PERSON> tả<PERSON>...", "copyFailed": "<PERSON><PERSON> ch<PERSON>p thất b<PERSON>i", "people": "người", "ok": "OK", "close": "Đ<PERSON><PERSON>", "copied": "Đã sao chép", "expand": "Mở rộng", "collapse": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON><PERSON> có", "remark": "<PERSON><PERSON><PERSON>", "selectPlaceholder": "<PERSON><PERSON> lòng chọn {{name}}", "on": "<PERSON><PERSON><PERSON>", "off": "Tắt", "name": "<PERSON><PERSON><PERSON> danh", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "description": "<PERSON><PERSON>", "ratio": "Tỷ lệ", "unnamed": "<PERSON><PERSON><PERSON> ch<PERSON>a đặt tên", "groups": "Nhóm", "captchaPlaceholder": "<PERSON><PERSON> lòng nhập mã xác thực", "confirm": "<PERSON><PERSON><PERSON>", "permissions": "<PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "expiredTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "search": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại", "refresh": "<PERSON><PERSON><PERSON>", "pagination": {"total": "Từ {{start}} - {{end}}, tổng cộng {{total}} mục"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "<PERSON><PERSON><PERSON><PERSON> để mở liên kết"}, "userRole": {"normal": "<PERSON><PERSON><PERSON><PERSON> dùng thường", "agent": "<PERSON><PERSON><PERSON> lý", "admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "superAdmin": "<PERSON><PERSON><PERSON> quản trị viên", "loading": "<PERSON><PERSON> tả<PERSON>..."}, "channelStatus": {"enabled": "<PERSON><PERSON> bật", "disabled": "Đã tắt", "waitingRestart": "<PERSON><PERSON> chờ khởi động lại", "waiting": "<PERSON><PERSON> chờ", "autoStoppedTitle": "<PERSON><PERSON><PERSON> đã vượt quá số lần thử lại tối đa hoặc kích hoạt điều kiện tự động dừng", "stopped": "Đã dừng", "partiallyDisabled": "<PERSON><PERSON><PERSON> một ph<PERSON>n", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "reason": "Lý do"}, "channelBillingTypes": {"payAsYouGo": "<PERSON><PERSON> toán theo sử dụng", "payPerRequest": "<PERSON><PERSON> to<PERSON> theo yêu c<PERSON>u", "unknown": "<PERSON><PERSON><PERSON><PERSON> thức không xác đ<PERSON>nh"}, "tokenStatus": {"normal": "<PERSON><PERSON><PERSON>", "disabled": "Đã tắt", "expired": "<PERSON><PERSON> hết hạn", "exhausted": "Đã cạn ki<PERSON>t", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "userStatus": {"normal": "<PERSON><PERSON><PERSON>", "banned": "<PERSON><PERSON> cấm", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "redemptionStatus": {"normal": "<PERSON><PERSON><PERSON>", "disabled": "Đã tắt", "redeemed": "<PERSON><PERSON> đổi", "expired": "<PERSON><PERSON> hết hạn", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "duration": {"request": "<PERSON><PERSON><PERSON> c<PERSON>", "firstByte": "Byte đầu tiên", "total": "<PERSON><PERSON><PERSON> cộng", "seconds": "giây", "lessThanOneSecond": "<1 giây"}, "streamType": {"stream": "<PERSON><PERSON><PERSON>", "nonStream": "<PERSON><PERSON><PERSON><PERSON> luồng"}, "noSet": {"title": "<PERSON><PERSON><PERSON><PERSON> trị viên chưa thiết lập {{name}}", "name": {"about": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "chat": "<PERSON><PERSON><PERSON>"}}, "buttonText": {"add": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "save": "<PERSON><PERSON><PERSON>", "updateBalance": "<PERSON><PERSON><PERSON> nh<PERSON>t số dư", "test": "<PERSON><PERSON><PERSON> tra", "multiple": "<PERSON><PERSON><PERSON>"}, "channelPage": {"title": "<PERSON><PERSON><PERSON><PERSON> lý k<PERSON>nh"}, "channelStatusCount": {"title": "Th<PERSON>ng kê trạng thái kênh", "summary": "<PERSON><PERSON> bật {{enabled}} | <PERSON><PERSON> tắt {{disabled}} | <PERSON><PERSON> thử lại {{retry}} | Đã dừng {{stopped}} | Tắt một phần {{partial}}", "statusEnabled": "<PERSON><PERSON> bật", "statusDisabled": "Đã tắt", "statusRetry": "<PERSON><PERSON> thử lại", "statusStopped": "Đã dừng", "statusPartially": "<PERSON><PERSON><PERSON> một ph<PERSON>n"}, "header": {"routes": {"status": "状态", "home": "主页", "chat": "对话", "pptGen": "PPT生成", "chart": "统计", "agency": "代理商", "channel": "渠道", "ability": "渠道能力", "channelGroup": "渠道组", "token": "令牌", "log": "日志", "logDetail": "明细", "midjourney": "绘图", "task": "异步任务", "user": "用户", "config": " 配置", "packagePlanAdmin": "套餐", "redemption": "兑换码", "group": "分组", "query": "查询", "about": "关于", "agencyJoin": "代理加盟", "setting": {"default": "设置", "operation": "运营设置", "system": "系统设置", "global": "全局设置", "advance": "特性设置", "sensitive": "敏感词配置", "verification": "验证码配置", "update": "检查更新"}, "account": {"default": "账户", "profile": "个人中心", "cardTopup": "卡密兑换", "onlineTopup": "在线充值", "recharge": "余额充值", "balanceTransfer": "余额转移", "pricing": "费用说明", "notificationSettings": "通知设置", "packagePlan": {"list": "套餐购买", "record": "购买记录"}}, "tools": {"default": "工具", "fileUpload": "文件上传", "keyExtraction": "密钥提取", "multiplierCalculator": "倍率计算器", "shortLink": "短链生成", "testConnection": "访问测试", "customPrompts": "提示词管理", "redis": "Redis 可视化", "ratioCompare": "倍率对比", "serverLog": "服务器日志查看器"}, "onlineTopupRecord": "充值记录", "channelScores": "渠道得分", "dynamicRouter": "动态路由"}, "dropdownMenu": {"profile": "个人中心", "recharge": "余额充值", "agencyCenter": "代理商中心", "checkin": "签到", "darkMode": {"enable": "深色模式", "disable": "日间模式"}, "fullScreen": {"default": "切换全屏", "enable": "全屏模式", "disable": "退出全屏"}, "logout": "退出登录"}, "checkin": {"default": "签到", "success": "签到成功", "failed": "签到失败", "verification": "请完成验证"}, "avatarProps": {"login": "登录"}}, "settings": {"public": {"titles": {"default": "公共设置"}, "SystemName": "系统名称", "ServerAddress": "服务地址", "TopUpLink": "充值链接", "ChatLink": "对话链接", "Logo": "系统Logo", "HomePageContent": "首页内容", "About": "关于内容", "Notice": "公告内容", "Footer": "页脚内容", "RegisterInfo": "注册通知", "HeaderScript": "自定义Header", "SiteDescription": "站点描述", "PrivacyPolicy": "隐私政策", "ServiceAgreement": "服务协议", "FloatButton": {"FloatButtonEnabled": "开启", "DocumentInfo": "文档信息", "WechatInfo": "微信信息", "QqInfo": "Q Q信息"}, "CustomThemeConfig": "自定义主题", "AppList": "友情链接"}}, "home": {"default": {"title": "欢迎使用 ", "subtitle": "基于One API二次开发，提供更完善的功能", "start": "开始使用", "description": {"title": "新特性：", "part1": "全新用户界面，方便快捷", "part2": "优化调度机制，高效稳定", "part3": "针对企业开发，安全可靠", "part4": "更多高级功能，等你发现"}}}, "dailyUsageChart": {"title": "每日模型使用情况", "yAxisName": "使用量 (USD)", "loadingTip": "每日使用情况", "fetchError": "获取每日使用数据时出错："}, "modelUsageChart": {"title": "模型使用情况", "hourlyTitle": "模型每小时使用情况", "dailyTitle": "模型每日使用情况", "weeklyTitle": "模型每周使用情况", "monthlyTitle": "模型每月使用情况"}, "granularity": {"hour": "每小时", "day": "每天", "week": "每周", "month": "每月", "all": "全部"}, "abilitiesTable": {"title": "渠道能力", "export": "导出", "group": "组别", "model": "模型", "channelId": "渠道编号", "enabled": "已启用", "weight": "权重", "priority": "优先级", "billingType": "计费类型", "functionCallEnabled": "功能调用启用", "imageSupported": "支持图像", "yes": "是", "no": "否", "perToken": "按Token计费", "perRequest": "按请求计费", "noDataToExport": "没有数据可以导出", "exportConfirm": "您确定要导出当前页面的数据吗？", "exportSuccess": "导出成功", "toggleSuccess": "切换成功", "toggleError": "切换失败", "selectOrInputGroup": "选择或输入用户分组"}, "logsTable": {"retry": "重试", "retryChannelList": "重试渠道列表", "retryDurations": "重试耗时详情", "channel": "渠道", "duration": "耗时", "startTime": "开始时间", "endTime": "结束时间", "retryCount": "重试次数", "retryDetails": "重试详情", "totalRetryTime": "总重试时间", "seconds": "秒", "tokenGroup": "令牌分组", "selectGroup": "选择分组", "dailyModelUsageStats": "调用数据一览", "time": "时间", "moreInfo": "更多信息", "ip": "IP", "remoteIp": "远程 IP", "ipTooltip": "IP: {{ip}}\n远程 IP: {{remoteIp}}", "requestId": "请求 ID", "username": "用户名", "userId": "用户 ID", "tokenName": "令牌名称", "token": "令牌", "type": "类型", "typeUnknown": "未知", "type充值": "充值", "type消费": "消费", "type管理": "管理", "type系统": "系统", "type邀请": "邀请", "type提示": "提示", "type警告": "警告", "type错误": "错误", "type签到": "签到", "type日志": "日志", "type退款": "退款", "type邀请奖励金划转": "邀请奖励金划转", "type代理奖励": "代理奖励", "type下游错误": "下游错误", "type测试渠道": "测试渠道", "typeRecharge": "充值", "typeConsumption": "消费", "typeManagement": "管理", "typeSystem": "系统", "typeInvitation": "邀请", "typePrompt": "提示", "typeWarning": "警告", "typeError": "错误", "typeCheckin": "签到", "typeLog": "日志", "typeRefund": "退款", "typeInviteReward": "邀请奖励金划转", "typeAgencyBonus": "代理奖励", "typeDownstreamError": "下游错误", "typeChannelTest": "测试渠道", "channelId": "渠道 ID", "channelName": "渠道名称", "model": "模型", "modelPlaceholder": "输入/选择模型名称", "info": "信息", "isStream": "流式", "isStreamPlaceholder": "输入/选择是否流式", "prompt": "提示", "completion": "补全", "consumption": "消费", "consumptionRange": "消费额度范围", "description": "说明", "action": "操作", "details": "详情", "tokenKey": "令牌密钥", "requestDuration": "请求耗时", "firstByteDuration": "首字节耗时", "totalDuration": "总耗时", "lessThanOneSecond": "<1秒", "modelInvocation": "模型调用", "modelUsage": "模型使用情况", "totalQuota": "总消费额度: {{quota}}", "totalRpm": "请求数/分钟: {{rpm}}", "totalTpm": "Token数/分钟: {{tpm}}", "totalMpm": "金额/分钟: {{mpm}}", "dailyEstimate": "预计日消费: {{estimate}}", "currentStats": "当前RPM:{{rpm}} 当前TPM:{{tpm}} 当前MPM:${{mpm}} 预估日消:${{dailyEstimate}}", "statsTooltip": "仅统计未归档的日志,RPM:每分钟请求次数,TPM:每分钟Token数,MPM:每分钟消费的Money,预估日消是根据当前MPM推断出来的", "showAll": "全部展示", "exportConfirm": "导出本页日志？", "export": "导出", "statsData": "统计数据", "today": "当天", "lastHour": "1小时", "last3Hours": "3小时", "lastDay": "1天", "last3Days": "3天", "last7Days": "7天", "lastMonth": "1个月", "last3Months": "3个月", "excludeModels": "排除模型", "selectModelsToExclude": "选择要排除的模型", "excludeErrorCodes": "排除错误码", "excludeErrorCodesPlaceholder": "选择要排除的错误码", "errorCode": "错误码", "errorCodePlaceholder": "输入/选择错误码", "timezoneTip": "当前时区: {timezone}", "timezoneNote": "时区提示", "timezoneDescription": "统计数据按照您当前的时区进行日期分组。不同时区可能会导致数据分组的时间段有所不同。如需调整，请前往个人中心修改时区设置。", "goToProfile": "前往个人中心", "realtimeQuota": "实时消费(1分钟)", "viewTotalQuota": "查看总消费", "viewTotalQuotaTip": "查看历史总消费金额(查询可能需要几秒钟)", "loadingTotalQuota": "正在查询总消费金额，请稍候...", "totalQuotaTitle": "历史总消费统计", "loadTotalQuotaError": "获取总消费金额失败", "requestLogs": "请求日志 - {{requestId}}", "noRequestLogs": "暂无请求日志", "metricsExplanation": "仅统计未归档的日志,RPM:每分钟请求次数,TPM:每分钟Token数,MPM:每分钟消费的Money,预估日消是根据当前MPM推断出来的", "autoRefresh": "自动刷新", "autoRefreshTip": "点击开启/关闭自动刷新，开启后每隔指定秒数自动刷新数据", "autoRefreshOn": "已开启自动刷新", "autoRefreshOff": "已关闭自动刷新", "refreshInterval": "刷新间隔", "stopRefresh": "停止刷新", "secondsWithValue": "{{seconds}}秒", "minutesWithValue": "{{minutes}}分钟"}, "mjLogs": {"logId": "日志ID", "submitTime": "提交时间", "type": "类型", "channelId": "渠道ID", "userId": "用户ID", "taskId": "任务ID", "submit": "提交", "status": "状态", "progress": "进度", "duration": "耗时", "result": "结果", "prompt": "Prompt", "promptEn": "PromptEn", "failReason": "失败原因", "startTime": "开始时间", "endTime": "结束时间", "today": "当天", "lastHour": "1小时", "last3Hours": "3小时", "lastDay": "1天", "last3Days": "3天", "last7Days": "7天", "lastMonth": "1个月", "last3Months": "3个月", "selectTaskType": "选择任务类型", "selectSubmitStatus": "选择提交情况", "submitSuccess": "提交成功", "queueing": "正在排队", "duplicateSubmit": "重复提交", "selectTaskStatus": "选择任务状态", "success": "成功", "waiting": "等待", "queued": "排队", "executing": "执行", "failed": "失败", "seconds": "秒", "unknown": "未知", "viewImage": "点击查看", "markdownFormat": "Markdown 格式", "midjourneyTaskId": "Midjourney 任务ID", "copiedAsMarkdown": "已复制为 Markdown 格式", "copyFailed": "复制失败", "copiedMidjourneyTaskId": "已复制 Midjourney 任务ID", "drawingLogs": "绘图日志", "onlyUnarchived": "仅统计未被归档的日志", "imagePreview": "图片预览", "copiedImageUrl": "已复制图片地址", "copy": "复制", "download": "下载", "resultImage": "结果图片", "downloadError": "下载图片失败", "mode": "模式", "selectMode": "选择模式", "relax": "轻松模式", "fast": "快速模式", "turbo": "极速模式", "actions": "操作", "refresh": "刷新", "tasks": {"title": "Nhiệm vụ bất đồng bộ", "taskId": "ID nhiệm vụ", "platform": "<PERSON><PERSON><PERSON> t<PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "submitTime": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "startTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "taskIdPlaceholder": "Nhập ID nhiệm vụ", "platformPlaceholder": "<PERSON><PERSON><PERSON> n<PERSON>n tảng", "typePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON> n<PERSON> vụ", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái", "videoGeneration": "Tạo video", "imageGeneration": "<PERSON><PERSON><PERSON>", "musicGeneration": "Tạo nhạc", "textGeneration": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "inProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "submitted": "Đ<PERSON> gửi", "queued": "<PERSON><PERSON> chờ", "notStarted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu", "viewResult": "<PERSON><PERSON> kế<PERSON> quả", "viewError": "Xem lỗi", "taskDetails": "<PERSON> ti<PERSON><PERSON> n<PERSON> vụ", "errorDetails": "<PERSON> tiết lỗi", "loadError": "Lỗi tải danh s<PERSON>ch nhi<PERSON>m vụ"}, "refreshSuccess": "任务状态刷新成功", "refreshFailed": "任务状态刷新失败", "refreshError": "刷新任务状态时发生错误", "viewVideo": "查看视频", "videoPreview": "视频预览", "copyVideoUrl": "复制视频地址", "copiedVideoUrl": "已复制视频地址", "downloadVideo": "下载视频", "videoNotSupported": "您的浏览器不支持视频播放", "videoUrl": "视频地址", "videoUrls": "视频地址列表"}, "mjTaskType": {"IMAGINE": "生成图像", "UPSCALE": "放大", "VARIATION": "变换", "REROLL": "重新生成", "DESCRIBE": "图生文", "BLEND": "混图", "OUTPAINT": "变焦", "DEFAULT": "未知"}, "mjCode": {"submitSuccess": "提交成功", "queueing": "正在排队", "duplicateSubmit": "重复提交", "unknown": "未知"}, "mjStatus": {"success": "成功", "waiting": "等待", "queued": "排队", "executing": "执行", "failed": "失败", "unknown": "未知"}, "tokensTable": {"title": "令牌管理", "table": {"title": "令牌管理", "toolBar": {"add": "新建令牌", "delete": "删除令牌", "deleteConfirm": "正在批量删除{{count}}个令牌，该操作不可逆", "export": "导出", "exportConfirm": "导出当前页面令牌？"}, "action": "操作"}, "modal": {"title": {"add": "新建令牌", "edit": "编辑令牌"}, "field": {"name": "令牌名称", "description": "令牌描述", "type": {"default": "计费方式", "type1": "按量计费", "type2": "按次计费", "type3": "混合计费", "type4": "按量优先", "type5": "按次优先"}, "status": "状态", "statusEnabled": "正常", "statusDisabled": "禁用", "statusExpired": "过期", "statusExhausted": "耗尽", "models": "可用模型", "usedQuota": "消耗额度", "remainQuota": "剩余额度", "createdTime": "创建时间", "expiredTime": "过期时间", "all": "全部", "more": "更多", "notEnabled": "未启用", "unlimited": "无限制", "daysLeft": "{{days}} 天后过期", "expired": "已过期 {{days}} 天", "userId": "用户ID", "key": "API密钥", "neverExpire": "永不过期"}, "delete": {"title": "删除", "content": "确定要删除API密钥 {{name}} 吗？"}, "footer": {"cancel": "取消", "confirm": "确认", "update": "更新"}, "bridge": {"title": "渠道快速对接", "placeholder": "请输入你的 {{name}} 服务地址"}, "copy": {"title": "手动复制"}}, "dropdown": {"onlineChat": "在线对话", "disableToken": "禁用令牌", "enableToken": "启用令牌", "editToken": "编辑令牌", "requestExample": "请求示例", "tokenLog": "令牌日志", "shareToken": "分享令牌", "quickIntegration": "一键对接"}, "error": {"fetchModelsFailed": "获取模型失败：{{message}}", "batchDeleteFailed": "批量删除失败：{{message}}", "deleteTokenFailed": "删除令牌失败：{{message}}", "refreshTokenFailed": "刷新令牌失败：{{message}}", "enableTokenFailed": "启用令牌失败：{{message}}", "disableTokenFailed": "禁用令牌失败：{{message}}", "fetchDataFailed": "获取数据失败：{{message}}"}, "success": {"batchDelete": "成功删除{{count}}个令牌", "shareTextCopied": "分享文本已复制到剪贴板", "tokenCopied": "令牌已复制到剪贴板", "deleteToken": "删除令牌成功", "refreshToken": "刷新令牌成功", "enableToken": "启用令牌成功", "disableToken": "禁用令牌成功", "export": "导出当前页令牌成功"}, "warning": {"copyFailed": "复制失败，请手动复制", "invalidServerAddress": "请输入正确的服务器地址"}, "info": {"openingBridgePage": "正在打开对接页面，已为您复制令牌"}, "export": {"name": "名称", "key": "密钥", "billingType": "计费方式", "status": "状态", "models": "可用模型", "usedQuota": "消耗额度", "remainQuota": "剩余额度", "createdTime": "创建时间", "expiredTime": "过期时间", "unlimited": "无限制", "neverExpire": "永不过期"}, "billingType": {"1": "按量计费", "2": "按次计费", "3": "混合计费", "4": "按量优先", "5": "按次优先"}, "bridge": {"quickIntegration": " 一键对接"}}, "editTokenModal": {"editTitle": "编辑令牌", "createTitle": "创建令牌", "defaultTokenName": "{{username}}的令牌 {{date}}", "tokenName": "令牌名称", "unlimitedQuota": "无限额度", "remainingQuota": "剩余额度", "authorizedQuota": "授权额度", "quotaLimitNote": "令牌最大可用额度受限于账户余额", "quickOptions": "快捷选项", "neverExpire": "永不过期", "expiryTime": "过期时间", "billingMode": "计费模式", "selectGroup": "选择分组", "switchGroup": "选择分组", "switchGroupTooltip": "选择令牌所属的分组，不同分组有不同的定价和功能权限。若不选择，则默认使用当前用户所在分组", "switchGroupHint": "选择分组将影响令牌的计费倍率和可用模型，请根据实际需求选择", "importantFeature": "重要", "tokenRemark": "令牌备注", "discordProxy": "Discord 代理", "enableAdvancedOptions": "启用高级选项", "generationAmount": "生成数量", "availableModels": "可用模型", "selectModels": "选择/搜索/添加可用模型，留空表示无限制", "activateOnFirstUse": "首次使用激活", "activateOnFirstUseTooltip": "首次使用后激活有效期，如果开启此选项，并且通过首次使用激活，则会覆盖上面配置的令牌有效期", "activationValidPeriod": "激活有效期", "activationValidPeriodTooltip": "首次使用后激活token有效期（单位：天）", "ipWhitelist": "IP白名单", "ipWhitelistPlaceholder": "IP地址（段），支持 IPV4 和 IPV6，多个用逗号分割", "rateLimiter": "限流器", "rateLimitPeriod": "限流周期", "rateLimitPeriodTooltip": "限流周期（单位：秒）", "rateLimitCount": "限流次数", "rateLimitCountTooltip": "限流周期内可用的次数", "promptMessage": "提示消息", "promptMessageTooltip": "限流超出时的提示消息", "promotionPosition": "推广位置", "promotionPositionStart": "开头", "promotionPositionEnd": "结尾", "promotionPositionRandom": "随机", "promotionContent": "推广内容", "currentGroup": "当前分组", "searchGroupPlaceholder": "搜索分组名称、描述或倍率...", "mjTranslateConfig": "MJ翻译配置", "mjTranslateConfigTip": "仅对Midjourney提示词生效的翻译配置", "mjTranslateBaseUrlPlaceholder": "请输入翻译服务的基础URL", "mjTranslateApiKeyPlaceholder": "请输入翻译服务的API密钥", "mjTranslateModelPlaceholder": "请输入翻译服务使用的模型名称", "mjTranslateBaseUrlRequired": "启用翻译时必须提供基础URL", "mjTranslateApiKeyRequired": "启用翻译时必须提供API密钥", "mjTranslateModelRequired": "启用翻译时必须提供模型名称"}, "addTokenQuotaModal": {"title": "令牌余额管理{{username}}", "defaultReason": "管理员操作", "enterRechargeAmount": "请输入充值金额", "enterRemark": "请输入备注消息", "confirmOperation": "确认操作", "confirmContent": "确认为{{username}}{{action}}{{amount}}美元{{updateExpiry}}？", "recharge": "充值", "deduct": "扣除", "andUpdateExpiry": "，并更新余额有效期为{{days}}天", "alertMessage": "输入负数可以减扣用户余额", "rechargeAmount": "充值额度", "operationReason": "操作原因", "finalBalance": "最终余额"}, "billingType": {"1": "按量计费", "2": "按次计费", "3": "混合计费", "4": "按量优先", "5": "按次优先", "payAsYouGo": "按量计费", "payPerRequest": "按次计费", "hybrid": "混合计费", "payAsYouGoPriority": "按量优先", "payPerRequestPriority": "按次优先", "unknown": "未知方式"}, "packagePlanAdmin": {"title": "套餐", "table": {"title": "套餐管理", "toolBar": {"add": "新建套餐", "delete": "删除套餐"}, "action": {"edit": "编辑", "delete": "删除", "detail": "详情", "recovery": "上架", "offline": "下架"}}, "modal": {"title": {"add": "新建套餐", "edit": "编辑套餐"}, "field": {"name": "套餐名称", "type": {"default": "套餐类型", "type1": "额度套餐", "type2": "计次套餐", "type3": "时长套餐"}, "group": "套餐分组", "description": "套餐描述", "price": "套餐价格", "valid_period": "有效期限", "first_buy_discount": "首购折扣", "rate_limit_num": "限制次数", "rate_limit_duration": "限制周期", "inventory": "套餐库存", "available_models": "可用模型", "quota": "套餐额度", "times": "套餐次数"}, "footer": {"cancel": "取消", "confirm": "确认", "update": "更新"}}}, "login": {"title": "登录", "username": "用户名", "password": "密码", "login": "登录", "otherLoginMethods": "其他登录方式", "register": "注册账户", "accountLogin": "账号登录", "phoneLogin": "手机号登录", "usernamePlaceholder": "用户名", "usernameRequired": "请输入用户名!", "passwordPlaceholder": "密码", "passwordRequired": "请输入密码！", "passwordMaxLength": "密码长度不能超过20位！", "phonePlaceholder": "手机号", "phoneRequired": "请输入手机号！", "phoneFormatError": "手机号格式错误！", "smsCodePlaceholder": "短信验证码", "smsCodeCountdown": "{{count}}秒后重新获取", "getSmsCode": "获取验证码", "agreementText": "我同意", "privacyPolicy": "《隐私政策》", "and": "和", "serviceAgreement": "《服务协议》", "alreadyLoggedIn": "您已登录", "weakPasswordWarning": "您的密码过于简单，请及时修改！", "welcomeMessage": "欢迎使用", "captchaError": "验证码错误", "credentialsError": "用户名或密码错误", "resetPassword": "重置密码", "captchaExpired": "验证码不存在或已过期", "loginFailed": "登录失败：{{message}}", "captchaRequired": "请输入验证码!", "captchaPlaceholder": "验证码", "smsSent": "短信验证码发送成功", "smsSendFailed": "短信验证码发送失败", "agreementWarning": "请先同意《隐私政策》和《服务协议》", "turnstileWarning": "请稍后重试，Turnstile 正在检查用户环境！", "loginSuccess": "登录成功"}, "register": {"title": "注册", "usernameRequired": "请输入用户名！", "usernameNoAt": "用户名不能包含 @ 符号", "usernameNoChinese": "用户名不能包含中文字符", "usernameLength": "用户名长度应为 4-12 个字符", "usernamePlaceholder": "用户名", "passwordRequired": "请输入密码！", "passwordLength": "密码长度应为 8-20 个字符", "passwordPlaceholder": "密码", "confirmPasswordRequired": "请确认密码！", "passwordMismatch": "两次输入的密码不一致！", "confirmPasswordPlaceholder": "确认密码", "emailInvalid": "请输入有效的邮箱地址！", "emailRequired": "请输入邮箱！", "emailPlaceholder": "邮箱地址", "emailCodeRequired": "请输入邮箱验证码！", "emailCodePlaceholder": "邮箱验证码", "enterCaptcha": "请输入验证码", "resendEmailCode": "{{seconds}}秒后重新发送", "getEmailCode": "获取验证码", "phoneRequired": "请输入手机号码！", "phoneInvalid": "手机号码格式不正确！", "phonePlaceholder": "手机号码", "smsCodeRequired": "请输入短信验证码！", "smsCodePlaceholder": "短信验证码", "resendSmsCode": "{{seconds}}秒后重新发送", "getSmsCode": "获取验证码", "captchaRequired": "请输入验证码！", "captchaPlaceholder": "验证码", "inviteCodePlaceholder": "邀请码（选填）", "submit": "注册", "successMessage": "注册成功", "failMessage": "注册失败", "emailCodeSent": "邮箱验证码已发送", "smsCodeSent": "短信验证码已发送", "confirm": "确认", "emailVerifyTitle": "邮箱验证", "smsVerifyTitle": "短信验证", "registerVerifyTitle": "注册验证"}, "profile": {"timezone": "时区", "phoneNumber": "手机号码", "emailAddress": "邮箱地址", "wechatAccount": "微信账户", "telegramAccount": "Telegram账户", "bindTelegram": "绑定Telegram", "balanceValidPeriod": "余额有效期", "lastLoginIP": "上次登录IP", "lastLoginTime": "上次登录时间", "inviteCode": "邀请码", "inviteLink": "邀请链接", "generate": "生成", "pendingEarnings": "待使用收益", "transfer": "划转", "totalEarnings": "总收益", "accountBalance": "账户余额", "totalConsumption": "累计消费", "callCount": "调用次数", "invitedUsers": "邀请用户", "promotionInfo": "推广信息", "inviteDescription": "一次邀请,终身返利,邀请越多，返利越多", "userInfo": "用户信息", "availableModels": "可用模型", "modelNameCopied": "已复制模型名称", "noAvailableModels": "暂无可用模型", "accountOptions": "账户选项", "changePassword": "修改密码", "systemToken": "系统令牌", "accessTokens": "访问令牌", "unsubscribe": "注销", "educationCertification": "教育认证", "timezoneUpdateSuccess": "时区更新成功", "inviteLinkCopied": "邀请链接已复制", "inviteLinkCopyFailed": "邀请链接复制失败", "inviteLinkGenerationFailed": "邀请链接生成失败", "allModelsCopied": "所有模型已复制到剪贴板", "copyAllModels": "复制全部模型", "totalModels": "可用模型数量", "expired": "已过期", "validPeriod": "有效期", "longTermValid": "长期有效", "failedToLoadModels": "加载模型列表失败", "accessTokensManagement": "访问令牌管理", "accessTokenDescription": "访问令牌用于API访问身份验证，具有与您账户相关的特定权限", "tokenNameLabel": "令牌名称", "tokenNamePlaceholder": "给令牌起个名字，如：只读令牌、测试工具令牌等", "presetPermissions": "预设权限", "detailPermissions": "详细权限", "validityPeriod": "有效期（天）", "validityPeriodExtra": "0表示永不过期", "remarkLabel": "备注", "remarkPlaceholder": "可选，令牌的用途描述等", "createNewToken": "创建新令牌", "tokenCreatedSuccess": "访问令牌创建成功", "tokenSavePrompt": "请妥善保存该令牌，它仅显示一次！", "copyToken": "复制令牌", "readPermission": "读取权限", "writePermission": "写入权限", "deletePermission": "删除权限", "tokenManagement": "令牌管理", "channelManagement": "渠道管理", "logView": "日志查看", "statisticsView": "统计信息", "userManagement": "用户管理", "quotaManagement": "额度管理", "readOnlyPermission": "只读权限", "writeOnlyPermission": "只写权限", "readWritePermission": "读写权限", "standardPermission": "标准权限", "fullPermission": "完全权限", "selectPermission": "请至少选择一项权限", "tokenStatus": "状态", "tokenEnabled": "启用", "tokenDisabled": "禁用", "enableToken": "启用", "disableToken": "禁用", "deleteToken": "删除", "deleteTokenConfirm": "确定要删除访问令牌\"{{name}}\"吗？", "disableTokenConfirm": "确定要禁用访问令牌\"{{name}}\"吗？", "enableTokenConfirm": "确定要启用访问令牌\"{{name}}\"吗？", "tokenExpiryNever": "永不过期", "accessTokensInfo": "访问令牌说明", "accessTokensInfoDetail1": "访问令牌用于API访问身份验证，具有与您账户相关的特定权限", "accessTokensInfoDetail2": "每个令牌都可以配置不同的权限，以满足不同的使用场景", "accessTokensInfoDetail3": "出于安全考虑，令牌仅在创建时显示一次，请妥善保存", "accessTokensInfoDetail4": "不再使用的令牌请及时禁用或删除", "accessTokensInfoDetail5": "作为超级管理员，您可以配置所有高级权限", "noPermission": "无权进行此操作"}, "topup": {"onlineRecharge": "在线充值", "cardRedemption": "兑换码核销", "accountBalance": "账户余额", "rechargeReminder": "充值提醒", "reminder1": "1. 余额可用于模型调用、套餐购买等", "reminder2": "2. 若支付后额度未到账，请联系客服处理", "reminder3": "3. 余额不支持提现，但可在同用户组内转账", "reminder4WithTransfer": "4. 充值成功后，账户余额有效期将重置为", "reminder4WithoutTransfer": "3. 充值成功后，账户余额有效期将重置为", "days": "天", "paymentSuccess": "支付成功", "paymentError": "支付出错", "paymentAmount": "支付金额：", "purchaseAmount": "购买额度：$ ", "yuan": "元", "or": "或", "usd": "美金", "cny": "元", "enterAmount": "请输入充值金额!", "amountPlaceholder": "请输入充值金额，{{min}}美金起", "amountUpdateError": "更新金额时出错", "alipay": "支付宝", "wechat": "微信", "visaMastercard": "Visa / Mastercard", "cardFormatError": "兑换码格式错误", "redeemSuccess": "{{amount}}兑换成功！", "redeemError": "兑换出错，请稍后重试", "enterCardKey": "请输入兑换码卡密", "cardKeyPlaceholder": "请输入兑换码卡密", "buyCardKey": "购买兑换码卡密", "redeem": "立即核销", "record": {"title": "充值记录", "amount": "充值额度", "payment": "支付金额", "paymentMethod": "支付方式", "orderNo": "订单号", "status": "状态", "createTime": "创建时间", "statusSuccess": "成功", "statusPending": "处理中", "statusFailed": "失败"}, "paymentMethodAlipay": "支付宝", "paymentMethodWxpay": "微信", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "管理员", "paymentMethodRedeem": "兑换码", "alipayF2F": "支付宝当面付"}, "pricing": {"fetchErrorMessage": "获取价格信息出现异常，请联系管理员", "availableModelErrorMessage": "获取可用模型出现异常，请联系管理员", "modelName": "模型名称", "billingType": "计费类型", "price": "价格", "ratio": "倍率", "promptPriceSame": "提示价格：和原始费率相同", "completionPriceSame": "补全价格：和原始费率相同", "promptPrice": "提示价格：$ {{price}} / 1M tokens", "completionPrice": "补全价格：$ {{price}} / 1M tokens", "promptRatioSame": "提示倍率：和原始倍率相同", "completionRatioSame": "补全倍率：和原始倍率相同", "promptRatio": "提示倍率：{{ratio}}", "completionRatio": "补全倍率：{{ratio}}", "payAsYouGo": "按量付费 - Chat", "fixedPrice": "$ {{price}} / 次", "payPerRequest": "按次付费 - Chat", "dynamicPrice": "$ {{price}} / 次", "payPerRequestAPI": "按次付费 - API", "loadingTip": "正在获取价格信息...", "userGroupRatio": "您的用户分组倍率为：{{ratio}}", "readFailed": "读取失败", "billingFormula": "按量计费费用 = 转换率 × 分组倍率 × 模型倍率 × （提示token数 + 补全token数 × 补全倍率）/ 500000（单位：美元）", "billingFormula1": "转换率 = (新充值倍率/原充值倍率) × (新分组倍率/原分组倍率)", "generatedBy": "本页面由 {{systemName}} 自动生成", "modalTitle": "价格详情", "perMillionTokens": "/1M tokens", "close": "关闭", "searchPlaceholder": "搜索模型名称", "viewGroups": "查看分组", "copiedToClipboard": "已复制到剪贴板", "copyFailed": "复制失败", "groupName": "分组名称", "availableGroups": "模型 {{model}} 可用的分组", "noGroupsAvailable": "没有可用的分组", "modelGroupsErrorMessage": "获取模型分组数据失败", "currentGroup": "当前分组", "copyModelName": "复制模型名称", "groupRatio": "分组比率", "closeModal": "关闭", "groupsForModel": "模型可用分组", "actions": "操作", "filterByGroup": "按分组筛选", "groupSwitched": "已切换到分组: {{group}}", "showAdjustedPrice": "显示分组调整后价格 (当前倍率: {{ratio}})"}, "guestQuery": {"usageTime": "使用时间", "modelName": "模型名称", "promptTooltip": "输入消耗 Tokens", "completionTooltip": "输出消耗 Tokens", "quotaConsumed": "消耗额度", "pasteConfirm": "检测到剪贴板中有有效的令牌，是否要粘贴？", "queryFailed": "查询失败", "tokenExpired": "该令牌已过期", "tokenExhausted": "该令牌额度已用尽", "invalidToken": "请输入正确的令牌", "focusRequired": "请确保页面处于焦点状态", "queryFirst": "请先查询", "tokenInfoText": "令牌总额：{{totalQuota}}\n令牌消耗：{{usedQuota}}\n令牌余额：{{remainQuota}}\n调用次数：{{callCount}}\n有效期至：{{validUntil}}", "unlimited": "无限制", "neverExpire": "永不过期", "infoCopied": "令牌信息已复制到剪贴板", "copyFailed": "复制失败", "noDataToExport": "没有数据可以导出", "prompt": "提示", "completion": "补全", "disabled": "访客查询未启用", "tokenQuery": "令牌查询", "tokenPlaceholder": "请输入要查询的令牌（sk-xxx）", "tokenInfo": "令牌信息", "copyInfo": "复制信息", "totalQuota": "令牌总额", "usedQuota": "令牌消耗", "remainQuota": "令牌余额", "callCount": "调用次数", "validUntil": "有效期至", "currentRPM": "当前RPM", "currentTPM": "当前TPM", "callLogs": "调用日志", "exportLogs": "导出日志"}, "agencyProfile": {"fetchError": "获取代理商信息失败", "fetchCommissionError": "获取佣金列表失败", "systemPreset": "系统预设", "lowerRatioWarning": "费率低于系统预设", "lowerRatioMessage": "以下费率低于系统预设值，请及时修改：", "cancelRatioEdit": "取消修改费率", "updateSuccess": "更新成功", "updateError": "代理商信息更新失败：", "updateFailed": "更新失败: ", "customPriceUpdateSuccess": "自定义价格更新成功", "customPriceUpdateError": "自定义价格更新失败：", "time": "时间", "type": "类型", "agencyCommission": "代理商返佣", "unknownType": "未知类型", "amount": "金额", "balance": "余额", "description": "描述", "group": "分组", "customRate": "自定义费率", "systemDefaultRate": "系统默认费率", "action": "操作", "save": "保存", "cancel": "取消", "edit": "编辑", "agencyConsole": "代理商控制台", "agencyInfo": "代理商信息", "editInfo": "编辑信息", "agencyName": "代理商名称", "agencyLevel": "代理商等级", "level1": "Lv 1", "subordinateUsers": "下级用户", "totalSales": "总销售额", "commissionIncome": "佣金收入", "cumulativeEarnings": "累计收益", "agencyFunctions": "代理功能", "hideSubordinateUsers": "隐藏下级用户", "viewSubordinateUsers": "查看下级用户", "hideCommissionDetails": "隐藏佣金明细", "viewCommissionDetails": "查看佣金明细", "hideCustomPrice": "隐藏自定义价格", "setCustomPrice": "设置自定义价格", "subordinateUsersList": "下级用户列表", "commissionRecords": "佣金记录", "customPriceSettings": "自定义价格设置", "saveChanges": "保存更改", "editAgencyInfo": "编辑代理商信息", "logo": "Logo", "setAgencyLogo": "设置代理商Logo", "customHomepage": "自定义首页", "aboutContent": "关于内容", "newHomepageConfig": "新首页配置", "customAnnouncement": "自定义公告", "customRechargeGroupRateJson": "自定义充值分组费率json", "customRechargeRate": "自定义充值费率", "viewSystemDefaultRate": "查看系统默认费率", "rateComparison": "费率比较", "comparisonResult": "对比结果", "higherThanSystem": "高于系统", "lowerThanSystem": "低于系统", "equalToSystem": "等于系统", "unknown": "未知", "notAnAgentYet": "您还不是代理商", "becomeAnAgent": "成为代理商", "startYourOnlineBusiness": "🌟 轻松开启您的在线事业", "becomeOurAgent": "成为我们的代理商，享受无压力创业体验：", "noInventory": "💼 无需压货，零资金周转压力", "instantCommission": "💰 销售即时分成，按比例获取丰厚回报", "easyManagement": "🖥️ 无需建站技术，轻松管理您的在线商店", "flexibleDomainChoice": "🌐 灵活的域名选择", "youCan": "您可以：", "useOwnDomain": "🏠 使用自己的域名", "orUseOurSubdomain": "🎁 或由我们为您提供专属子域名", "convenientStart": "🔥 无论您是经验丰富还是刚刚起步，我们都为您提供便捷的开始方式。", "actNow": "🚀 立即行动！", "contactAdmin": "联系网站管理员，开启您的代理商之旅！ 📞", "applyNow": "立即申请", "contactCooperation": "咨询合作", "understandPolicy": "了解代理商政策和合作细节", "provideDomain": "提供域名", "configDomain": "提供您的域名，我们帮您配置", "promoteAndEarn": "推广获利", "startPromoting": "开始推广您的代理站点，赚取佣金", "noDeploymentWorries": "无需担心复杂的云服务部署、支付渠道、备货问题", "easySetup": "只需提供域名，按教程配置,即可轻松开启企业级 API 代理商业务", "customizeContent": "您可以定制价格、站点信息、SEO、Logo等内容", "commissionBenefits": "作为代理商，您将获得用户充值分成,系统自动扣除成本,剩余金额可随时提现", "joinNowBenefit": "现在就加入我们，一起吃下 AI 时代的红利！", "groups": {"student": "大学生", "studentDesc": "时间充裕，希望通过推广活动轻松增加收入，以负担部分生活费和娱乐支出", "partTime": "兼职或副业", "partTimeDesc": "不需大量时间投入，只需在业务余时间简单推广，即可轻松赚取额外的收入", "mediaWorker": "自媒体从业者", "mediaWorkerDesc": "拥有一定的粉丝基础，只需在文章或帖子末尾附上链接，即可轻松地实现额外的收益", "freelancer": "自由职业者", "freelancerDesc": "拥有大量灵活时间，仅通过参加与销售活动，即可轻松增加额外收入"}, "stories": {"story1": {"name": "张先生", "role": "大学生"}, "story2": {"name": "李女士", "role": "中学教师"}, "story3": {"name": "刘先生", "role": "电商"}, "story4": {"name": "郑先生", "role": "自媒体"}, "story5": {"name": "周先生", "role": "科研从业者"}, "story6": {"name": "王女士", "role": "小红书博主"}, "story7": {"name": "黄女士", "role": "自媒体"}, "story8": {"name": "刘先生", "role": "IT行业"}}, "earnedAmount": "已赚取{{amount}}", "applyForAgentNow": "立即申请成为代理商", "businessLinesConnected": "40+业务线已经接入", "agencyJoin": "代理加盟", "becomeExclusiveAgent": "成为我们的专属代理", "startBusinessJourney": "轻松开启您的商业旅程~", "welcomeToAgencyPage": "欢迎来到我们的代理页面！", "earningsTitle": "超百人已赚3000+元", "becomeAgentSteps": "成为代理商的步骤", "agencyRules": "代理规则", "suitableGroups": "适合人群", "agencyImages": {"becomeAgent": "成为代理商", "agencyBusiness": "代理业务"}, "rules": {"howToEstablishRelation": "用户怎么和我建立代理关系", "howToEstablishRelationAnswer": "在你的代理站点注册，即为你的用户", "canSetPrice": "我可以设置售价吗", "canSetPriceAnswer": "可以！但是你的售价必须高于拿货价的10%", "commissionShare": "我可以获得多少分成", "commissionShareAnswer": {"assumption": "假设：你的拿货价是$1=1元，你的售价是$1=2元，你的佣金比例是90%", "example": "用户在你的站点购买$10，消费20元", "calculation": "你可以获得：(2-1)*10*0.9 = 9元", "explanation": "解读：（售价-拿货价）*交易量*佣金比例"}}}, "error": {"title": "错误", "content": "出错了"}, "loading": {"title": "加载中", "content": "加载中..."}, "notfound": {"title": "404", "content": "页面未找到"}, "servererror": {"title": "500", "content": "服务器错误"}, "unauthorized": {"title": "401", "content": "未授权"}, "forbidden": {"title": "403", "content": "禁止访问"}, "networkerror": {"title": "网络错误", "content": "网络错误"}, "timeout": {"title": "超时", "content": "请求超时"}, "noresult": {"title": "无结果", "content": "无结果"}, "nopermission": {"title": "无权限", "content": "无权限"}, "channelBridge": {"title": "渠道快速对接", "channelPlatform": "渠道平台", "billingMethod": "计费方式", "channelName": "渠道名称", "remark": "备注", "availableGroups": "可用分组", "availableModels": "可用模型", "channelKey": "渠道密钥", "proxyAddress": "对接地址", "cancel": "取消", "submit": "提交", "gpt35Models": "GPT-3.5模型", "gpt4Models": "GPT-4模型", "clear": "清空", "customModelName": "自定义模型名称", "add": "添加", "moreConfigReminder": "更多配置请保存渠道后编辑", "quickIntegration": "一键对接", "selectBillingMethod": "请选择计费方式", "enterChannelName": "请输入渠道名称", "enterChannelRemark": "请输入渠道备注", "selectAvailableGroups": "请选择可使用该渠道的分组", "selectAvailableModels": "选择/搜索该渠道可用模型", "enterChannelKey": "请输入渠道密钥", "proxyAddressPlaceholder": "此项可选，用于通过代理站来进行 API 调用，请输入代理站地址", "includes16kModels": "包含16k模型", "excludes32kModels": "不包含32k模型", "cleared": "已清空", "addCustomModel": "添加自定义模型", "clipboardTokenDetected": "检测到剪贴板中有有效的令牌，是否要粘贴？", "channelIntegrationSuccess": "渠道对接成功！", "channelIntegrationFailed": "渠道对接失败："}, "about": {"loading": "获取最新内容...", "noContent": "管理员未设置关于页面内容", "loadFailed": "加载关于内容失败..."}, "onlineTopupRecord": {"title": "充值记录", "columns": {"id": "ID", "username": "用户", "amount": "充值额度", "money": "支付金额", "paymentMethod": "支付方式", "tradeNo": "订单号", "status": "状态", "createTime": "创建时间"}, "status": {"success": "成功", "pending": "处理中", "failed": "失败"}, "paymentMethod": {"alipay": "支付宝", "wxpay": "微信", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "描述信息", "downstreamError": "下游错误", "originalError": "原始错误", "requestParams": "请求参数", "copy": "复制"}, "viewMode": {"switchTo": "切换到{{mode}}视角", "cost": "成本", "usage": "使用量"}, "agenciesTable": {"title": "代理商管理", "addAgency": "新增代理商", "columns": {"id": "ID", "userId": "用户ID", "name": "名称", "domain": "域名", "commissionRate": "佣金比例", "salesVolume": "销售额", "userCount": "用户数", "commissionIncome": "佣金收入", "historicalCommission": "累计收益", "actions": "操作"}, "confirm": {"deleteTitle": "确定要删除该代理商吗？", "updateName": "正在更新代理商名称...", "updateSuccess": "更新成功", "updateFailed": "更新失败", "deleteSuccess": "删除成功！"}, "messages": {"getListFailed": "获取代理商列表失败：{{message}}", "deleteSuccess": "删除成功！", "loadingData": "加载中..."}}, "units": {"times": "次", "percentage": "{{value}}%", "formatUsage": "{{name}}：{{value}} 次 ({{percent}}%)"}, "notification": {"title": "通知设置", "subscriptionEvents": "订阅事件", "notificationMethods": "通知方式", "alertSettings": "预警设置", "emailConfig": "邮件配置", "customEmails": "自定义邮箱地址", "addEmail": "添加邮箱", "removeEmail": "删除", "emailPlaceholder": "请输入邮箱地址", "emailTooltip": "如果不填写，将使用您账户的预留邮箱", "emailDescription": "如果您希望将通知发送到其他邮箱地址，请在此处配置。留空则使用您账户的预留邮箱地址。", "balanceThreshold": "余额预警阈值", "balanceThresholdTooltip": "当账户余额低于此阈值时发送预警通知", "balanceThresholdDescription": "当余额低于此数值时发送预警通知（实时检查，2小时内最多通知1次）", "alertExplanationTitle": "预警说明", "alertExplanation": "• 余额预警：实时检查用户余额，低于阈值时立即通知\n• 营销通知：每日检查一次，避免过度打扰\n• 安全警报：发生时立即通知，确保账户安全\n• 系统公告：重要更新时一次性通知所有用户", "selectEvents": "选择您感兴趣的事件类型", "eventsDescription": "当这些事件发生时，系统将通过您选择的方式向您发送通知", "selectMethods": "选择接收通知的方式", "methodsDescription": "您可以同时启用多种通知方式，系统将通过所有启用的方式发送通知", "description": "管理您的通知偏好，选择接收哪些类型的通知以及通过何种方式接收", "recommended": "推荐开启", "important": "重要", "testRecommendation": "保存设置后建议进行测试，确保通知功能正常工作", "testNotification": "测试通知", "testMessage": "这是一条测试通知消息", "testSuccess": "测试通知发送成功", "testFailed": "测试通知发送失败", "saveSuccess": "通知设置保存成功", "saveFailed": "保存设置失败", "validation": {"invalidEmail": "请输入有效的邮箱地址", "emailRequired": "邮箱地址不能为空", "invalidUrl": "请输入有效的URL", "qywxWebhookRequired": "请输入企业微信机器人Webhook URL", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "请输入WxPusher用户UID", "dingtalkWebhookRequired": "请输入钉钉机器人Webhook URL", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "请输入Webhook URL", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "请输入Telegram Chat ID", "telegramBotTokenRequired": "请输入Telegram Bot Token"}, "qywxbotConfig": "企业微信机器人配置", "qywxbotGuide": "企业微信机器人配置指南", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "WxPusher配置指南", "wxpusherUid": "用户UID", "dingtalkConfig": "钉钉机器人配置", "dingtalkGuide": "钉钉机器人配置指南", "feishuConfig": "飞书机器人配置", "feishuGuide": "飞书机器人配置指南", "webhookConfig": "Webhook配置", "webhookGuide": "Webhook配置指南", "webhookUrl": "调用地址", "webhookToken": "接口凭证", "webhookTokenTooltip": "Bearer Token，用于API身份验证", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "Telegram机器人配置", "telegramGuide": "Telegram机器人配置指南", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "余额不足预警", "account_quota_expiry": "额度即将过期", "security_alert": "安全警报", "system_announcement": "系统公告", "promotional_activity": "促销活动通知", "model_pricing_update": "模型价格更新", "anti_loss_contact": "防失联-定期通知"}, "eventDescriptions": {"account_balance_low": "当账户余额低于设定阈值时通知您及时充值", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "账户异常登录、密码修改等安全相关提醒", "system_announcement": "重要的系统更新、维护通知和功能发布", "promotional_activity": "新的优惠活动、折扣信息和特殊促销", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "定期发送通知确保联系方式有效"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "通过Telegram机器人接收通知", "webhook": "通过Webhook接收通知", "wxpusher": "通过WxPusher微信推送服务", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "通过钉钉机器人接收通知", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "邮件通知", "telegram": "Telegram通知", "webhook": "Webhook通知", "wxpusher": "WxPusher通知", "qywxbot": "企业微信机器人", "dingtalk": "钉钉机器人", "feishu": "飞书机器人"}, "configurationSteps": "配置步骤：", "detailedDocumentation": "详细文档：", "qywxbotConfigurationGuide": "企业微信机器人配置指南", "qywxbotStep1": "在企业微信群聊中，点击右上角「...」→「群机器人」", "qywxbotStep2": "点击「添加机器人」→「自定义机器人」", "qywxbotStep3": "设置机器人名称和头像，完成创建", "qywxbotStep4": "复制生成的Webhook URL（格式：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx）", "qywxbotStep5": "将Webhook URL填入上方配置项", "qywxbotDocumentationLink": "企业微信群机器人配置说明", "wxpusherConfiguration": "WxPusher配置", "wxpusherConfigurationGuide": "WxPusher配置指南", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "用户UID", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "请输入WxPusher APP Token", "wxpusherUserUIDRequired": "请输入WxPusher用户UID", "wxpusherStep1": "访问 WxPusher官网 注册账号", "wxpusherStep2": "创建应用获取APP Token（格式：AT_xxx）", "wxpusherStep3": "微信扫码关注应用二维码获取用户UID（格式：UID_xxx）", "wxpusherStep4": "将APP Token和用户UID填入上方配置项", "wxpusherOfficialWebsite": "WxPusher官网", "dingtalkConfigurationGuide": "钉钉机器人配置指南", "dingtalkStep1": "在钉钉群聊中，点击右上角「...」→「群助手」→「添加机器人」", "dingtalkStep2": "选择「自定义」机器人，点击「添加」", "dingtalkStep3": "设置机器人名称和头像，选择安全设置：", "dingtalkStep4": "完成创建后，复制生成的Webhook URL", "dingtalkStep5": "URL格式：https://oapi.dingtalk.com/robot/send?access_token=xxx", "dingtalkSecurityNote": "• 建议启用加签验证，提高安全性", "dingtalkPrivacyNote": "• 请妥善保管Webhook URL，避免泄露", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "飞书机器人配置指南", "feishuStep1": "在飞书群聊中，点击右上角「设置」→「群机器人」", "feishuStep2": "点击「添加机器人」→「自定义机器人」", "feishuStep3": "设置机器人名称和描述", "feishuStep4": "选择安全设置（推荐启用签名校验）", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "如启用签名校验，同时复制签名密钥", "feishuStep7": "将Webhook URL和签名密钥填入上方配置项", "feishuSecurityNote": "• 建议启用签名校验，提高安全性", "feishuPrivacyNote": "• 请妥善保管Webhook URL，避免泄露", "feishuDocumentationLink": "飞书自定义机器人接入", "telegramConfigurationGuide": "Telegram机器人配置指南", "telegramStep1": "在Telegram中搜索并添加 @BotFather 机器人", "telegramStep2": "发送 /newbot 命令创建新机器人", "telegramStep3": "按提示设置机器人名称和用户名", "telegramStep4": "复制获得的Bot Token", "telegramStep5": "向您的机器人发送任意消息", "telegramStep6": "访问 https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "从返回结果中找到您的Chat ID", "telegramStep8": "将Bot Token和Chat ID填入上方配置项", "telegramSecurityNote": "• 请妥善保管Bot Token，避免泄露", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "• 推荐选择「自定义关键词」，添加关键词如「通知」、「预警」等", "dingtalkStep3b": "• 或选择「加签」方式（需要额外配置签名）", "dingtalkStep6": "将Webhook URL填入上方配置项", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "• 每个机器人每分钟最多发送20条消息", "dingtalkKeywordNote": "• 如果设置了关键词安全设置，消息内容必须包含设置的关键词", "feishuStep3Detailed": "设置机器人名称、描述和头像", "feishuStep4Detailed": "选择安全设置（推荐使用签名校验或关键词验证）：", "feishuSignatureVerification": "• 签名校验：提供更高的安全性，需要配置密钥", "feishuKeywordVerification": "• 关键词验证：消息必须包含指定关键词，如「通知」、「预警」", "feishuStep5Detailed": "完成创建后，复制生成的Webhook URL", "feishuStep6Detailed": "URL格式：https://open.feishu.cn/open-apis/bot/v2/hook/xxx", "feishuStep7Detailed": "将Webhook URL填入上方配置项", "feishuMessageFormatsTitle": "消息格式支持：", "feishuTextMessage": "• 文本消息：支持纯文本和@功能", "feishuRichTextMessage": "• 富文本消息：支持markdown格式、链接、图片等", "feishuCardMessage": "• 消息卡片：支持交互式卡片消息", "feishuImageMessage": "• 图片消息：支持发送图片内容", "feishuNoticeTitle": "注意事项：", "feishuRateLimit": "• 每个机器人每分钟最多发送100条消息", "feishuKeywordNote": "• 如果设置了关键词验证，消息内容必须包含设置的关键词", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "在返回的JSON中找到 chat.id 字段的值", "telegramStep7Detailed": "将Bot Token和Chat ID配置到您的账户设置中", "telegramStep8Detailed": "将Bot Token和Chat ID填入上方配置项", "telegramNoticeTitle": "注意事项：", "telegramRateLimit": "• 每个机器人每秒最多发送30条消息", "telegramMessageFormats": "• 支持文本、图片、文档等多种消息格式", "telegramMarkdownSupport": "• 支持Markdown和HTML格式的富文本消息", "telegramInlineKeyboard": "• 支持内联键盘和自定义键盘", "telegramPrivacyNote": "• 请妥善保管Bot Token，避免泄露", "telegramSecurityTip": "• 建议定期更换Bot Token以提高安全性", "telegramStep4Detailed": "创建成功后，BotFather会返回Bot Token（格式：*********0:ABCDEFGHIJKLMNOPQRSTUVWXYZ）", "telegramStep5Detailed": "获取Chat ID的方法：", "telegramPersonalChat": "• 个人聊天：向机器人发送消息，然后访问", "telegramGroupChat": "• 群组聊天：将机器人添加到群组，发送消息后同样访问上述链接", "telegramChannel": "• 频道：将机器人添加为管理员，Chat ID通常以-100开头", "telegramChatIdFormatsTitle": "Chat ID格式说明：", "telegramPersonalChatId": "• 个人聊天：正整数，如 *********", "telegramGroupChatId": "• 群组：负整数，如 -987654321", "telegramSuperGroupChatId": "• 超级群组/频道：以-100开头，如 -100*********0", "telegramUsernameFormat": "• 用户名：也可以使用@username格式（仅限公开群组/频道）", "telegramInteractionRequired": "• 机器人只能向已经与其交互过的用户发送消息", "telegramGroupMembership": "• 群组中需要先将机器人添加为成员", "telegramChannelPermission": "• 频道中机器人需要有发送消息的权限", "webhookCallUrl": "调用地址", "webhookConfigurationGuide": "Webhook配置指南", "webhookDataFormatExample": "数据格式示例：", "webhookConfigurationInstructions": "配置说明：", "webhookRequestMethod": "• 请求方法：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• 认证方式：Bearer <PERSON>ken（可选，填写后会在请求头中添加 Authorization: Bearer {token}）", "webhookTimeout": "• 超时时间：30秒", "webhookRetryMechanism": "• 重试机制：失败后会重试2次", "webhookTip": "💡 提示：确保您的Webhook端点能够接收POST请求并返回2xx状态码", "telegramStep3Detailed": "按提示设置机器人名称和用户名（用户名必须以bot结尾）", "telegramPersonalChatDetailed": "• 个人聊天：向机器人发送消息，然后访问", "telegramGroupChatDetailed": "• 群组聊天：将机器人添加到群组，发送消息后同样访问上述链接", "telegramChannelDetailed": "• 频道：将机器人添加为管理员，Chat ID通常以-100开头", "telegramQuickChatIdTitle": "快速获取Chat ID示例：", "telegramQuickStep1": "替换BOT_TOKEN：https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "在浏览器中访问上述链接", "telegramQuickStep3": "在JSON响应中查找：\"chat\":{\"id\":*********}"}, "dailyUsage": {"total": "总计", "totalCost": "总成本", "tooltipTitle": {"cost": "成本情况", "usage": "使用情况"}, "yAxisName": {"cost": "成本 (USD)", "usage": "使用量 (USD)"}}, "dailyUsageByModel": {"total": "总计", "tooltipTotal": "总计: $ {{value}}", "switchTo": "切换到", "cost": "成本", "usage": "使用量", "perspective": "视角", "granularity": {"hour": "按小时", "day": "按天", "week": "按周", "month": "按月"}}, "checkinModal": {"title": "请完成验证", "captchaPlaceholder": "验证码", "confirm": "确定", "close": "关闭"}, "balanceTransfer": {"title": "账户间转账", "accountInfo": {"balance": "账户余额", "transferFee": "转账手续费", "groupNote": "仅相同的用户组之间可转账"}, "form": {"receiverId": "接收者ID", "receiverUsername": "接收者用户名", "remark": "备注信息", "amount": "转账金额", "expectedFee": "预计扣费", "submit": "发起转账"}, "result": {"success": "转账成功", "continueTransfer": "继续转账", "viewRecord": "查看记录"}, "warning": {"disabled": "管理员未开启转账功能，暂时无法使用"}, "placeholder": {"autoCalculate": "填写转账金额自动计算"}}, "channelsTable": {"title": "渠道管理", "columns": {"id": "ID", "name": "名称", "createdTime": "创建时间", "type": "类型", "key": "密钥", "base": "接口地址", "models": "模型", "weight": "权重", "priority": "优先级", "retryInterval": "重试间隔", "responseTime": "响应时间", "rpm": "RPM", "status": "状态", "quota": "余额", "expireTime": "过期时间", "group": "分组", "billingType": "计费类型", "actions": "操作", "fusing": "熔断", "sort": "优先级", "disableReason": "禁用原因"}, "status": {"all": "所有", "normal": "正常", "enabled": "正常状态", "manualDisabled": "手动禁用", "waitingRetry": "等待重启", "suspended": "暂停使用", "partiallyDisabled": "部分禁用", "specified": "指定状态", "allDisabled": "禁用", "specifiedDisabled": "指定禁用类型"}, "placeholder": {"selectGroup": "请选择/搜索分组", "selectStatus": "选择渠道状态", "inputSelectModel": "输入/选择模型名称", "selectFusingStatus": "选择自动熔断状态"}, "quota": {"usageAmount": "消耗：{amount}", "remainingAmount": "剩余：{amount}", "customTotalAmount": "自定义总额：{amount}", "updateNotSupported": "暂不支持更新余额，请使用自定义余额", "details": "详情", "sufficient": "充足"}, "actions": {"edit": "编辑", "copy": "克隆渠道", "delete": "删除渠道", "enable": "启用", "disable": "禁用", "test": "测试", "advancedTest": "高级测试", "viewLog": "渠道日志", "viewAbility": "查看能力", "cleanUsage": "清空已用", "updateBalance": "更新余额", "copyKey": "复制密钥"}, "confirm": {"deleteTitle": "删除确认", "deleteContent": "确定要删除渠道 {{name}}（#{{id}}）吗？", "cleanUsageTitle": "清空使用量确认", "cleanUsageContent": "确定要清空渠道 {{name}}（#{{id}}）的已消耗金额吗？", "testTitle": "确认测试", "testContent": "您确定要测试{{status}}的渠道吗？", "testNote": "注意：此功能需要配合[配置]->[中继]->[监控设置]->[失败时禁用渠道,成功时启用通道]来使用。如果没有开启相关设置，测试完成后不会自动禁用或启用渠道。", "deleteDisabledTitle": "删除确认", "deleteDisabledContent": "确定要删除所有{{type}}渠道吗？"}, "messages": {"operationSuccess": "操作成功", "operationSuccessWithSort": "操作成功，渠道排序可能有变化，建议按 ID 排序！", "operationFailed": "操作失败：{{message}}", "testRunning": "渠道 {{name}}(#{{id}}) 测试运行中，请稍等...", "testSuccess": "渠道「{{name}}(#{{id}})」{{model}}测试成功，响应时间 {{time}} 秒", "testSuccessWithWarnings": "渠道「{{name}}(#{{id}})」{{model}}测试完成，响应时间 {{time}} 秒，但有警告信息", "viewDetails": "查看详情", "testFailed": "渠道「{{name}}(#{{id}})」{{model}}测试失败。状态码：{{code}}，原因：{{reason}}，点击查看详情", "testStarted": "开始测试{{status}}的渠道，请稍后刷新查看结果。测试结果的应用取决于您的监控设置。", "testOperationFailed": "测试失败", "deleteSuccess": "成功删除{{count}}个渠道", "deleteFailed": "删除失败：{{message}}", "modelPrefix": "模型 {{model}} ", "channelInfo": "渠道信息", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "渠道「{{name}}」余额更新成功", "updateBalanceFailed": "渠道「{{name}}」余额更新失败：{{message}}", "updateAllBalanceStarted": "开始更新所有正常状态的渠道余额", "updateAllBalanceSuccess": "所有渠道余额更新成功", "fetchGroupError": "获取渠道组数据出错：{{response}}", "fetchChannelError": "获取渠道数据失败：{{message}}", "selectChannelFirst": "请先选择要删除的渠道", "deleteDisabledSuccess": "已删除所有{{type}}渠道，共 {{count}} 个", "deleteOperationFailed": "删除失败", "copySuccess": "复制成功", "copyFailed": "复制失败：{{message}}", "emptyKey": "密钥为空", "fetchChannelDetailError": "获取渠道详情失败：{{message}}", "topupSuccess": "充值成功", "topupFailed": "充值失败：{{message}}"}, "popover": {"channelInfo": "渠道信息"}, "menu": {"deleteManualDisabled": "删除手动禁用渠道", "deleteWaitingRetry": "删除等待重启渠道", "deleteSuspended": "删除暂停使用渠道", "deleteDisabledAccount": "删除停用账号", "deleteQuotaExceeded": "删除配额超限渠道", "deleteRateLimitExceeded": "删除频率限制渠道", "deleteInvalidKey": "删除无效密钥渠道", "deleteConnectionError": "删除连接错误渠道", "testAll": "测试所有渠道", "testNormal": "测试正常渠道", "testManualDisabled": "测试手动禁用渠道", "testWaitingRetry": "测试等待重启渠道", "testSuspended": "测试暂停使用渠道"}, "tooltip": {"testNote": "需要配合[配置]->[中继]->[监控设置]->[失败时禁用渠道,成功时启用通道]来使用。如果没有开启则不会在测速完成后自动禁用或者启用。"}, "disableReasons": {"account_deactivated": "账号停用", "quota_exceeded": "配额超限", "rate_limit_exceeded": "频率限制", "invalid_key": "无效密钥", "connection_error": "连接错误"}, "topup": {"reminder1": "上游充值成功后，请点击更新余额按钮刷新余额", "reminder2": "如充值失败，请检查兑换码是否正确或联系管理员"}}, "billingTypes": {"quota": "额度", "times": "次数"}, "serverLogViewer": {"title": "服务器日志查看器", "connecting": "正在连接服务器...", "downloadSelect": "选择日志文件下载", "nginxConfig": "Nginx WebSocket 配置说明", "directAccess": "如果使用域名访问且未配置 WebSocket 支持，日志查看器将无法工作。此时您可以通过服务器 IP 和端口直接访问（例如：http://your-ip:9527）。", "domainAccess": "若要通过域名访问，需要在 Nginx 配置中添加以下配置以支持 WebSocket：", "buttons": {"pause": "暂停", "resume": "继续", "clear": "清空"}, "errors": {"fetchFailed": "获取日志文件列表失败", "downloadFailed": "下载日志文件失败", "wsError": "WebSocket连接错误"}}, "channelScore": {"score": "得分", "successRate": "成功率", "avgResponseTime": "平均响应时间", "title": "渠道得分", "hourlyTitle": "渠道小时得分", "dailyTitle": "渠道日得分", "weeklyTitle": "渠道周得分", "monthlyTitle": "渠道月得分", "allTimeTitle": "渠道总体得分", "infoTooltip": "渠道得分是根据成功率和响应时间计算的综合评分", "tableView": "表格视图", "chartView": "图表视图", "refresh": "刷新", "selectModel": "选择模型", "allModels": "所有模型", "sortByScore": "按得分排序", "sortBySuccessRate": "按成功率排序", "sortByResponseTime": "按响应时间排序", "noData": "暂无数据", "totalItems": "共 {{total}} 项", "fetchError": "获取渠道得分数据失败", "aboutScoring": "关于得分计算", "scoringExplanation": "渠道得分是根据成功率和响应时间计算的综合评分，满分为1分。", "successRateWeight": "成功率权重 (70%)", "successRateExplanation": "成功率越高，得分越高", "responseTimeWeight": "响应时间权重 (30%)", "responseTimeExplanation": "响应时间低于1000ms获得满分，超过则按比例扣分", "columns": {"rank": "排名", "channelId": "渠道ID", "channelName": "渠道名称", "model": "模型", "totalRequests": "总请求数", "successRequests": "成功请求数", "failedRequests": "失败请求数", "successRate": "成功率", "avgResponseTime": "平均响应时间", "score": "综合得分", "actions": "操作"}, "actions": {"viewDetails": "查看详情", "test": "测试渠道", "edit": "编辑渠道"}, "tooltips": {"excellent": "优秀", "good": "良好", "average": "一般", "poor": "较差", "veryPoor": "很差"}, "scoringExplanation100": "渠道得分是根据成功率和响应时间计算的综合评分，满分为100分。"}, "menu": {"channelScores": "渠道得分"}, "relay": {"dispatchOptions": "调度选项", "preciseWeightCalculation": "权重精确计算", "preciseWeightCalculationTip": "启用后将使用更精确的算法计算渠道权重，可能会增加CPU开销", "channelMetricsEnabled": "启用渠道指标统计", "channelMetricsEnabledTip": "开启后会收集渠道的成功率、响应时间等指标，用于评估渠道性能。关闭则不会收集这些数据，可减少系统资源占用。", "channelScoreRoutingEnabled": "启用基于渠道得分的智能路由", "channelScoreRoutingEnabledTip": "开启后系统会根据渠道的历史表现自动调整请求分配优先级。性能更好的渠道将获得更高的请求分配概率。", "globalIgnoreBillingTypeFilteringEnabled": "全局忽略计费方式筛选", "globalIgnoreBillingTypeFilteringEnabledTip": "开启后将忽略按量计费与按次计费的区分，不再根据计费方式筛选渠道，降低CPU和内存占用。", "globalIgnoreFunctionCallFilteringEnabled": "全局忽略函数调用筛选", "globalIgnoreFunctionCallFilteringEnabledTip": "开启后将忽略函数调用(Function Call)能力的筛选，不再专门筛选支持函数调用的渠道，降低资源占用。", "globalIgnoreImageSupportFilteringEnabled": "全局忽略图片支持筛选", "globalIgnoreImageSupportFilteringEnabledTip": "开启后将忽略图片支持能力的筛选，不再专门筛选支持图片输入的渠道，降低资源占用。"}, "dynamicRouter": {"title": "动态路由管理", "reloadRoutes": "重新加载路由", "exportConfig": "导出配置", "clearConfig": "清空配置", "importantNotice": "重要提示", "reloadLimitation": "1. 重新加载路由只能更新现有路由的配置，无法添加或删除路由。如需完全重新加载路由结构，请重启应用。", "exportDescription": "2. 导出配置会将当前数据库中的配置导出到router.json文件，过滤掉空值和零值。", "clearDescription": "3. 清空配置会删除数据库中所有动态路由配置，重启应用后将从router.json文件重新加载。", "routeGroups": "路由组", "upstreamConfig": "上游配置", "endpointConfig": "端点配置", "editRouteGroup": "编辑路由组", "editUpstream": "编辑上游配置", "editEndpoint": "编辑端点配置", "editJSON": "编辑JSON", "confirmClear": "确认清空配置", "confirmClearMessage": "此操作将清空数据库中所有动态路由配置，下次重启应用后将从配置文件重新加载。确定要继续吗？", "configCleared": "动态路由配置已清空，请重启应用以应用更改", "configExported": "配置已成功导出到文件", "configReloaded": "路由配置已成功重新加载"}, "legal": {"privacyPolicy": {"title": "隐私政策", "lastUpdated": "最后更新日期：{{date}}", "sections": {"informationCollection": {"title": "信息收集", "description": "我们收集以下类型的信息：", "items": {"accountInfo": "账户信息：当您通过 Google 登录时，我们收集您的姓名、电子邮件地址和基本个人资料信息", "usageData": "使用数据：API 调用记录、使用统计和系统日志", "technicalInfo": "技术信息：IP 地址、浏览器类型、设备信息"}}, "informationUsage": {"title": "信息使用", "description": "我们使用收集的信息用于：", "items": ["提供和维护我们的服务", "用户身份验证和账户管理", "改进服务质量和用户体验", "发送重要的服务通知", "防止欺诈和滥用"]}, "informationSharing": {"title": "信息共享", "description": "我们不会出售、交易或转让您的个人信息给第三方，除非：", "items": ["获得您的明确同意", "法律要求或法院命令", "保护我们的权利、财产或安全"]}, "dataSecurity": {"title": "数据安全", "description": "我们采取适当的安全措施保护您的个人信息：", "items": ["数据加密传输和存储", "访问控制和权限管理", "定期安全审计和更新", "员工隐私培训"]}, "dataRetention": {"title": "数据保留", "description": "我们仅在必要期间保留您的个人信息：", "items": ["账户信息：账户存续期间", "使用日志：90天", "系统日志：30天"]}, "userRights": {"title": "您的权利", "description": "您有权：", "items": ["访问和更新您的个人信息", "删除您的账户和相关数据", "撤回同意", "数据可携带性"]}, "cookieUsage": {"title": "<PERSON><PERSON> 使用", "description": "我们使用 Cookie 和类似技术来：", "items": ["维持用户会话", "记住用户偏好", "分析网站使用情况"]}, "thirdPartyServices": {"title": "第三方服务", "description": "我们的服务可能包含第三方链接或集成：", "items": ["Google OAuth：用于用户身份验证", "GitHub OAuth：用于用户身份验证", "这些服务有自己的隐私政策"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "我们的服务不面向13岁以下儿童。我们不会故意收集儿童的个人信息。"}, "policyUpdates": {"title": "政策更新", "description": "我们可能会更新此隐私政策。重大变更将通过电子邮件或网站通知您。"}, "contactUs": {"title": "联系我们", "description": "如果您对此隐私政策有任何问题，请联系我们：", "email": "邮箱", "address": "地址"}}}, "termsOfService": {"title": "服务条款", "lastUpdated": "最后更新日期：{{date}}", "importantNotice": "使用我们的服务即表示您同意这些条款。请仔细阅读。", "sections": {"serviceDescription": {"title": "服务描述", "description": "Shell API Pro Max 是一个 API 管理和代理服务平台，提供：", "items": ["API 密钥管理", "API 调用代理和转发", "使用统计和监控", "用户账户管理", "相关技术支持服务"]}, "userAccount": {"title": "用户账户", "description": "使用我们的服务需要：", "items": ["通过 Google 或 GitHub 进行身份验证", "提供准确、完整的注册信息", "保护账户安全，不与他人共享", "及时更新账户信息", "对账户下的所有活动负责"]}, "usageRules": {"title": "使用规则", "description": "您同意：", "items": ["合法使用：仅用于合法目的", "不滥用：不进行恶意攻击或过度请求", "不侵权：不侵犯他人知识产权", "不传播有害内容：不传播病毒、恶意软件", "遵守限制：遵守 API 调用限制和配额"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "以下行为被严格禁止：", "items": ["尝试未经授权访问系统", "干扰或破坏服务正常运行", "逆向工程、反编译服务", "创建虚假账户或身份", "违反任何适用法律法规"]}, "serviceAvailability": {"title": "服务可用性", "description": "我们努力提供稳定的服务，但：", "items": ["不保证 100% 的服务可用性", "可能因维护、升级而暂停服务", "可能因不可抗力而中断服务", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "费用和付款", "description": "关于服务费用：", "items": ["基础服务可能免费提供", "高级功能可能需要付费", "费用标准在网站上公布", "付款后不可退款（法律要求除外）"]}, "intellectualProperty": {"title": "知识产权", "description": "关于知识产权：", "items": ["服务及其内容受知识产权法保护", "您获得有限的使用许可", "不得复制、修改、分发我们的内容", "您保留对自己数据的权利"]}, "privacyProtection": {"title": "隐私保护", "description": "我们重视您的隐私：", "items": ["按照隐私政策处理您的信息", "采取合理措施保护数据安全", "不会未经同意分享您的信息"]}, "disclaimer": {"title": "免责声明", "description": "在法律允许的范围内：", "items": ["服务按\"现状\"提供", "不保证服务无错误或中断", "不对间接损失承担责任", "责任限制在您支付的费用范围内"]}, "serviceTermination": {"title": "服务终止", "description": "服务可能在以下情况终止：", "items": ["您违反这些条款", "您要求删除账户", "我们停止提供服务", "法律要求"]}, "termsModification": {"title": "条款修改", "description": "我们可能会修改这些条款：", "items": ["重大变更会提前通知", "继续使用服务表示接受新条款", "如不同意，请停止使用服务"]}, "disputeResolution": {"title": "争议解决", "description": "如发生争议：", "items": ["首先尝试友好协商", "适用中华人民共和国法律", "由服务提供方所在地法院管辖"]}, "contactUs": {"title": "联系我们", "description": "如果您对这些条款有任何问题，请联系我们：", "email": "邮箱", "address": "地址", "serviceHours": "客服时间：工作日 9:00-18:00"}}}, "common": {"copyright": "© {{year}} Shell API Pro Max. 保留所有权利。", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "[您的公司地址]"}}}