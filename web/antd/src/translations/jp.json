{"message": {"copyModelSuccess": "モデル名がクリップボードにコピーされました！", "copyFailed": "コピーに失敗しました。手動でコピーしてください。", "logoutSuccess": "ログアウト成功", "loginSuccess": {"default": "ログイン成功", "welcomeBack": "おかえりなさい"}, "removeLocalStorage": {"confirm": "ローカルキャッシュをクリアしますか？", "success": "ローカルキャッシュのクリア成功"}, "loadData": {"error": "{{name}}データの読み込みに失敗しました"}, "noNotice": "現在お知らせ内容はありません", "verification": {"turnstileChecking": "Turnstileがユーザー環境をチェックしています！", "pleaseWait": "しばらくしてから再試行してください"}, "clipboard": {"inviteCodeDetected": "招待コードが検出され、自動入力されました！", "clickToCopy": "クリックしてコピー", "copySuccess": "コピー成功"}}, "common": {"yes": "はい", "no": "いいえ", "copyAll": "すべてコピーする", "all": "すべて", "more": "もっと", "unlimited": "無制限", "enabled": "有効", "disabled": "無効", "save": "保存", "cancel": "キャンセル", "create": "作成", "usd": "ドル", "day": "{{count}} 日", "day_plural": "{{count}} 日", "days": "日", "seconds": "秒", "times": "回", "submit": "送信", "bind": "バインディング", "unknown": "不明", "loading": "読み込み中...", "copyFailed": "コピー失敗", "people": "人", "ok": "OK", "close": "閉じる", "copied": "コピーしました。", "expand": "展開", "collapse": "折りたたむ", "none": "なし", "remark": "備考", "selectPlaceholder": "{{name}}を選択してください", "on": "オン", "off": "オフ", "name": "識別子", "displayName": "表示名", "description": "説明", "ratio": "倍率", "unnamed": "名前なしチャンネル", "groups": "グループ", "captchaPlaceholder": "認証コードを入力してください", "confirm": "確認", "permissions": "権限", "actions": "操作", "createdTime": "作成時間", "expiredTime": "有効期限", "search": "検索", "reset": "リセット", "refresh": "更新", "pagination": {"total": "第 {{start}} - {{end}} 件、合計 {{total}} 件"}, "delete": "削除", "edit": "Text", "add": "追加", "update": "Time", "back": "戻る", "next": "Text", "previous": "前へ", "success": "Success", "error": "エラー", "warning": "Warning", "info": "情報"}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "クリックしてリンクを開く"}, "userRole": {"normal": "一般ユーザー", "agent": "代理店", "admin": "管理者", "superAdmin": "スーパー管理者", "loading": "読み込み中..."}, "channelStatus": {"enabled": "有効", "disabled": "無効", "waitingRestart": "再起動待ち", "waiting": "待機中", "autoStoppedTitle": "チャンネル自動再試行が最大回数を超えたか、自動停止条件がトリガーされました", "stopped": "停止", "partiallyDisabled": "部分的に無効", "unknown": "不明", "reason": "理由"}, "channelBillingTypes": {"payAsYouGo": "従量課金", "payPerRequest": "リクエスト課金", "unknown": "不明な方式"}, "tokenStatus": {"normal": "正常", "disabled": "無効", "expired": "期限切れ", "exhausted": "使い切り", "unknown": "不明"}, "userStatus": {"normal": "正常", "banned": "禁止", "unknown": "不明"}, "redemptionStatus": {"normal": "正常", "disabled": "Status", "redeemed": "Status", "expired": "期限切れ", "unknown": "Status"}, "duration": {"request": "リクエスト", "firstByte": "Text", "total": "合計", "seconds": "Text", "lessThanOneSecond": "Text"}, "streamType": {"stream": "ストリーミング", "nonStream": "Text"}, "noSet": {"title": "Title", "name": {"about": "について", "chat": "Title"}}, "buttonText": {"add": "追加", "cancel": "キャンセル", "confirm": "Action", "delete": "削除", "edit": "Action", "save": "保存する", "updateBalance": "Action", "test": "テスト", "multiple": "Action"}, "channelPage": {"title": "Title"}, "channelStatusCount": {"title": "Title", "summary": "Text", "statusEnabled": "Status", "statusDisabled": "Status", "statusRetry": "Status", "statusStopped": "停止しました", "statusPartially": "Status"}, "header": {"routes": {"status": "状態", "home": "ホーム", "chat": "Title", "pptGen": "Title", "chart": "統計", "agency": "Title", "channel": "チャネル", "ability": "Title", "channelGroup": "チャネルグループ", "token": "トークン", "log": "ログ", "logDetail": "Title", "midjourney": "Title", "user": "ユーザー", "config": "Title", "packagePlanAdmin": "セットメニュー", "redemption": "Title", "group": "Title", "query": "Text", "about": "について", "setting": {"default": "Title", "operation": "Title", "system": "Title", "global": "全体設定", "advance": "Title", "sensitive": "Title", "verification": "Title", "update": "Text"}, "account": {"default": "アカウント", "profile": "Title", "cardTopup": "Title", "onlineTopup": "オンラインチャージ", "recharge": "Title", "balanceTransfer": "Title", "pricing": "Title", "packagePlan": {"list": "セット購入", "record": "Title"}, "notificationSettings": "通知設定"}, "tools": {"default": "Title", "fileUpload": "ファイルのアップロード", "keyExtraction": "Action", "multiplierCalculator": "Title", "shortLink": "Title", "testConnection": "アクセステスト", "customPrompts": "Title", "redis": "Title", "ratioCompare": "Text", "serverLog": "サーバーログビューア"}, "onlineTopupRecord": "Title", "channelScores": "チャネルスコア", "dynamicRouter": "Title", "task": "Title", "agencyJoin": "Title"}, "dropdownMenu": {"profile": "個人センター", "recharge": "Title", "agencyCenter": "Title", "checkin": "チェックイン", "darkMode": {"enable": "ダークモード", "disable": "デイモード"}, "fullScreen": {"default": "Title", "enable": "全画面モード", "disable": "Title"}, "logout": "ログアウト"}, "checkin": {"default": "チェックイン", "success": "Title", "failed": "サインインに失敗しました。", "verification": "Title"}, "avatarProps": {"login": "ログイン"}}, "settings": {"public": {"titles": {"default": "Title"}, "SystemName": "Title", "ServerAddress": "サービスアドレス", "TopUpLink": "チャージリンク", "ChatLink": "Text", "Logo": "システムロゴ", "HomePageContent": "Description", "About": "内容について", "Notice": "Message", "Footer": "Text", "RegisterInfo": "登録通知", "HeaderScript": "カスタムヘッダー", "SiteDescription": "Description", "PrivacyPolicy": "プライバシーポリシー", "ServiceAgreement": "Text", "FloatButton": {"FloatButtonEnabled": "開く", "DocumentInfo": "Action", "WechatInfo": "微信メッセージ", "QqInfo": "Action"}, "CustomThemeConfig": "カスタムテーマ", "AppList": "Text"}}, "home": {"default": {"title": "Title", "subtitle": "Title", "start": "Text", "description": {"title": "Title", "part1": "Description", "part2": "Description", "part3": "Description", "part4": "Description"}}}, "dailyUsageChart": {"title": "Title", "yAxisName": "使用量 (USD)", "loadingTip": "Tip", "fetchError": "Error"}, "modelUsageChart": {"title": "Title", "hourlyTitle": "Title", "dailyTitle": "Title", "weeklyTitle": "Title", "monthlyTitle": "Text"}, "granularity": {"hour": "Text", "day": "毎日", "week": "Text", "month": "毎月", "all": "すべて"}, "abilitiesTable": {"title": "Title", "export": "エクスポート", "group": "グループ", "model": "モデル", "channelId": "Text", "enabled": "有効になりました", "weight": "Text", "priority": "Text", "billingType": "請求タイプ", "functionCallEnabled": "Text", "imageSupported": "画像をサポートする", "yes": "はい", "no": "いいえ", "perToken": "Text", "perRequest": "Text", "noDataToExport": "データをエクスポートできません。", "exportConfirm": "Action", "exportSuccess": "Success", "toggleSuccess": "Text", "toggleError": "Error", "selectOrInputGroup": "Option"}, "logsTable": {"retry": "再試行", "retryChannelList": "Text", "retryDurations": "Text", "channel": "チャネル", "duration": "Text", "startTime": "Time", "endTime": "終了時間", "retryCount": "Text", "retryDetails": "Text", "totalRetryTime": "Time", "seconds": "秒", "tokenGroup": "トークングループ", "selectGroup": "Option", "dailyModelUsageStats": "Text", "time": "時間", "moreInfo": "Description", "ip": "IP", "remoteIp": "リモートIP", "ipTooltip": "IP: {{ip}}  \nリモート IP: {{remoteIp}}", "requestId": "リクエスト ID", "username": "Title", "userId": "ユーザーID", "tokenName": "Title", "token": "トークン", "type": "タイプ", "typeUnknown": "Text", "type充值": "チャージ", "type消费": "Text", "type管理": "管理", "type系统": "システム", "type邀请": "Text", "type提示": "ヒント", "type警告": "Text", "type错误": "エラー", "type签到": "チェックイン", "type日志": "ログ", "type退款": "Text", "type邀请奖励金划转": "Text", "type代理奖励": "代理報酬", "type下游错误": "ダウンストリームエラー", "type测试渠道": "テストチャネル", "typeRecharge": "チャージ", "typeConsumption": "Text", "typeManagement": "管理", "typeSystem": "システム", "typeInvitation": "Text", "typePrompt": "ヒント", "typeWarning": "Warning", "typeError": "エラー", "typeCheckin": "チェックイン", "typeLog": "ログ", "typeRefund": "Text", "typeInviteReward": "Text", "typeAgencyBonus": "代理報酬", "typeDownstreamError": "ダウンストリームエラー", "typeChannelTest": "テストチャネル", "channelId": "チャネルID", "channelName": "Title", "model": "モデル", "modelPlaceholder": "Please enter...", "info": "情報", "isStream": "ストリーミング", "isStreamPlaceholder": "Please enter...", "prompt": "ヒント", "completion": "Text", "consumption": "消費", "consumptionRange": "Text", "description": "説明", "action": "Action", "details": "詳細", "tokenKey": "トークンキー", "requestDuration": "Text", "firstByteDuration": "Text", "totalDuration": "Text", "lessThanOneSecond": "<1秒", "modelInvocation": "Text", "modelUsage": "Text", "totalQuota": "総消費限度額: {{quota}}", "totalRpm": "Text", "totalTpm": "Text", "totalMpm": "Text", "dailyEstimate": "Text", "currentStats": "Text", "statsTooltip": "Tip", "showAll": "Text", "exportConfirm": "このページのログをエクスポートしますか？", "export": "エクスポート", "statsData": "Text", "today": "当日", "lastHour": "Text", "last3Hours": "3時間", "lastDay": "Text", "last3Days": "3日", "last7Days": "Text", "lastMonth": "1ヶ月", "last3Months": "Text", "excludeModels": "除外モデル", "selectModelsToExclude": "Option", "excludeErrorCodes": "Error", "excludeErrorCodesPlaceholder": "Error", "errorCode": "エラーコード", "errorCodePlaceholder": "Error", "timezoneTip": "現在のタイムゾーン: {timezone}", "timezoneNote": "タイムゾーンのヒント", "timezoneDescription": "Text", "goToProfile": "Text", "realtimeQuota": "Time", "viewTotalQuota": "Text", "viewTotalQuotaTip": "Text", "loadingTotalQuota": "Text", "totalQuotaTitle": "Title", "loadTotalQuotaError": "Error", "requestLogs": "リクエストログ - {{requestId}}", "noRequestLogs": "リクエストログはありません。", "metricsExplanation": "Text", "autoRefresh": "Text", "autoRefreshTip": "Text", "autoRefreshOn": "Text", "autoRefreshOff": "Text", "refreshInterval": "更新間隔", "stopRefresh": "Text", "secondsWithValue": "Text", "minutesWithValue": "{{minutes}}分", "title": "Title", "columns": {"id": "ID", "createdTime": "Time", "type": "タイプ", "content": "コンテンツ", "username": "Title", "tokenName": "トークン名", "modelName": "Title", "quota": "クォータ", "promptTokens": "プロンプトトークン", "completionTokens": "Text"}, "actions": {"view": "表示", "retry": "Action"}, "status": {"success": "成功", "failed": "Error"}, "messages": {"retrySuccess": "Message", "retryFailed": "再試行失敗"}}, "mjLogs": {"logId": "ログID", "submitTime": "Action", "type": "タイプ", "channelId": "チャネルID", "userId": "ユーザーID", "taskId": "タスクID", "submit": "Action", "status": "状態", "progress": "Text", "duration": "所要時間", "result": "Text", "prompt": "プロンプト", "promptEn": "Text", "failReason": "失敗理由", "startTime": "Time", "endTime": "終了時間", "today": "Text", "lastHour": "Text", "last3Hours": "3時間", "lastDay": "Text", "last3Days": "3日", "last7Days": "Text", "lastMonth": "1ヶ月", "last3Months": "Text", "selectTaskType": "Option", "selectSubmitStatus": "Text", "submitSuccess": "Action", "queueing": "並んでいます。", "duplicateSubmit": "Action", "selectTaskStatus": "Status", "success": "成功", "waiting": "Text", "queued": "Text", "executing": "実行", "failed": "Error", "seconds": "秒", "unknown": "Text", "viewImage": "Text", "markdownFormat": "Text", "midjourneyTaskId": "Midjourney タスクID", "copiedAsMarkdown": "Text", "copyFailed": "コピーに失敗しました。", "copiedMidjourneyTaskId": "MidjourneyのタスクIDがコピーされました。", "drawingLogs": "Text", "onlyUnarchived": "Text", "imagePreview": "画像プレビュー", "copiedImageUrl": "Text", "copy": "コピー", "download": "ダウンロード", "resultImage": "Text", "downloadError": "Error", "mode": "モード", "selectMode": "Option", "relax": "リラックスモード", "fast": "クイックモード", "turbo": "スピードモード", "actions": "操作", "refresh": "リフレッシュ", "videoUrls": "ビデオURLリスト"}, "mjTaskType": {"IMAGINE": "Text", "UPSCALE": "拡大", "VARIATION": "Text", "REROLL": "Text", "DESCRIBE": "Text", "BLEND": "混合画像", "OUTPAINT": "ズーム", "DEFAULT": "Text"}, "mjCode": {"submitSuccess": "Action", "queueing": "並んでいます。", "duplicateSubmit": "Action", "unknown": "Text"}, "mjStatus": {"success": "成功", "waiting": "Status", "queued": "Status", "executing": "実行", "failed": "Error", "unknown": "未知"}, "tokensTable": {"title": "Title", "table": {"title": "トークン管理", "toolBar": {"add": "Text", "delete": "Text", "deleteConfirm": "Text", "export": "エクスポート", "exportConfirm": "Action"}, "action": "操作"}, "modal": {"title": {"add": "Title", "edit": "Title"}, "field": {"name": "Title", "description": "Description", "type": {"default": "請求方式", "type1": "Text", "type2": "Text", "type3": "混合料金", "type4": "Text", "type5": "次優先で"}, "status": "Status", "statusEnabled": "正常", "statusDisabled": "Status", "statusExpired": "Status", "statusExhausted": "消耗尽くす", "models": "モデル", "usedQuota": "Text", "remainQuota": "残高", "createdTime": "Time", "expiredTime": "有効期限", "all": "すべて", "more": "もっと", "notEnabled": "Text", "unlimited": "無制限", "daysLeft": "Text", "expired": "Text", "userId": "ユーザーID", "key": "APIキー", "neverExpire": "Text", "quota": "クォータ", "unlimitedQuota": "Text", "unlimitedExpired": "無期限"}, "delete": {"title": "Title", "content": "Description"}, "footer": {"cancel": "キャンセル", "confirm": "Action", "update": "更新"}, "bridge": {"title": "Title", "placeholder": "Please enter..."}, "copy": {"title": "手動コピー"}}, "dropdown": {"onlineChat": "Text", "disableToken": "Text", "enableToken": "トークンを有効にする", "editToken": "Text", "requestExample": "Text", "tokenLog": "トークンログ", "shareToken": "Text", "quickIntegration": "Text"}, "error": {"fetchModelsFailed": "Error", "batchDeleteFailed": "Error", "deleteTokenFailed": "Error", "refreshTokenFailed": "Error", "enableTokenFailed": "Error", "disableTokenFailed": "Error", "fetchDataFailed": "Error", "createTokenFailed": "Error", "updateTokenFailed": "トークン更新失敗：{{message}}"}, "success": {"batchDelete": "Success", "shareTextCopied": "共有されたテキストはクリップボードにコピーされました。", "tokenCopied": "トークンがクリップボードにコピーされました。", "deleteToken": "Success", "refreshToken": "Success", "enableToken": "Success", "disableToken": "Success", "export": "Success", "createToken": "トークン作成成功", "updateToken": "Success"}, "warning": {"copyFailed": "Error", "invalidServerAddress": "Warning"}, "info": {"openingBridgePage": "Text"}, "export": {"name": "Title", "key": "鍵", "billingType": "Text", "status": "状態", "models": "Text", "usedQuota": "Text", "remainQuota": "残高", "createdTime": "Time", "expiredTime": "期限", "unlimited": "Text", "neverExpire": "Text", "filename": "トークンリスト", "success": "Success", "failed": "エクスポート失敗"}, "billingType": {"1": "Text", "2": "Text", "3": "混合料金", "4": "Text", "5": "次優先で"}, "bridge": {"quickIntegration": "Text"}, "columns": {"name": "名前", "status": "ステータス", "quota": "クォータ", "usedQuota": "Text", "remainingQuota": "残りクォータ", "accessedTime": "Time", "expiredTime": "有効期限"}, "actions": {"copy": "コピー", "enable": "Action", "disable": "無効化"}, "messages": {"copySuccess": "Message", "enableSuccess": "有効化成功", "disableSuccess": "Message", "deleteSuccess": "削除成功"}}, "editTokenModal": {"editTitle": "Title", "createTitle": "Title", "defaultTokenName": "{{username}}のトークン {{date}}", "tokenName": "Title", "unlimitedQuota": "Text", "remainingQuota": "残高", "authorizedQuota": "Text", "quotaLimitNote": "Text", "quickOptions": "ショートカットオプション", "neverExpire": "Text", "expiryTime": "期限", "billingMode": "Text", "selectGroup": "Option", "switchGroup": "グループを選択する", "switchGroupTooltip": "Tip", "switchGroupHint": "Text", "importantFeature": "Text", "tokenRemark": "Text", "discordProxy": "Discord プロキシ", "enableAdvancedOptions": "Option", "generationAmount": "Text", "availableModels": "利用可能なモデル", "selectModels": "Text", "activateOnFirstUse": "Text", "activateOnFirstUseTooltip": "Tip", "activationValidPeriod": "Text", "activationValidPeriodTooltip": "Tip", "ipWhitelist": "IPホワイトリスト", "ipWhitelistPlaceholder": "Text", "rateLimiter": "リミッター", "rateLimitPeriod": "Text", "rateLimitPeriodTooltip": "Tip", "rateLimitCount": "制限回数", "rateLimitCountTooltip": "Tip", "promptMessage": "ヒントメッセージ", "promptMessageTooltip": "Message", "promotionPosition": "Text", "promotionPositionStart": "始まり", "promotionPositionEnd": "Text", "promotionPositionRandom": "ランダム", "promotionContent": "プロモーションコンテンツ", "currentGroup": "Text", "searchGroupPlaceholder": "Please enter...", "mjTranslateConfig": "Text", "mjTranslateConfigTip": "Tip", "mjTranslateBaseUrlPlaceholder": "Text", "mjTranslateApiKeyPlaceholder": "Please enter...", "mjTranslateModelPlaceholder": "Please enter...", "mjTranslateBaseUrlRequired": "Text", "mjTranslateApiKeyRequired": "Text", "mjTranslateModelRequired": "Text"}, "addTokenQuotaModal": {"title": "トークン残高管理{{username}}", "defaultReason": "Text", "enterRechargeAmount": "Text", "enterRemark": "Text", "confirmOperation": "Text", "confirmContent": "Action", "recharge": "チャージ", "deduct": "Text", "andUpdateExpiry": "Time", "alertMessage": "Message", "rechargeAmount": "チャージ額", "operationReason": "Text", "finalBalance": "最終残高"}, "billingType": {"1": "Text", "2": "Text", "3": "混合料金", "4": "Text", "5": "次優先で", "payAsYouGo": "Text", "payPerRequest": "Text", "hybrid": "混合料金", "payAsYouGoPriority": "Text", "payPerRequestPriority": "次優先で", "unknown": "Text"}, "packagePlanAdmin": {"title": "セットメニュー", "table": {"title": "Title", "toolBar": {"add": "Text", "delete": "プランを削除する"}, "action": {"edit": "Action", "delete": "削除", "detail": "Action", "recovery": "上架", "offline": "Action"}}, "modal": {"title": {"add": "Title", "edit": "編集パッケージ"}, "field": {"name": "Title", "type": {"default": "Text", "type1": "限度額パッケージ", "type2": "Text", "type3": "時間パッケージ"}, "group": "パッケージグループ", "description": "Description", "price": "セット料金", "valid_period": "Text", "first_buy_discount": "Text", "rate_limit_num": "制限回数", "rate_limit_duration": "Text", "inventory": "Text", "available_models": "利用可能なモデル", "quota": "Text", "times": "セット回数"}, "footer": {"cancel": "キャンセル", "confirm": "Action", "update": "更新"}}}, "login": {"title": "ログイン", "username": "Title", "password": "パスワード", "login": "ログイン", "otherLoginMethods": "Text", "register": "Text", "accountLogin": "アカウントログイン", "phoneLogin": "Text", "usernamePlaceholder": "Title", "usernameRequired": "Title", "passwordPlaceholder": "パスワード", "passwordRequired": "Text", "passwordMaxLength": "Text", "phonePlaceholder": "携帯電話番号", "phoneRequired": "Text", "phoneFormatError": "Error", "smsCodePlaceholder": "SMS認証コード", "smsCodeCountdown": "Text", "getSmsCode": "Text", "agreementText": "Text", "privacyPolicy": "《プライバシーポリシー》", "and": "Text", "serviceAgreement": "Text", "alreadyLoggedIn": "ログインしました。", "weakPasswordWarning": "Warning", "welcomeMessage": "ようこそご利用ください。", "captchaError": "Error", "credentialsError": "Error", "resetPassword": "パスワードをリセットする", "captchaExpired": "Text", "loginFailed": "Error", "captchaRequired": "Text", "captchaPlaceholder": "Please enter...", "smsSent": "Text", "smsSendFailed": "Error", "agreementWarning": "Warning", "turnstileWarning": "Text", "loginSuccess": "Success", "rememberMe": "Text", "forgotPassword": "Text", "loginButton": "ログイン", "noAccount": "Text", "signUp": "新規登録"}, "register": {"title": "Title", "usernameRequired": "Title", "usernameNoAt": "Title", "usernameNoChinese": "Title", "usernameLength": "Title", "usernamePlaceholder": "ユーザー名", "passwordRequired": "Text", "passwordLength": "Text", "passwordPlaceholder": "パスワード", "confirmPasswordRequired": "Action", "passwordMismatch": "Text", "confirmPasswordPlaceholder": "パスワードを確認する", "emailInvalid": "Text", "emailRequired": "Text", "emailPlaceholder": "メールアドレス", "emailCodeRequired": "Text", "emailCodePlaceholder": "メール認証コード", "enterCaptcha": "Text", "resendEmailCode": "Text", "getEmailCode": "Text", "phoneRequired": "Text", "phoneInvalid": "Text", "phonePlaceholder": "携帯電話番号", "smsCodeRequired": "Text", "smsCodePlaceholder": "SMS認証コード", "resendSmsCode": "Text", "getSmsCode": "Text", "captchaRequired": "Text", "captchaPlaceholder": "確認コード", "inviteCodePlaceholder": "Please enter...", "submit": "登録", "successMessage": "Message", "failMessage": "登録失敗", "emailCodeSent": "Text", "smsCodeSent": "Text", "confirm": "確認", "emailVerifyTitle": "Title", "smsVerifyTitle": "SMS認証", "registerVerifyTitle": "Title", "confirmPassword": "Action", "email": "メールアドレス", "inviteCode": "Text", "registerButton": "登録", "hasAccount": "Text", "signIn": "ログイン", "agreement": "Text", "termsOfService": "Text"}, "profile": {"timezone": "タイムゾーン", "phoneNumber": "Text", "emailAddress": "メールアドレス", "wechatAccount": "WeChatアカウント", "telegramAccount": "Telegramアカウント", "bindTelegram": "Telegramをバインドする", "balanceValidPeriod": "Text", "lastLoginIP": "最終ログインIP", "lastLoginTime": "Time", "inviteCode": "招待コード", "inviteLink": "Text", "generate": "生成", "pendingEarnings": "Text", "transfer": "移転", "totalEarnings": "Text", "accountBalance": "Text", "totalConsumption": "累計消費", "callCount": "Text", "invitedUsers": "User", "promotionInfo": "Description", "inviteDescription": "Text", "userInfo": "Description", "availableModels": "Text", "modelNameCopied": "モデル名がコピーされました。", "noAvailableModels": "Text", "accountOptions": "アカウントオプション", "changePassword": "Text", "systemToken": "システムトークン", "unsubscribe": "Text", "educationCertification": "Text", "timezoneUpdateSuccess": "Success", "inviteLinkCopied": "招待リンクがコピーされました。", "inviteLinkCopyFailed": "Error", "inviteLinkGenerationFailed": "Error", "allModelsCopied": "すべてのモデルがクリップボードにコピーされました。", "copyAllModels": "すべてのモデルをコピーする", "totalModels": "Text", "expired": "期限切れ", "validPeriod": "Text", "longTermValid": "長期有効", "failedToLoadModels": "Error", "accessTokens": "アクセストークン", "accessTokensManagement": "Text", "accessTokenDescription": "Text", "tokenNameLabel": "Title", "tokenNamePlaceholder": "Title", "presetPermissions": "プリセット権限", "detailPermissions": "Text", "validityPeriod": "Text", "validityPeriodExtra": "Text", "remarkLabel": "備考", "remarkPlaceholder": "Please enter...", "createToken": "トークン作成", "editToken": "Text", "deleteToken": "トークン削除", "copyToken": "トークンコピー", "tokenCreated": "Text", "tokenUpdated": "トークン更新成功", "tokenDeleted": "Text", "tokenCopied": "トークンをクリップボードにコピーしました", "deleteTokenConfirm": "Action", "disableTokenConfirm": "アクセストークン「{{name}}」を無効にしてもよろしいですか？", "enableTokenConfirm": "Action", "tokenSecurityWarning": "Text", "tokenPermissionTip": "Tip", "tokenExpiryWarning": "Warning", "tokenExpired": "トークンは期限切れです", "tokenNeverExpires": "Text", "tokenLastUsed": "Text", "tokenNeverUsed": "未使用", "tokenUsageCount": "Text", "tokenCreatedAt": "作成時間", "tokenStatus": "ステータス", "tokenActive": "アクティブ", "tokenInactive": "Text", "tokenDisabled": "Text", "permissions": {"read": "Text", "write": "Text", "admin": "管理者権限", "channels": "Text", "tokens": "トークン管理", "logs": "Text", "users": "User", "settings": "システム設定", "billing": "Text", "analytics": "Text", "api": "APIアクセス", "webhook": "Text"}, "createNewToken": "新しいトークンを作成", "tokenCreatedSuccess": "Success", "tokenSavePrompt": "Text", "readPermission": "読み取り権限", "writePermission": "Text", "deletePermission": "Text", "tokenManagement": "Text", "channelManagement": "チャネル管理", "logView": "Text", "statisticsView": "統計情報", "userManagement": "User", "quotaManagement": "クォータ管理", "readOnlyPermission": "Text", "writeOnlyPermission": "Text", "readWritePermission": "Text", "standardPermission": "Text", "fullPermission": "完全権限", "selectPermission": "Option", "tokenEnabled": "Text", "enableToken": "Text", "disableToken": "禁用", "tokenExpiryNever": "Text", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Description", "noPermission": "この操作を実行する権限がありません"}, "topup": {"onlineRecharge": "オンラインチャージ", "cardRedemption": "Text", "accountBalance": "口座残高", "rechargeReminder": "チャージリマインダー", "reminder1": "Text", "reminder2": "Text", "reminder3": "Text", "reminder4WithTransfer": "Text", "reminder4WithoutTransfer": "Text", "days": "天", "paymentSuccess": "Success", "paymentError": "支払いエラー", "paymentAmount": "Text", "purchaseAmount": "Text", "yuan": "元", "or": "または", "usd": "アメリカドル", "cny": "Text", "enterAmount": "Text", "amountPlaceholder": "Please enter...", "amountUpdateError": "Error", "alipay": "アリペイ", "wechat": "ウィーチャット", "visaMastercard": "ビザ / マスターカード", "cardFormatError": "Error", "redeemSuccess": "Success", "redeemError": "Error", "enterCardKey": "Text", "cardKeyPlaceholder": "Please enter...", "buyCardKey": "Text", "redeem": "即時消込", "record": {"title": "Title", "amount": "チャージ額", "payment": "Text", "paymentMethod": "Text", "orderNo": "注文番号", "status": "Status", "createTime": "Time", "statusSuccess": "成功", "statusPending": "Status", "statusFailed": "失敗"}, "paymentMethodAlipay": "アリペイ", "paymentMethodWxpay": "ウィーチャット", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "ペイパル", "paymentMethodAdmin": "Text", "paymentMethodRedeem": "交換コード", "alipayF2F": "Text"}, "pricing": {"fetchErrorMessage": "Text", "availableModelErrorMessage": "Message", "modelName": "モデル名", "billingType": "Text", "price": "価格", "ratio": "Text", "promptPriceSame": "Text", "completionPriceSame": "Text", "promptPrice": "提示価格：$ {{price}} / 1M トークン", "completionPrice": "Text", "promptRatioSame": "Text", "completionRatioSame": "Text", "promptRatio": "提示倍率：{{ratio}}", "completionRatio": "Text", "payAsYouGo": "従量課金 - チャット", "fixedPrice": "Text", "payPerRequest": "Text", "dynamicPrice": "$ {{price}} / 回", "payPerRequestAPI": "Text", "loadingTip": "Tip", "userGroupRatio": "User", "readFailed": "Text", "billingFormula": "Text", "billingFormula1": "Text", "generatedBy": "Text", "modalTitle": "価格詳細", "perMillionTokens": "/1Mトークン", "close": "Text", "searchPlaceholder": "Please enter...", "viewGroups": "Text", "copiedToClipboard": "クリップボードにコピーされました", "copyFailed": "Error", "groupName": "グループ名", "availableGroups": "Text", "noGroupsAvailable": "利用可能なグループがありません。", "modelGroupsErrorMessage": "Message", "currentGroup": "現在のグループ", "copyModelName": "Title", "groupRatio": "Text", "closeModal": "閉じる", "groupsForModel": "Text", "actions": "操作", "filterByGroup": "グループによるフィルタリング", "groupSwitched": "Text", "showAdjustedPrice": "Text"}, "guestQuery": {"usageTime": "使用時間", "modelName": "Title", "promptTooltip": "入力消費トークン", "completionTooltip": "Tip", "quotaConsumed": "Text", "pasteConfirm": "Action", "queryFailed": "クエリに失敗しました。", "tokenExpired": "Text", "tokenExhausted": "Text", "invalidToken": "Text", "focusRequired": "Text", "queryFirst": "まずは確認してください。", "tokenInfoText": "Text", "unlimited": "Text", "neverExpire": "Text", "infoCopied": "Description", "copyFailed": "コピーに失敗しました。", "noDataToExport": "データをエクスポートできません。", "prompt": "ヒント", "completion": "Text", "disabled": "Text", "tokenQuery": "トークンのクエリ", "tokenPlaceholder": "Please enter...", "tokenInfo": "トークン情報", "copyInfo": "Description", "totalQuota": "Text", "usedQuota": "トークン消費", "remainQuota": "Text", "callCount": "Text", "validUntil": "Text", "currentRPM": "現在のRPM", "currentTPM": "Text", "callLogs": "Text", "exportLogs": "ログをエクスポートする"}, "agencyProfile": {"fetchError": "Error", "fetchCommissionError": "Error", "systemPreset": "システムプリセット", "lowerRatioWarning": "Warning", "lowerRatioMessage": "Text", "cancelRatioEdit": "Text", "updateSuccess": "Success", "updateError": "Error", "updateFailed": "更新失敗", "customPriceUpdateSuccess": "Success", "customPriceUpdateError": "Error", "time": "時間", "type": "タイプ", "agencyCommission": "Text", "unknownType": "未知のタイプ", "amount": "Text", "balance": "残高", "description": "Description", "group": "Text", "customRate": "Text", "systemDefaultRate": "Text", "action": "操作", "save": "Text", "cancel": "キャンセル", "edit": "Text", "agencyConsole": "Text", "agencyInfo": "Description", "editInfo": "編集情報", "agencyName": "Title", "agencyLevel": "代理店のランク", "level1": "レベル1", "subordinateUsers": "User", "totalSales": "Text", "commissionIncome": "Text", "cumulativeEarnings": "累積収益", "agencyFunctions": "Text", "hideSubordinateUsers": "User", "viewSubordinateUsers": "Text", "hideCommissionDetails": "Text", "viewCommissionDetails": "Text", "hideCustomPrice": "Text", "setCustomPrice": "Text", "subordinateUsersList": "User", "commissionRecords": "Text", "customPriceSettings": "カスタム価格設定", "saveChanges": "Text", "editAgencyInfo": "Description", "logo": "ロゴ", "setAgencyLogo": "Text", "customHomepage": "カスタムホームページ", "aboutContent": "Description", "newHomepageConfig": "Text", "customAnnouncement": "カスタム通知", "customRechargeGroupRateJson": "Text", "customRechargeRate": "カスタムチャージレート", "viewSystemDefaultRate": "Text", "rateComparison": "料金比較", "comparisonResult": "Text", "higherThanSystem": "Text", "lowerThanSystem": "システム未満", "equalToSystem": "Text", "unknown": "未知", "notAnAgentYet": "Text", "becomeAnAgent": "代理店になる", "startYourOnlineBusiness": "Text", "becomeOurAgent": "Text", "noInventory": "Text", "instantCommission": "Text", "easyManagement": "Text", "flexibleDomainChoice": "Text", "youCan": "あなたはできます：", "useOwnDomain": "Text", "orUseOurSubdomain": "Text", "convenientStart": "Text", "actNow": "[TRANSLATION_FAILED] 🚀 立即行动！", "contactAdmin": "Text", "applyNow": "Text", "contactCooperation": "Text", "understandPolicy": "Text", "provideDomain": "Text", "configDomain": "Text", "promoteAndEarn": "プロモーション利益", "startPromoting": "Text", "noDeploymentWorries": "Text", "easySetup": "Text", "customizeContent": "Description", "commissionBenefits": "Text", "joinNowBenefit": "Text", "groups": {"student": "大学生", "studentDesc": "Text", "partTime": "Time", "partTimeDesc": "Text", "mediaWorker": "Text", "mediaWorkerDesc": "Text", "freelancer": "フリーランス", "freelancerDesc": "Text"}, "stories": {"story1": {"name": "チャンさん", "role": "Text"}, "story2": {"name": "リーさん", "role": "Text"}, "story3": {"name": "リウさん", "role": "Text"}, "story4": {"name": "鄭さん", "role": "Text"}, "story5": {"name": "周さん", "role": "Text"}, "story6": {"name": "王さん", "role": "Text"}, "story7": {"name": "黄さん", "role": "Text"}, "story8": {"name": "リウさん", "role": "Text"}}, "earnedAmount": "Text", "applyForAgentNow": "Text", "businessLinesConnected": "Text", "agencyJoin": "代理加盟", "becomeExclusiveAgent": "Text", "startBusinessJourney": "Text", "welcomeToAgencyPage": "Text", "earningsTitle": "Title", "becomeAgentSteps": "代理店になるためのステップ", "agencyRules": "Text", "suitableGroups": "Text", "agencyImages": {"becomeAgent": "代理店になる", "agencyBusiness": "Text"}, "rules": {"howToEstablishRelation": "Text", "howToEstablishRelationAnswer": "Text", "canSetPrice": "Text", "canSetPriceAnswer": "Text", "commissionShare": "Text", "commissionShareAnswer": {"assumption": "Text", "example": "Text", "calculation": "Text", "explanation": "Text"}}, "title": "エージェントプロフィール", "basicInfo": "Description", "commissionSettings": "Text", "statistics": "統計情報", "totalUsers": "User", "totalRevenue": "Text", "monthlyRevenue": "月収入", "commissionRate": "Text"}, "error": {"title": "エラー", "content": "Description"}, "loading": {"title": "Title", "content": "Description"}, "notfound": {"title": "404", "content": "Description"}, "servererror": {"title": "500", "content": "サーバーエラー"}, "unauthorized": {"title": "401", "content": "Description"}, "forbidden": {"title": "403", "content": "Description"}, "networkerror": {"title": "ネットワークエラー", "content": "ネットワークエラー"}, "timeout": {"title": "タイムアウト", "content": "リクエストのタイムアウト"}, "noresult": {"title": "Title", "content": "結果なし"}, "nopermission": {"title": "Title", "content": "権限がありません"}, "channelBridge": {"title": "Title", "channelPlatform": "チャネルプラットフォーム", "billingMethod": "Text", "channelName": "Title", "remark": "備考", "availableGroups": "Text", "availableModels": "利用可能なモデル", "channelKey": "チャネルキー", "proxyAddress": "Text", "cancel": "キャンセル", "submit": "Action", "gpt35Models": "GPT-3.5モデル", "gpt4Models": "GPT-4モデル", "clear": "クリア", "customModelName": "Title", "add": "追加", "moreConfigReminder": "Text", "quickIntegration": "Text", "selectBillingMethod": "請選択料金方式", "enterChannelName": "Title", "enterChannelRemark": "Text", "selectAvailableGroups": "Option", "selectAvailableModels": "Option", "enterChannelKey": "チャネルキーを入力してください。", "proxyAddressPlaceholder": "Text", "includes16kModels": "Text", "excludes32kModels": "32kモデルを含まない", "cleared": "Text", "addCustomModel": "Text", "clipboardTokenDetected": "Text", "channelIntegrationSuccess": "Success", "channelIntegrationFailed": "チャネル接続失敗："}, "about": {"loading": "Text", "noContent": "Description", "loadFailed": "Error"}, "onlineTopupRecord": {"title": "チャージ記録", "columns": {"id": "ID", "username": "ユーザー", "amount": "Text", "money": "支払額", "paymentMethod": "Text", "tradeNo": "Text", "status": "状態", "createTime": "Time"}, "status": {"success": "成功", "pending": "Status", "failed": "失敗"}, "paymentMethod": {"alipay": "アリペイ", "wxpay": "ウィーチャット", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "ペイパル"}}, "logContentDetail": {"description": "Description", "downstreamError": "ダウンストリームエラー", "originalError": "Description", "requestParams": "リクエストパラメータ", "copy": "コピー"}, "viewMode": {"switchTo": "Text", "cost": "コスト", "usage": "Text"}, "agenciesTable": {"title": "Title", "addAgency": "Text", "columns": {"id": "ID", "userId": "ユーザーID", "name": "Title", "domain": "Text", "commissionRate": "手数料率", "salesVolume": "Text", "userCount": "User", "commissionIncome": "Text", "historicalCommission": "累積収益", "actions": "Action"}, "confirm": {"deleteTitle": "Action", "updateName": "Text", "updateSuccess": "Action", "updateFailed": "Action", "deleteSuccess": "削除成功！"}, "messages": {"getListFailed": "Message", "deleteSuccess": "Message", "loadingData": "Message"}}, "units": {"times": "次", "percentage": "{{value}}%", "formatUsage": "Text"}, "dailyUsage": {"total": "合計", "totalCost": "Text", "tooltipTitle": {"cost": "Title", "usage": "使用状況"}, "yAxisName": {"cost": "コスト (USD)", "usage": "Title"}}, "dailyUsageByModel": {"total": "合計", "tooltipTotal": "Tip", "switchTo": "Text", "cost": "コスト", "usage": "Text", "perspective": "視点", "granularity": {"hour": "Text", "day": "日ごと", "week": "Text", "month": "月ごと"}}, "checkinModal": {"title": "Title", "captchaPlaceholder": "確認コード", "confirm": "Action", "close": "閉じる"}, "balanceTransfer": {"title": "Title", "accountInfo": {"balance": "口座残高", "transferFee": "Description", "groupNote": "Description"}, "form": {"receiverId": "受信者ID", "receiverUsername": "Title", "remark": "Text", "amount": "振込金額", "expectedFee": "Text", "submit": "Action"}, "result": {"success": "振込成功", "continueTransfer": "Text", "viewRecord": "Text"}, "warning": {"disabled": "Warning"}, "placeholder": {"autoCalculate": "Please enter..."}}, "channelsTable": {"title": "チャネル管理", "columns": {"id": "ID", "name": "Title", "type": "タイプ", "key": "Text", "base": "インターフェースアドレス", "models": "モデル", "weight": "Text", "priority": "Text", "retryInterval": "Text", "responseTime": "応答時間", "status": "Status", "quota": "残高", "expireTime": "Time", "group": "Text", "billingType": "請求タイプ", "actions": "Action", "fusing": "ブレーカー", "sort": "Text", "balance": "残高", "balanceUpdatedTime": "Time", "testTime": "テスト時間", "rpm": "RPM", "createdTime": "Time", "disableReason": "無効理由"}, "status": {"all": "すべて", "normal": "Status", "enabled": "Status", "manualDisabled": "手動無効化", "waitingRetry": "Status", "suspended": "Status", "specified": "Status", "allDisabled": "Status", "specifiedDisabled": "Status", "partiallyDisabled": "部分無効"}, "placeholder": {"selectGroup": "Please enter...", "selectStatus": "Status", "inputSelectModel": "Please enter...", "selectFusingStatus": "Status"}, "quota": {"usageAmount": "消費：{amount}", "remainingAmount": "Text", "customTotalAmount": "Text", "updateNotSupported": "Text", "details": "Text", "sufficient": "Text"}, "actions": {"edit": "編集", "copy": "クローンチャネル", "delete": "Action", "enable": "Action", "disable": "無効化", "test": "テスト", "advancedTest": "Action", "viewLog": "チャネルログ", "viewAbility": "Action", "cleanUsage": "Action", "updateBalance": "Action", "copyKey": "キーをコピーする", "topup": "チャージ", "viewModels": "Action"}, "confirm": {"deleteTitle": "削除確認", "deleteContent": "Action", "cleanUsageTitle": "Text", "cleanUsageContent": "Action", "testTitle": "確認テスト", "testContent": "{{status}}のチャネルをテストしてもよろしいですか？", "testNote": "Text", "deleteDisabledTitle": "Action", "deleteDisabledContent": "Action"}, "messages": {"operationSuccess": "操作成功", "operationSuccessWithSort": "Message", "operationFailed": "Message", "testRunning": "Message", "testSuccess": "Message", "testFailed": "チャネル「{{name}}(#{{id}})」テスト失敗：{{message}}", "testStarted": "Message", "testOperationFailed": "Message", "deleteSuccess": "Message", "deleteFailed": "Message", "modelPrefix": "モデル {{model}}", "channelInfo": "Description", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "Message", "updateBalanceFailed": "Message", "updateAllBalanceStarted": "Message", "updateAllBalanceSuccess": "Message", "fetchGroupError": "Message", "fetchChannelError": "Message", "selectChannelFirst": "Message", "deleteDisabledSuccess": "Message", "deleteOperationFailed": "Message", "copySuccess": "Message", "copyFailed": "Message", "emptyKey": "キーが空です", "enableSuccess": "Message", "disableSuccess": "無効化成功", "updateSuccess": "Message", "deleteConfirm": "Action", "batchDeleteConfirm": "Action", "testSuccessWithWarnings": "Message", "viewDetails": "Message", "fetchChannelDetailError": "Message", "topupSuccess": "Message", "topupFailed": "チャージ失敗：{{message}}"}, "popover": {"channelInfo": "Description"}, "menu": {"deleteManualDisabled": "Text", "deleteWaitingRetry": "Text", "deleteSuspended": "Text", "testAll": "すべてのチャネルをテストする", "testNormal": "Text", "testManualDisabled": "Text", "testWaitingRetry": "テスト待機再起動チャネル", "testSuspended": "Text", "deleteDisabledAccount": "Text", "deleteQuotaExceeded": "Text", "deleteRateLimitExceeded": "頻度制限を削除チャネル", "deleteInvalidKey": "Text", "deleteConnectionError": "Error"}, "tooltip": {"testNote": "Tip"}, "disableReasons": {"account_deactivated": "Text", "quota_exceeded": "Text", "rate_limit_exceeded": "頻度制限", "invalid_key": "Text", "connection_error": "连接エラー"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "Text", "times": "Time"}, "serverLogViewer": {"title": "サーバーログビューア", "connecting": "Text", "downloadSelect": "Option", "nginxConfig": "Text", "directAccess": "Text", "domainAccess": "Text", "buttons": {"pause": "Action", "resume": "Action", "clear": "クリア"}, "errors": {"fetchFailed": "Error", "downloadFailed": "Error", "wsError": "WebSocket接続エラー"}}, "channelScore": {"score": "Text", "successRate": "Success", "avgResponseTime": "Time", "title": "チャネルスコア", "hourlyTitle": "Title", "dailyTitle": "チャネル日得点", "weeklyTitle": "Title", "monthlyTitle": "チャネル月間スコア", "allTimeTitle": "Title", "infoTooltip": "Description", "tableView": "表形式ビュー", "chartView": "グラフビュー", "refresh": "リフレッシュ", "selectModel": "Option", "allModels": "すべてのモデル", "sortByScore": "Text", "sortBySuccessRate": "Success", "sortByResponseTime": "応答時間でソートする", "noData": "データがありません。", "totalItems": "Text", "fetchError": "Error", "aboutScoring": "得点計算について", "scoringExplanation": "Text", "successRateWeight": "Success", "successRateExplanation": "Success", "responseTimeWeight": "Time", "responseTimeExplanation": "Text", "columns": {"rank": "ランキング", "channelId": "チャネルID", "channelName": "Title", "model": "モデル", "totalRequests": "Text", "successRequests": "Success", "failedRequests": "Error", "successRate": "成功率", "avgResponseTime": "Time", "score": "総合得点", "actions": "Action"}, "actions": {"viewDetails": "Action", "test": "テストチャネル", "edit": "Action"}, "tooltips": {"excellent": "優秀", "good": "Tip", "average": "一般", "poor": "Tip", "veryPoor": "Tip"}, "scoringExplanation100": "Text"}, "menu": {"channelScores": "チャネルスコア"}, "relay": {"dispatchOptions": "スケジューリングオプション", "preciseWeightCalculation": "Text", "preciseWeightCalculationTip": "Text", "channelMetricsEnabled": "Text", "channelMetricsEnabledTip": "Text", "channelScoreRoutingEnabled": "Text", "channelScoreRoutingEnabledTip": "Text", "globalIgnoreBillingTypeFilteringEnabled": "Text", "globalIgnoreBillingTypeFilteringEnabledTip": "Text", "globalIgnoreFunctionCallFilteringEnabled": "Text", "globalIgnoreFunctionCallFilteringEnabledTip": "Text", "globalIgnoreImageSupportFilteringEnabled": "Text", "globalIgnoreImageSupportFilteringEnabledTip": "Text"}, "dynamicRouter": {"title": "Title", "reloadRoutes": "Text", "exportConfig": "Text", "clearConfig": "設定をリセットする", "importantNotice": "Message", "reloadLimitation": "Text", "exportDescription": "Text", "clearDescription": "Text", "routeGroups": "ルーティンググループ", "upstreamConfig": "Text", "endpointConfig": "Text", "editRouteGroup": "Text", "editUpstream": "Text", "editEndpoint": "Text", "editJSON": "JSONを編集する", "confirmClear": "Action", "confirmClearMessage": "Text", "configCleared": "Text", "configExported": "Text", "configReloaded": "Text"}, "notification": {"webhookConfig": "Message", "telegramConfig": "Message", "wxpusherConfig": "WxPusher設定", "qywxbotConfig": "Message", "dingtalkConfig": "Message", "feishuConfig": "<PERSON><PERSON><PERSON>設定", "title": "Title", "subscriptionEvents": "購読イベント", "notificationMethods": "Message", "alertSettings": "Message", "emailConfig": "メール設定", "customEmails": "カスタムメールアドレス", "addEmail": "Message", "removeEmail": "削除", "emailPlaceholder": "Message", "emailTooltip": "Message", "webhookUrl": "WebhookURL", "webhookUrlPlaceholder": "Message", "webhookSecret": "Webhookシークレット", "webhookSecretPlaceholder": "Message", "telegramBotToken": "Bot <PERSON>", "telegramBotTokenPlaceholder": "Message", "telegramChatId": "Chat ID", "telegramChatIdPlaceholder": "Message", "wxpusherAppToken": "<PERSON><PERSON>", "wxpusherAppTokenPlaceholder": "Message", "wxpusherUids": "UIDリスト", "wxpusherUidsPlaceholder": "Message", "qywxbotWebhookUrl": "WebhookURL", "qywxbotWebhookUrlPlaceholder": "Message", "dingtalkWebhookUrl": "WebhookURL", "dingtalkWebhookUrlPlaceholder": "Message", "dingtalkSecret": "署名シークレット", "dingtalkSecretPlaceholder": "Message", "feishuWebhookUrl": "WebhookURL", "feishuWebhookUrlPlaceholder": "Message", "feishuSecret": "署名検証", "feishuSecretPlaceholder": "Message", "events": {"lowBalance": "残高不足", "quotaExpiry": "Message", "securityAlert": "セキュリティアラート", "systemAnnouncement": "Message", "promotionalActivity": "Message", "modelPricingUpdate": "モデル価格更新", "account_balance_low": "Message", "account_quota_expiry": "割り当て期限切れ間近", "security_alert": "Message", "system_announcement": "システム公告", "promotional_activity": "Message", "model_pricing_update": "モデル価格更新", "anti_loss_contact": "Message"}, "lowBalanceThreshold": "Message", "lowBalanceThresholdPlaceholder": "Message", "quotaExpiryThreshold": "Message", "quotaExpiryThresholdPlaceholder": "Message", "selectEvents": "Message", "eventsDescription": "Description", "selectMethods": "Message", "methodsDescription": "Message", "description": "Description", "recommended": "Message", "important": "Message", "testRecommendation": "Message", "testNotification": "Message", "testMessage": "Message", "testSuccess": "Message", "testFailed": "テスト通知送信失敗", "saveSuccess": "Message", "saveFailed": "設定保存失敗", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Message", "alertExplanationTitle": "Title", "alertExplanation": "Message", "validation": {"invalidEmail": "Message", "emailRequired": "Message", "invalidUrl": "有効なURLを入力してください", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "WxPusher APP Tokenを入力してください", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "Feishu ボット Webhook URLを入力してください", "webhookUrlRequired": "Message", "telegramTokenRequired": "Telegram Bot Tokenを入力してください", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Message"}, "qywxbotGuide": "企業WeChat ボット設定ガイド", "wxpusherGuide": "Message", "wxpusherUid": "ユーザーUID", "dingtalkGuide": "Message", "feishuGuide": "Feishu ボット設定ガイド", "webhookGuide": "Message", "webhookToken": "インターフェース認証情報", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（オプション）", "telegramGuide": "Message", "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "アカウントの割り当てが期限切れになる前に事前に通知します", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "モデル価格変動と課金ルール更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "メールで通知メッセージを受信", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "企業WeChat ボットで通知を受信", "dingtalk": "Description", "feishu": "<PERSON><PERSON>u ボットで通知を受信"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企業WeChat ボット", "dingtalk": "Message", "feishu": "<PERSON><PERSON><PERSON> ボット"}, "configurationSteps": "Message", "detailedDocumentation": "詳細ドキュメント：", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "企業WeChat グループボット設定説明", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherUserUID": "ユーザーUID", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Message", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "DingTalk カスタムボット接続", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "ボット名と説明を設定", "feishuStep4": "Message", "feishuStep5": "生成されたWebhook URLをコピー", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "Message", "telegramStep5": "Message", "telegramStep6": "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates にアクセス", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 公式ドキュメント", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "注意事項：", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "メッセージ形式サポート：", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• セキュリティ向上のため署名検証の有効化を推奨", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "Title", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "• インラインキーボードとカスタムキーボードをサポート", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Chat ID形式説明：", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "Message", "telegramChannelPermission": "Message", "webhookCallUrl": "Message", "webhookConfigurationGuide": "Message", "webhookDataFormatExample": "Message", "webhookConfigurationInstructions": "Message", "webhookRequestMethod": "Message", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "Message", "webhookTimeout": "Message", "webhookRetryMechanism": "Message", "webhookTip": "Message", "telegramStep3Detailed": "Message", "telegramPersonalChatDetailed": "Message", "telegramGroupChatDetailed": "Message", "telegramChannelDetailed": "Message", "telegramQuickChatIdTitle": "Title", "telegramQuickStep1": "Message", "telegramQuickStep2": "ブラウザで上記リンクにアクセス", "telegramQuickStep3": "Message"}, "legal": {"privacyPolicy": {"title": "プライバシーポリシー", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "情報収集", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Text"}}, "informationUsage": {"title": "Title", "description": "Description", "items": ["Description", "Description", "Description", "Text", "Description"]}}}, "serviceAgreement": {"title": "Title", "lastUpdated": "最終更新日：{{date}}", "sections": {"serviceDescription": {"title": "Title", "description": "Description", "features": ["Text", "Description", "Description", "開発者向けツールとドキュメント"]}}}}, "tasks": {"title": "タスクリスト", "taskId": "タスクID", "platform": "プラットフォーム", "type": "タイプ", "status": "ステータス", "progress": "進行状況", "submitTime": "送信時間", "startTime": "開始時間", "endTime": "終了時間", "duration": "実行時間", "result": "結果", "taskIdPlaceholder": "タスクIDを入力してください", "platformPlaceholder": "プラットフォームを選択してください", "typePlaceholder": "タイプを選択してください", "statusPlaceholder": "ステータスを選択してください", "videoGeneration": "ビデオ生成", "imageGeneration": "画像生成", "musicGeneration": "音楽生成", "textGeneration": "テキスト生成", "unknown": "不明", "success": "成功", "failed": "失敗", "inProgress": "実行中", "submitted": "送信済み", "queued": "待機中", "notStarted": "未開始", "viewResult": "結果を表示", "retry": "再試行", "cancel": "キャンセル", "viewError": "エラーを表示", "taskDetails": "タスク詳細", "errorDetails": "エラー詳細", "loadError": "タスクリストの読み込みに失敗しました", "refreshSuccess": "タスクステータスの更新に成功しました", "refreshFailed": "タスクステータスの更新に失敗しました", "refreshError": "タスクステータスの更新中にエラーが発生しました", "viewVideo": "ビデオを表示", "videoPreview": "ビデオプレビュー", "copyVideoUrl": "ビデオリンクをコピー", "copiedVideoUrl": "ビデオリンクをコピーしました", "downloadVideo": "ビデオをダウンロード", "videoNotSupported": "お使いのブラウザはビデオ再生をサポートしていません", "finishTime": "完了時間", "imageUrl": "画像URL", "videoUrl": "ビデオURL", "action": "操作", "model": "モデル", "videoUrls": "ビデオURLリスト"}}