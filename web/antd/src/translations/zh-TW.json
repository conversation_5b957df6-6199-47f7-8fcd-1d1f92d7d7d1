{"message": {"copyModelSuccess": "Message", "copyFailed": "Message", "logoutSuccess": "Message", "loginSuccess": {"default": "登入成功", "welcomeBack": "Message"}, "removeLocalStorage": {"confirm": "Action", "success": "清除本地快取成功"}, "loadData": {"error": "Message"}, "noNotice": "Message", "verification": {"turnstileChecking": "Message", "pleaseWait": "請稍後重試"}, "clipboard": {"inviteCodeDetected": "Message", "clickToCopy": "點擊複製", "copySuccess": "Message"}}, "common": {"yes": "是", "no": "Text", "copyAll": "Text", "all": "全部", "more": "Text", "unlimited": "Text", "enabled": "開啟", "disabled": "Text", "save": "保存", "cancel": "Text", "create": "創建", "usd": "Text", "day": "Text", "day_plural": "{{count}} 天", "days": "Text", "seconds": "Text", "times": "次", "submit": "Action", "bind": "綁定", "unknown": "Text", "loading": "Text", "copyFailed": "Error", "people": "人", "ok": "Text", "close": "關閉", "copied": "Text", "expand": "展開", "collapse": "Text", "none": "無", "remark": "Text", "selectPlaceholder": "Please enter...", "on": "開", "off": "Text", "name": "Title", "displayName": "Title", "description": "描述", "ratio": "Text", "unnamed": "Title", "groups": "分組", "captchaPlaceholder": "Please enter...", "confirm": "確認", "permissions": "Text", "actions": "操作", "createdTime": "Time", "expiredTime": "過期時間", "search": "Text", "reset": "重置", "refresh": "Text", "pagination": {"total": "Text"}, "delete": "刪除", "edit": "Text", "add": "新增", "update": "Time", "back": "返回", "next": "Text", "previous": "上一步", "success": "Success", "error": "錯誤", "warning": "Warning", "info": "信息"}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "Action"}, "userRole": {"normal": "普通用戶", "agent": "User", "admin": "管理員", "superAdmin": "User", "loading": "載入中..."}, "channelStatus": {"enabled": "Status", "disabled": "禁用", "waitingRestart": "Status", "waiting": "等待", "autoStoppedTitle": "Title", "stopped": "停用", "partiallyDisabled": "Status", "unknown": "未知", "reason": "Status"}, "channelBillingTypes": {"payAsYouGo": "Text", "payPerRequest": "按次計費", "unknown": "Text"}, "tokenStatus": {"normal": "正常", "disabled": "Status", "expired": "過期", "exhausted": "Status", "unknown": "未知"}, "userStatus": {"normal": "Status", "banned": "封鎖", "unknown": "Status"}, "redemptionStatus": {"normal": "正常", "disabled": "Status", "redeemed": "Status", "expired": "過期", "unknown": "Status"}, "duration": {"request": "請求", "firstByte": "Text", "total": "總計", "seconds": "Text", "lessThanOneSecond": "Text"}, "streamType": {"stream": "流式", "nonStream": "Text"}, "noSet": {"title": "Title", "name": {"about": "關於", "chat": "Title"}}, "buttonText": {"add": "新增", "cancel": "Action", "confirm": "確認", "delete": "Action", "edit": "編輯", "save": "Action", "updateBalance": "Action", "test": "測試", "multiple": "Action"}, "channelPage": {"title": "Title"}, "channelStatusCount": {"title": "Title", "summary": "Text", "statusEnabled": "Status", "statusDisabled": "Status", "statusRetry": "重試中", "statusStopped": "Status", "statusPartially": "Status"}, "header": {"routes": {"status": "狀態", "home": "Title", "chat": "對話", "pptGen": "Title", "chart": "統計", "agency": "Title", "channel": "渠道", "ability": "Title", "channelGroup": "渠道組", "token": "Title", "log": "日誌", "logDetail": "Title", "midjourney": "繪圖", "user": "Title", "config": "配置", "packagePlanAdmin": "Title", "redemption": "Title", "group": "分組", "query": "Title", "about": "關於", "setting": {"default": "Title", "operation": "Title", "system": "系統設定", "global": "Title", "advance": "特性設定", "sensitive": "Title", "verification": "驗證碼配置", "update": "Title"}, "account": {"default": "帳戶", "profile": "Title", "cardTopup": "卡密兌換", "onlineTopup": "Title", "recharge": "餘額充值", "balanceTransfer": "Title", "pricing": "費用說明", "packagePlan": {"list": "Title", "record": "購買紀錄"}, "notificationSettings": "Title"}, "tools": {"default": "工具", "fileUpload": "Title", "keyExtraction": "密鑰提取", "multiplierCalculator": "Title", "shortLink": "短鏈生成", "testConnection": "Title", "customPrompts": "Title", "redis": "Title", "ratioCompare": "倍率對比", "serverLog": "Title"}, "onlineTopupRecord": "充值紀錄", "channelScores": "Title", "dynamicRouter": "動態路由", "task": "Title", "agencyJoin": "代理加盟"}, "dropdownMenu": {"profile": "Title", "recharge": "餘額充值", "agencyCenter": "Title", "checkin": "簽到", "darkMode": {"enable": "Title", "disable": "日間模式"}, "fullScreen": {"default": "Title", "enable": "全螢幕模式", "disable": "Title"}, "logout": "登出"}, "checkin": {"default": "Title", "success": "Title", "failed": "簽到失敗", "verification": "Title"}, "avatarProps": {"login": "登入"}}, "settings": {"public": {"titles": {"default": "Title"}, "SystemName": "系統名稱", "ServerAddress": "Text", "TopUpLink": "充值連結", "ChatLink": "Text", "Logo": "系統Logo", "HomePageContent": "Description", "About": "關於內容", "Notice": "Message", "Footer": "頁腳內容", "RegisterInfo": "Description", "HeaderScript": "自訂標頭", "SiteDescription": "Description", "PrivacyPolicy": "隱私政策", "ServiceAgreement": "Text", "FloatButton": {"FloatButtonEnabled": "開啟", "DocumentInfo": "Action", "WechatInfo": "微信訊息", "QqInfo": "Action"}, "CustomThemeConfig": "自訂主題", "AppList": "Text"}, "system": {"title": "Title"}, "operation": {"title": "運營設置"}, "payment": {"title": "Title"}, "notification": {"title": "通知設置"}, "security": {"title": "Title"}, "appearance": {"title": "外觀設置"}, "advanced": {"title": "Title"}}, "home": {"default": {"title": "歡迎使用", "subtitle": "Title", "start": "Text", "description": {"title": "新特性：", "part1": "Description", "part2": "Description", "part3": "Description", "part4": "Description"}}}, "dailyUsageChart": {"title": "Title", "yAxisName": "Text", "loadingTip": "Tip", "fetchError": "Error"}, "modelUsageChart": {"title": "模型使用情況", "hourlyTitle": "Title", "dailyTitle": "模型每日使用情況", "weeklyTitle": "Title", "monthlyTitle": "模型每月使用情況"}, "granularity": {"hour": "Text", "day": "每天", "week": "Text", "month": "每月", "all": "Text"}, "abilitiesTable": {"title": "Title", "export": "匯出", "group": "Text", "model": "模型", "channelId": "Text", "enabled": "已啟用", "weight": "Text", "priority": "Text", "billingType": "Text", "functionCallEnabled": "Text", "imageSupported": "支持圖像", "yes": "Text", "no": "不", "perToken": "Text", "perRequest": "Text", "noDataToExport": "Text", "exportConfirm": "Action", "exportSuccess": "導出成功", "toggleSuccess": "Success", "toggleError": "切換失敗", "selectOrInputGroup": "Option"}, "logsTable": {"retry": "重試", "retryChannelList": "Text", "retryDurations": "重試耗時詳情", "channel": "Text", "duration": "耗時", "startTime": "Time", "endTime": "結束時間", "retryCount": "Text", "retryDetails": "重試詳情", "totalRetryTime": "Time", "seconds": "秒鐘", "tokenGroup": "Text", "selectGroup": "選擇分組", "dailyModelUsageStats": "Text", "time": "時間", "moreInfo": "Description", "ip": "IP", "remoteIp": "Text", "ipTooltip": "Tip", "requestId": "請求 ID", "username": "Title", "userId": "使用者 ID", "tokenName": "Title", "token": "令牌", "type": "Text", "typeUnknown": "未知", "type充值": "Text", "type消费": "消費", "type管理": "Text", "type系统": "系統", "type邀请": "Text", "type提示": "提示", "type警告": "Text", "type错误": "錯誤", "type签到": "Text", "type日志": "日誌", "type退款": "Text", "type邀请奖励金划转": "Text", "type代理奖励": "代理獎勵", "type下游错误": "Text", "type测试渠道": "測試渠道", "typeRecharge": "Text", "typeConsumption": "消費", "typeManagement": "Text", "typeSystem": "系統", "typeInvitation": "Text", "typePrompt": "提示", "typeWarning": "Warning", "typeError": "錯誤", "typeCheckin": "Text", "typeLog": "日誌", "typeRefund": "Text", "typeInviteReward": "Text", "typeAgencyBonus": "代理獎勵", "typeDownstreamError": "Error", "typeChannelTest": "測試渠道", "channelId": "Text", "channelName": "Title", "model": "模型", "modelPlaceholder": "Please enter...", "info": "資訊", "isStream": "Text", "isStreamPlaceholder": "Please enter...", "prompt": "提示", "completion": "Text", "consumption": "消費", "consumptionRange": "Text", "description": "說明", "action": "Action", "details": "詳情", "tokenKey": "Text", "requestDuration": "請求耗時", "firstByteDuration": "Text", "totalDuration": "總耗時", "lessThanOneSecond": "Text", "modelInvocation": "Text", "modelUsage": "Text", "totalQuota": "總消費額度: {{quota}}", "totalRpm": "Text", "totalTpm": "Text", "totalMpm": "Text", "dailyEstimate": "Text", "currentStats": "Text", "statsTooltip": "Tip", "showAll": "Text", "exportConfirm": "Action", "export": "匯出", "statsData": "Text", "today": "當天", "lastHour": "Text", "last3Hours": "3小時", "lastDay": "Text", "last3Days": "3天", "last7Days": "Text", "lastMonth": "Text", "last3Months": "3個月", "excludeModels": "Text", "selectModelsToExclude": "Option", "excludeErrorCodes": "排除錯誤碼", "excludeErrorCodesPlaceholder": "Error", "errorCode": "錯誤碼", "errorCodePlaceholder": "Error", "timezoneTip": "Tip", "timezoneNote": "時區提示", "timezoneDescription": "Text", "goToProfile": "Text", "realtimeQuota": "Time", "viewTotalQuota": "Text", "viewTotalQuotaTip": "Tip", "loadingTotalQuota": "Text", "totalQuotaTitle": "歷史總消費統計", "loadTotalQuotaError": "Error", "requestLogs": "請求日誌 - {{requestId}}", "noRequestLogs": "Text", "metricsExplanation": "Text", "autoRefresh": "Text", "autoRefreshTip": "Text", "autoRefreshOn": "Text", "autoRefreshOff": "已關閉自動刷新", "refreshInterval": "Tip", "stopRefresh": "停止更新", "secondsWithValue": "Text", "minutesWithValue": "Text", "title": "日誌管理", "columns": {"id": "ID", "createdTime": "Time", "type": "類型", "content": "Description", "username": "Title", "tokenName": "Title", "modelName": "模型名稱", "quota": "Text", "promptTokens": "Text", "completionTokens": "完成令牌", "model": "Text", "requestId": "請求ID", "responseTime": "Time"}, "actions": {"view": "查看", "retry": "Action", "viewDetails": "Action", "copyContent": "複製內容"}, "status": {"success": "Success", "failed": "失敗"}, "messages": {"retrySuccess": "Message", "retryFailed": "重試失敗：{{message}}", "copySuccess": "Message"}, "totalRequests": "總請求數", "successRate": "Success"}, "mjLogs": {"logId": "日誌ID", "submitTime": "Action", "type": "類型", "channelId": "Text", "userId": "用戶ID", "taskId": "Text", "submit": "提交", "status": "Status", "progress": "進度", "duration": "Text", "result": "結果", "prompt": "Prompt", "promptEn": "PromptEn", "failReason": "Error", "startTime": "開始時間", "endTime": "Time", "today": "當天", "lastHour": "Text", "last3Hours": "3小時", "lastDay": "Text", "last3Days": "3天", "last7Days": "Text", "lastMonth": "Text", "last3Months": "3個月", "selectTaskType": "Option", "selectSubmitStatus": "選擇提交情況", "submitSuccess": "Action", "queueing": "正在排隊", "duplicateSubmit": "Action", "selectTaskStatus": "Status", "success": "成功", "waiting": "Text", "queued": "排隊", "executing": "Text", "failed": "失敗", "seconds": "Text", "unknown": "未知", "viewImage": "Text", "markdownFormat": "Text", "midjourneyTaskId": "Text", "copiedAsMarkdown": "Text", "copyFailed": "複製失敗", "copiedMidjourneyTaskId": "Text", "drawingLogs": "Text", "onlyUnarchived": "Text", "imagePreview": "圖片預覽", "copiedImageUrl": "Text", "copy": "複製", "download": "Text", "resultImage": "Text", "downloadError": "Error", "mode": "模式", "selectMode": "Option", "relax": "輕鬆模式", "fast": "Text", "turbo": "極速模式", "actions": "Action", "refresh": "刷新", "refreshSuccess": "Success", "refreshFailed": "任務狀態刷新失敗", "refreshError": "Error", "tasks": {"title": "任務列表", "taskId": "任務ID", "platform": "平台", "type": "類型", "status": "狀態", "progress": "進度", "submitTime": "提交時間", "startTime": "開始時間", "endTime": "結束時間", "duration": "持續時間", "result": "結果", "taskIdPlaceholder": "請輸入任務ID", "platformPlaceholder": "請選擇平台", "typePlaceholder": "請選擇任務類型", "statusPlaceholder": "請選擇狀態", "videoGeneration": "影片生成", "imageGeneration": "圖像生成", "musicGeneration": "音樂生成", "textGeneration": "文本生成", "unknown": "未知", "success": "成功", "failed": "失敗", "inProgress": "進行中", "submitted": "已提交", "queued": "排隊中", "notStarted": "未開始", "viewResult": "查看結果", "retry": "重試", "cancel": "取消", "viewError": "查看錯誤", "taskDetails": "任務詳情", "errorDetails": "錯誤詳情", "loadError": "加載任務列表失敗"}, "viewVideo": "Text", "videoPreview": "視頻預覽", "copyVideoUrl": "Text", "copiedVideoUrl": "Text", "downloadVideo": "下載視頻", "videoNotSupported": "Text", "finishTime": "完成時間", "imageUrl": "Text", "videoUrl": "視頻地址", "action": "Action", "model": "模型", "platform": "Text", "videoUrls": "Text"}, "mjTaskType": {"IMAGINE": "生成圖像", "UPSCALE": "Text", "VARIATION": "變換", "REROLL": "Text", "DESCRIBE": "圖生文", "BLEND": "Text", "OUTPAINT": "變焦", "DEFAULT": "Text"}, "mjCode": {"submitSuccess": "Action", "queueing": "正在排隊", "duplicateSubmit": "Action", "unknown": "未知"}, "mjStatus": {"success": "Success", "waiting": "等待", "queued": "Status", "executing": "執行", "failed": "Error", "unknown": "未知"}, "tokensTable": {"title": "Title", "table": {"title": "令牌管理", "toolBar": {"add": "Text", "delete": "刪除令牌", "deleteConfirm": "Action", "export": "匯出", "exportConfirm": "Action"}, "action": "操作"}, "modal": {"title": {"add": "Title", "edit": "編輯令牌"}, "field": {"name": "Title", "description": "令牌描述", "type": {"default": "Text", "type1": "按量計費", "type2": "Text", "type3": "混合計費", "type4": "Text", "type5": "按次優先"}, "status": "Status", "statusEnabled": "正常", "statusDisabled": "Status", "statusExpired": "過期", "statusExhausted": "Status", "models": "模型", "usedQuota": "Text", "remainQuota": "剩餘額度", "createdTime": "Time", "expiredTime": "過期時間", "all": "Text", "more": "更多", "notEnabled": "Text", "unlimited": "無限制", "daysLeft": "Text", "expired": "Text", "userId": "使用者ID", "key": "Text", "neverExpire": "永不過期", "quota": "Text", "unlimitedQuota": "Text", "unlimitedExpired": "永不過期"}, "delete": {"title": "Title", "content": "Description"}, "footer": {"cancel": "取消", "confirm": "Action", "update": "更新"}, "bridge": {"title": "Title", "placeholder": "Please enter..."}, "copy": {"title": "手動複製"}}, "dropdown": {"onlineChat": "Text", "disableToken": "禁用令牌", "enableToken": "Text", "editToken": "編輯令牌", "requestExample": "Text", "tokenLog": "令牌日誌", "shareToken": "Text", "quickIntegration": "一鍵對接"}, "error": {"fetchModelsFailed": "Error", "batchDeleteFailed": "批量刪除失敗：{{message}}", "deleteTokenFailed": "Error", "refreshTokenFailed": "刷新令牌失敗：{{message}}", "enableTokenFailed": "Error", "disableTokenFailed": "禁用令牌失敗：{{message}}", "fetchDataFailed": "Error", "createTokenFailed": "創建令牌失敗：{{message}}", "updateTokenFailed": "Error"}, "success": {"batchDelete": "批量刪除成功", "shareTextCopied": "Success", "tokenCopied": "令牌已複製到剪貼簿", "deleteToken": "Success", "refreshToken": "刷新令牌成功", "enableToken": "Success", "disableToken": "禁用令牌成功", "export": "Success", "createToken": "創建令牌成功", "updateToken": "Success"}, "warning": {"copyFailed": "Error", "invalidServerAddress": "Warning"}, "info": {"openingBridgePage": "Description"}, "export": {"name": "名稱", "key": "Text", "billingType": "Text", "status": "狀態", "models": "Text", "usedQuota": "消耗額度", "remainQuota": "Text", "createdTime": "創建時間", "expiredTime": "Time", "unlimited": "無限制", "neverExpire": "Text", "filename": "令牌列表", "success": "Success", "failed": "導出失敗"}, "billingType": {"1": "Text", "2": "按次計費", "3": "Text", "4": "按量優先", "5": "Text"}, "bridge": {"quickIntegration": "一鍵對接"}, "columns": {"name": "Title", "status": "狀態", "quota": "Text", "usedQuota": "Text", "remainingQuota": "剩餘額度", "accessedTime": "Time", "expiredTime": "過期時間"}, "actions": {"copy": "Action", "enable": "啟用", "disable": "Action"}, "messages": {"copySuccess": "Message", "enableSuccess": "啟用成功", "disableSuccess": "Message", "deleteSuccess": "刪除成功"}}, "editTokenModal": {"editTitle": "Title", "createTitle": "創建令牌", "defaultTokenName": "Title", "tokenName": "令牌名稱", "unlimitedQuota": "Text", "remainingQuota": "剩餘額度", "authorizedQuota": "Text", "quotaLimitNote": "Text", "quickOptions": "快捷選項", "neverExpire": "Text", "expiryTime": "過期時間", "billingMode": "Text", "selectGroup": "選擇分組", "switchGroup": "Text", "switchGroupTooltip": "Tip", "switchGroupHint": "Tip", "importantFeature": "重要", "tokenRemark": "Text", "discordProxy": "Text", "enableAdvancedOptions": "啟用高級選項", "generationAmount": "Text", "availableModels": "可用模型", "selectModels": "Option", "activateOnFirstUse": "Text", "activateOnFirstUseTooltip": "Tip", "activationValidPeriod": "Text", "activationValidPeriodTooltip": "Tip", "ipWhitelist": "IP白名單", "ipWhitelistPlaceholder": "Text", "rateLimiter": "Text", "rateLimitPeriod": "Text", "rateLimitPeriodTooltip": "Tip", "rateLimitCount": "限流次數", "rateLimitCountTooltip": "Tip", "promptMessage": "提示訊息", "promptMessageTooltip": "Message", "promotionPosition": "推廣位置", "promotionPositionStart": "Text", "promotionPositionEnd": "結尾", "promotionPositionRandom": "Text", "promotionContent": "Description", "currentGroup": "當前分組", "searchGroupPlaceholder": "Please enter...", "mjTranslateConfig": "MJ翻譯配置", "mjTranslateConfigTip": "Tip", "mjTranslateBaseUrlPlaceholder": "Please enter...", "mjTranslateApiKeyPlaceholder": "Please enter...", "mjTranslateModelPlaceholder": "Please enter...", "mjTranslateBaseUrlRequired": "啟用翻譯時必須提供基礎URL", "mjTranslateApiKeyRequired": "Text", "mjTranslateModelRequired": "Text"}, "addTokenQuotaModal": {"title": "令牌餘額管理{{username}}", "defaultReason": "Text", "enterRechargeAmount": "Text", "enterRemark": "請輸入備註消息", "confirmOperation": "Action", "confirmContent": "Text", "recharge": "Text", "deduct": "Text", "andUpdateExpiry": "Time", "alertMessage": "Message", "rechargeAmount": "充值額度", "operationReason": "Text", "finalBalance": "最終餘額"}, "billingType": {"1": "Text", "2": "按次計費", "3": "Text", "4": "按量優先", "5": "Text", "payAsYouGo": "按量計費", "payPerRequest": "Text", "hybrid": "混合計費", "payAsYouGoPriority": "Text", "payPerRequestPriority": "按次優先", "unknown": "Text"}, "packagePlanAdmin": {"title": "套餐", "table": {"title": "Title", "toolBar": {"add": "新建套餐", "delete": "Text"}, "action": {"edit": "編輯", "delete": "Action", "detail": "詳情", "recovery": "Action", "offline": "下架"}}, "modal": {"title": {"add": "Title", "edit": "編輯套餐"}, "field": {"name": "Title", "type": {"default": "套餐類型", "type1": "Text", "type2": "計次套餐", "type3": "Text"}, "group": "套餐分組", "description": "Description", "price": "套餐價格", "valid_period": "Text", "first_buy_discount": "首購折扣", "rate_limit_num": "Text", "rate_limit_duration": "限制週期", "inventory": "Text", "available_models": "可用模型", "quota": "Text", "times": "套餐次數"}, "footer": {"cancel": "Text", "confirm": "確認", "update": "Time"}}}, "login": {"title": "登入", "username": "Title", "password": "密碼", "login": "Text", "otherLoginMethods": "Text", "register": "註冊帳戶", "accountLogin": "Text", "phoneLogin": "Text", "usernamePlaceholder": "用戶名", "usernameRequired": "Title", "passwordPlaceholder": "密碼", "passwordRequired": "Text", "passwordMaxLength": "Text", "phonePlaceholder": "手機號碼", "phoneRequired": "Text", "phoneFormatError": "手機號格式錯誤！", "smsCodePlaceholder": "Please enter...", "smsCodeCountdown": "Text", "getSmsCode": "獲取驗證碼", "agreementText": "Text", "privacyPolicy": "Text", "and": "和", "serviceAgreement": "Text", "alreadyLoggedIn": "您已登入", "weakPasswordWarning": "Warning", "welcomeMessage": "歡迎使用", "captchaError": "Error", "credentialsError": "Error", "resetPassword": "重設密碼", "captchaExpired": "Text", "loginFailed": "登入失敗：{{message}}", "captchaRequired": "Text", "captchaPlaceholder": "驗證碼", "smsSent": "Text", "smsSendFailed": "簡訊驗證碼發送失敗", "agreementWarning": "Warning", "turnstileWarning": "Warning", "loginSuccess": "登入成功", "rememberMe": "Text", "forgotPassword": "Text", "loginButton": "登錄", "noAccount": "Text", "signUp": "註冊"}, "register": {"title": "Title", "usernameRequired": "Title", "usernameNoAt": "Title", "usernameNoChinese": "Title", "usernameLength": "Title", "usernamePlaceholder": "用戶名", "passwordRequired": "Text", "passwordLength": "Text", "passwordPlaceholder": "密碼", "confirmPasswordRequired": "Action", "passwordMismatch": "Text", "confirmPasswordPlaceholder": "確認密碼", "emailInvalid": "Text", "emailRequired": "請輸入電子郵件！", "emailPlaceholder": "Please enter...", "emailCodeRequired": "Text", "emailCodePlaceholder": "郵箱驗證碼", "enterCaptcha": "Text", "resendEmailCode": "Text", "getEmailCode": "獲取驗證碼", "phoneRequired": "Text", "phoneInvalid": "Text", "phonePlaceholder": "手機號碼", "smsCodeRequired": "Text", "smsCodePlaceholder": "簡訊驗證碼", "resendSmsCode": "Text", "getSmsCode": "獲取驗證碼", "captchaRequired": "Text", "captchaPlaceholder": "驗證碼", "inviteCodePlaceholder": "Please enter...", "submit": "註冊", "successMessage": "Message", "failMessage": "註冊失敗", "emailCodeSent": "Text", "smsCodeSent": "簡訊驗證碼已發送", "confirm": "Action", "emailVerifyTitle": "Title", "smsVerifyTitle": "簡訊驗證", "registerVerifyTitle": "Title", "confirmPassword": "確認密碼", "email": "Text", "inviteCode": "Text", "registerButton": "註冊", "hasAccount": "Text", "signIn": "登錄", "agreement": "Text", "termsOfService": "Text"}, "profile": {"timezone": "時區", "phoneNumber": "Text", "emailAddress": "Text", "wechatAccount": "微信帳戶", "telegramAccount": "Text", "bindTelegram": "綁定Telegram", "balanceValidPeriod": "Text", "lastLoginIP": "上次登入IP", "lastLoginTime": "Time", "inviteCode": "邀請碼", "inviteLink": "Text", "generate": "生成", "pendingEarnings": "Text", "transfer": "劃轉", "totalEarnings": "Text", "accountBalance": "Text", "totalConsumption": "累計消費", "callCount": "Text", "invitedUsers": "邀請用戶", "promotionInfo": "Description", "inviteDescription": "Description", "userInfo": "使用者資訊", "availableModels": "Text", "modelNameCopied": "Title", "noAvailableModels": "暫無可用模型", "accountOptions": "Option", "changePassword": "修改密碼", "systemToken": "Text", "unsubscribe": "註銷", "educationCertification": "Text", "timezoneUpdateSuccess": "Success", "inviteLinkCopied": "Text", "inviteLinkCopyFailed": "Error", "inviteLinkGenerationFailed": "邀請鏈接生成失敗", "allModelsCopied": "Text", "copyAllModels": "複製全部模型", "totalModels": "Text", "expired": "已過期", "validPeriod": "Text", "longTermValid": "長期有效", "failedToLoadModels": "Error", "accessTokens": "訪問令牌", "accessTokensManagement": "Text", "accessTokenDescription": "Text", "tokenNameLabel": "Title", "tokenNamePlaceholder": "Title", "presetPermissions": "預設權限", "detailPermissions": "Text", "validityPeriod": "Text", "validityPeriodExtra": "Text", "remarkLabel": "備註", "remarkPlaceholder": "Please enter...", "createToken": "Text", "editToken": "編輯令牌", "deleteToken": "Text", "copyToken": "複製令牌", "tokenCreated": "Text", "tokenUpdated": "令牌更新成功", "tokenDeleted": "Text", "tokenCopied": "Text", "deleteTokenConfirm": "Action", "disableTokenConfirm": "Text", "enableTokenConfirm": "Action", "tokenSecurityWarning": "Warning", "tokenPermissionTip": "Tip", "tokenExpiryWarning": "Text", "tokenExpired": "Text", "tokenNeverExpires": "永不過期", "tokenLastUsed": "Text", "tokenNeverUsed": "從未使用", "tokenUsageCount": "Text", "tokenCreatedAt": "創建時間", "tokenStatus": "Status", "tokenActive": "活躍", "tokenInactive": "Text", "tokenDisabled": "已禁用", "permissions": {"read": "Text", "write": "寫入權限", "admin": "Text", "channels": "渠道管理", "tokens": "Text", "logs": "日誌查看", "users": "User", "settings": "系統設置", "billing": "Text", "analytics": "數據分析", "api": "Text", "webhook": "Text"}, "createNewToken": "创建新令牌", "tokenCreatedSuccess": "Success", "tokenSavePrompt": "Text", "readPermission": "读取权限", "writePermission": "Text", "deletePermission": "删除权限", "tokenManagement": "Text", "channelManagement": "渠道管理", "logView": "Text", "statisticsView": "统计信息", "userManagement": "User", "quotaManagement": "额度管理", "readOnlyPermission": "Text", "writeOnlyPermission": "只写权限", "readWritePermission": "Text", "standardPermission": "标准权限", "fullPermission": "Text", "selectPermission": "Option", "tokenEnabled": "启用", "enableToken": "Text", "disableToken": "禁用", "tokenExpiryNever": "Text", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Description", "noPermission": "无权进行此操作"}, "topup": {"onlineRecharge": "Text", "cardRedemption": "兌換碼核銷", "accountBalance": "Text", "rechargeReminder": "充值提醒", "reminder1": "Text", "reminder2": "Text", "reminder3": "Text", "reminder4WithTransfer": "Text", "reminder4WithoutTransfer": "Text", "days": "天", "paymentSuccess": "Success", "paymentError": "支付出錯", "paymentAmount": "Text", "purchaseAmount": "購買額度：$", "yuan": "Text", "or": "Text", "usd": "美金", "cny": "Text", "enterAmount": "Text", "amountPlaceholder": "Please enter...", "amountUpdateError": "更新金額時出錯", "alipay": "Text", "wechat": "微信", "visaMastercard": "Text", "cardFormatError": "Error", "redeemSuccess": "Success", "redeemError": "Text", "enterCardKey": "Text", "cardKeyPlaceholder": "請輸入兌換碼卡密", "buyCardKey": "Text", "redeem": "立即核銷", "record": {"title": "Title", "amount": "充值額度", "payment": "Text", "paymentMethod": "支付方式", "orderNo": "Text", "status": "狀態", "createTime": "Time", "statusSuccess": "成功", "statusPending": "Status", "statusFailed": "失敗"}, "paymentMethodAlipay": "Text", "paymentMethodWxpay": "微信", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "Text", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "Text", "paymentMethodRedeem": "兌換碼", "recharge": {"title": "Title"}, "transfer": {"title": "餘額轉移"}, "history": {"title": "Title"}, "methods": {"alipay": "支付寶", "wechat": "Text", "stripe": "Stripe"}, "alipayF2F": "Text"}, "pricing": {"fetchErrorMessage": "Message", "availableModelErrorMessage": "Message", "modelName": "模型名稱", "billingType": "Text", "price": "價格", "ratio": "Text", "promptPriceSame": "Text", "completionPriceSame": "Text", "promptPrice": "提示價格：$ {{price}} / 1M tokens", "completionPrice": "Text", "promptRatioSame": "Text", "completionRatioSame": "Text", "promptRatio": "Text", "completionRatio": "補全倍率：{{ratio}}", "payAsYouGo": "Text", "fixedPrice": "Text", "payPerRequest": "按次付費 - Chat", "dynamicPrice": "Text", "payPerRequestAPI": "按次付費 - API", "loadingTip": "Tip", "userGroupRatio": "User", "readFailed": "讀取失敗", "billingFormula": "Text", "billingFormula1": "Text", "generatedBy": "Text", "modalTitle": "價格詳情", "perMillionTokens": "Text", "close": "關閉", "searchPlaceholder": "Please enter...", "viewGroups": "查看分組", "copiedToClipboard": "Text", "copyFailed": "複製失敗", "groupName": "Title", "availableGroups": "Text", "noGroupsAvailable": "沒有可用的分組", "modelGroupsErrorMessage": "Message", "currentGroup": "當前分組", "copyModelName": "Title", "groupRatio": "分組比率", "closeModal": "Text", "groupsForModel": "Text", "actions": "操作", "filterByGroup": "Text", "groupSwitched": "Text", "showAdjustedPrice": "Text", "model": {"title": "模型定價"}, "package": {"title": "Title"}, "custom": {"title": "Title"}}, "guestQuery": {"usageTime": "使用時間", "modelName": "Title", "promptTooltip": "輸入消耗 Tokens", "completionTooltip": "Tip", "quotaConsumed": "消耗額度", "pasteConfirm": "Action", "queryFailed": "查詢失敗", "tokenExpired": "Text", "tokenExhausted": "Text", "invalidToken": "請輸入正確的令牌", "focusRequired": "Text", "queryFirst": "請先查詢", "tokenInfoText": "Text", "unlimited": "Text", "neverExpire": "Text", "infoCopied": "Description", "copyFailed": "複製失敗", "noDataToExport": "Text", "prompt": "提示", "completion": "Text", "disabled": "Text", "tokenQuery": "令牌查詢", "tokenPlaceholder": "Please enter...", "tokenInfo": "令牌資訊", "copyInfo": "Description", "totalQuota": "令牌總額", "usedQuota": "Text", "remainQuota": "令牌餘額", "callCount": "Text", "validUntil": "Text", "currentRPM": "當前RPM", "currentTPM": "Text", "callLogs": "Text", "exportLogs": "導出日誌"}, "agencyProfile": {"fetchError": "Error", "fetchCommissionError": "獲取佣金列表失敗", "systemPreset": "Text", "lowerRatioWarning": "Warning", "lowerRatioMessage": "Message", "cancelRatioEdit": "取消修改費率", "updateSuccess": "Success", "updateError": "Error", "updateFailed": "更新失敗", "customPriceUpdateSuccess": "Success", "customPriceUpdateError": "自定義價格更新失敗：{{message}}", "time": "Time", "type": "類型", "agencyCommission": "Text", "unknownType": "未知類型", "amount": "Text", "balance": "餘額", "description": "Description", "group": "分組", "customRate": "Text", "systemDefaultRate": "Text", "action": "操作", "save": "Text", "cancel": "取消", "edit": "Text", "agencyConsole": "Text", "agencyInfo": "代理商資訊", "editInfo": "Description", "agencyName": "Title", "agencyLevel": "代理商等級", "level1": "Lv 1", "subordinateUsers": "User", "totalSales": "總銷售額", "commissionIncome": "Text", "cumulativeEarnings": "累積收益", "agencyFunctions": "Text", "hideSubordinateUsers": "User", "viewSubordinateUsers": "查看下級用戶", "hideCommissionDetails": "Text", "viewCommissionDetails": "查看佣金明細", "hideCustomPrice": "Text", "setCustomPrice": "設定自訂價格", "subordinateUsersList": "User", "commissionRecords": "佣金紀錄", "customPriceSettings": "Text", "saveChanges": "保存變更", "editAgencyInfo": "Description", "logo": "標誌", "setAgencyLogo": "Text", "customHomepage": "自訂首頁", "aboutContent": "Description", "newHomepageConfig": "Text", "customAnnouncement": "自訂公告", "customRechargeGroupRateJson": "Text", "customRechargeRate": "自訂充值費率", "viewSystemDefaultRate": "Text", "rateComparison": "費率比較", "comparisonResult": "Text", "higherThanSystem": "高於系統", "lowerThanSystem": "Text", "equalToSystem": "等於系統", "unknown": "Text", "notAnAgentYet": "Text", "becomeAnAgent": "成為代理商", "startYourOnlineBusiness": "Text", "becomeOurAgent": "Text", "noInventory": "Text", "instantCommission": "Text", "easyManagement": "Text", "flexibleDomainChoice": "🌐 彈性的域名選擇", "youCan": "Text", "useOwnDomain": "Text", "orUseOurSubdomain": "Text", "convenientStart": "Text", "actNow": "🚀 立即行動！", "contactAdmin": "Text", "applyNow": "立即申請", "contactCooperation": "Text", "understandPolicy": "Text", "provideDomain": "提供網域名稱", "configDomain": "Text", "promoteAndEarn": "推廣獲利", "startPromoting": "Text", "noDeploymentWorries": "Text", "easySetup": "Text", "customizeContent": "Description", "commissionBenefits": "Text", "joinNowBenefit": "Text", "groups": {"student": "Text", "studentDesc": "Text", "partTime": "Time", "partTimeDesc": "Text", "mediaWorker": "Text", "mediaWorkerDesc": "Text", "freelancer": "Text", "freelancerDesc": "Text", "title": "Title", "subtitle": "Title"}, "stories": {"story1": {"name": "張先生", "role": "Text"}, "story2": {"name": "李女士", "role": "Text"}, "story3": {"name": "劉先生", "role": "Text"}, "story4": {"name": "Title", "role": "自媒體"}, "story5": {"name": "Title", "role": "Text"}, "story6": {"name": "王女士", "role": "Text"}, "story7": {"name": "黃女士", "role": "Text"}, "story8": {"name": "劉先生", "role": "Text"}, "title": "Title", "subtitle": "Title"}, "earnedAmount": "已賺取{{amount}}", "applyForAgentNow": "Text", "businessLinesConnected": "Text", "agencyJoin": "代理加盟", "becomeExclusiveAgent": "Text", "startBusinessJourney": "Text", "welcomeToAgencyPage": "Text", "earningsTitle": "Title", "becomeAgentSteps": "成為代理商的步驟", "agencyRules": "Text", "suitableGroups": "適合人群", "agencyImages": {"becomeAgent": "Text", "agencyBusiness": "代理業務"}, "rules": {"howToEstablishRelation": "Text", "howToEstablishRelationAnswer": "Text", "canSetPrice": "我可以設定售價嗎？", "canSetPriceAnswer": "Text", "commissionShare": "Text", "commissionShareAnswer": {"assumption": "Text", "example": "Text", "calculation": "Text", "explanation": "Text"}, "title": "Title", "subtitle": "Title"}, "title": "代理商資料", "basicInfo": "Description", "commissionSettings": "佣金設置", "statistics": "Text", "totalUsers": "總用戶數", "totalRevenue": "Text", "monthlyRevenue": "月收入", "commissionRate": "Text", "activeUsers": "活躍用戶", "pendingCommission": "Text", "paidCommission": "已結算佣金"}, "error": {"title": "Title", "content": "Description"}, "loading": {"title": "載入中", "content": "Description"}, "notfound": {"title": "404", "content": "Description"}, "servererror": {"title": "500", "content": "Description"}, "unauthorized": {"title": "401", "content": "Description"}, "forbidden": {"title": "403", "content": "Description"}, "networkerror": {"title": "網路錯誤", "content": "Description"}, "timeout": {"title": "超時", "content": "Description"}, "noresult": {"title": "無結果", "content": "Description"}, "nopermission": {"title": "無權限", "content": "Description"}, "channelBridge": {"title": "Title", "channelPlatform": "渠道平台", "billingMethod": "Text", "channelName": "渠道名稱", "remark": "Text", "availableGroups": "Text", "availableModels": "可用模型", "channelKey": "Text", "proxyAddress": "對接地址", "cancel": "Text", "submit": "提交", "gpt35Models": "Text", "gpt4Models": "GPT-4模型", "clear": "Text", "customModelName": "Title", "add": "添加", "moreConfigReminder": "Text", "quickIntegration": "一鍵對接", "selectBillingMethod": "Option", "enterChannelName": "請輸入渠道名稱", "enterChannelRemark": "Text", "selectAvailableGroups": "Option", "selectAvailableModels": "Text", "enterChannelKey": "Text", "proxyAddressPlaceholder": "Text", "includes16kModels": "Text", "excludes32kModels": "Text", "cleared": "已清空", "addCustomModel": "Text", "clipboardTokenDetected": "Text", "channelIntegrationSuccess": "渠道對接成功！", "channelIntegrationFailed": "Error"}, "about": {"loading": "獲取最新內容...", "noContent": "Description", "loadFailed": "載入關於內容失敗..."}, "onlineTopupRecord": {"title": "Title", "columns": {"id": "身份證明", "username": "Title", "amount": "Text", "money": "支付金額", "paymentMethod": "Text", "tradeNo": "訂單號碼", "status": "Status", "createTime": "Time"}, "status": {"success": "成功", "pending": "Status", "failed": "失敗"}, "paymentMethod": {"alipay": "Text", "wxpay": "微信", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "Text", "paypal": "PayPal"}}, "logContentDetail": {"description": "Description", "downstreamError": "下游錯誤", "originalError": "Description", "requestParams": "請求參數", "copy": "Description"}, "viewMode": {"switchTo": "Text", "cost": "成本", "usage": "Text"}, "agenciesTable": {"title": "Title", "addAgency": "新增代理商", "columns": {"id": "Text", "userId": "使用者ID", "name": "Title", "domain": "域名", "commissionRate": "Text", "salesVolume": "銷售額", "userCount": "User", "commissionIncome": "Text", "historicalCommission": "累積收益", "actions": "Action"}, "confirm": {"deleteTitle": "Action", "updateName": "正在更新代理商名稱...", "updateSuccess": "Action", "updateFailed": "更新失敗", "deleteSuccess": "Action"}, "messages": {"getListFailed": "Message", "deleteSuccess": "刪除成功！", "loadingData": "Message"}}, "units": {"times": "次", "percentage": "{{value}}%", "formatUsage": "Text"}, "dailyUsage": {"total": "總計", "totalCost": "Text", "tooltipTitle": {"cost": "Title", "usage": "使用情況"}, "yAxisName": {"cost": "Title", "usage": "Title"}}, "dailyUsageByModel": {"total": "總計", "tooltipTotal": "Tip", "switchTo": "Text", "cost": "成本", "usage": "Text", "perspective": "視角", "granularity": {"hour": "Text", "day": "按天", "week": "Text", "month": "按月"}}, "checkinModal": {"title": "Title", "captchaPlaceholder": "驗證碼", "confirm": "Action", "close": "關閉"}, "balanceTransfer": {"title": "Title", "accountInfo": {"balance": "帳戶餘額", "transferFee": "Description", "groupNote": "Description"}, "form": {"receiverId": "接收者ID", "receiverUsername": "Title", "remark": "備註資訊", "amount": "Text", "expectedFee": "預計扣費", "submit": "Action"}, "result": {"success": "轉帳成功", "continueTransfer": "Text", "viewRecord": "查看紀錄"}, "warning": {"disabled": "Warning"}, "placeholder": {"autoCalculate": "填寫轉帳金額自動計算"}}, "channelsTable": {"title": "Title", "columns": {"id": "身份證明", "name": "Title", "type": "類型", "key": "Text", "base": "Text", "models": "模型", "weight": "Text", "priority": "Text", "retryInterval": "Text", "responseTime": "響應時間", "rpm": "RPM", "status": "Status", "quota": "餘額", "expireTime": "Time", "group": "分組", "billingType": "Text", "actions": "操作", "fusing": "Text", "sort": "Text", "balance": "餘額", "balanceUpdatedTime": "Time", "testTime": "測試時間", "createdTime": "Time", "disableReason": "禁用原因"}, "status": {"all": "Status", "normal": "正常", "enabled": "Status", "manualDisabled": "手動禁用", "waitingRetry": "Status", "suspended": "暫停使用", "specified": "Status", "allDisabled": "禁用", "specifiedDisabled": "Status", "partiallyDisabled": "部分禁用"}, "placeholder": {"selectGroup": "Please enter...", "selectStatus": "Status", "inputSelectModel": "Please enter...", "selectFusingStatus": "選擇自動熔斷狀態"}, "quota": {"usageAmount": "Text", "remainingAmount": "剩餘：{amount}", "customTotalAmount": "Text", "updateNotSupported": "Time", "details": "詳情", "sufficient": "Text"}, "actions": {"edit": "編輯", "copy": "Action", "delete": "刪除", "enable": "Action", "disable": "禁用", "test": "Action", "advancedTest": "Action", "viewLog": "渠道日誌", "viewAbility": "Action", "cleanUsage": "清空已用", "updateBalance": "Action", "copyKey": "複製密鑰", "topup": "Action", "viewModels": "Action"}, "confirm": {"deleteTitle": "刪除確認", "deleteContent": "Action", "cleanUsageTitle": "清空使用量確認", "cleanUsageContent": "Text", "testTitle": "Action", "testContent": "Action", "testNote": "Text", "deleteDisabledTitle": "Action", "deleteDisabledContent": "Action"}, "messages": {"operationSuccess": "操作成功", "operationSuccessWithSort": "Message", "operationFailed": "操作失敗：{{message}}", "testRunning": "Message", "testSuccess": "Message", "testFailed": "Message", "testStarted": "Message", "testOperationFailed": "Message", "deleteSuccess": "Message", "deleteFailed": "刪除失敗：{{message}}", "modelPrefix": "Message", "channelInfo": "Description", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "Message", "updateBalanceFailed": "Message", "updateAllBalanceStarted": "開始更新所有正常狀態的渠道餘額", "updateAllBalanceSuccess": "Message", "fetchGroupError": "獲取渠道組數據出錯：{{response}}", "fetchChannelError": "Message", "selectChannelFirst": "Message", "deleteDisabledSuccess": "Message", "deleteOperationFailed": "Message", "copySuccess": "Message", "copyFailed": "複製失敗：{{message}}", "emptyKey": "Message", "enableSuccess": "啟用成功", "disableSuccess": "Message", "updateSuccess": "更新成功", "deleteConfirm": "Action", "batchDeleteConfirm": "Action", "testSuccessWithWarnings": "Message", "viewDetails": "Message", "fetchChannelDetailError": "Message", "topupSuccess": "充值成功", "topupFailed": "Message"}, "popover": {"channelInfo": "渠道資訊"}, "menu": {"deleteManualDisabled": "Text", "deleteWaitingRetry": "刪除等待重啟渠道", "deleteSuspended": "Text", "testAll": "測試所有渠道", "testNormal": "Text", "testManualDisabled": "Text", "testWaitingRetry": "測試等待重啟渠道", "testSuspended": "Text", "deleteDisabledAccount": "刪除停用賬號", "deleteQuotaExceeded": "Text", "deleteRateLimitExceeded": "删除频率限制渠道", "deleteInvalidKey": "Text", "deleteConnectionError": "删除连接错误渠道", "deleteInternalServerError": "刪除內部伺服器錯誤渠道"}, "tooltip": {"testNote": "Tip"}, "disableReasons": {"account_deactivated": "Text", "quota_exceeded": "Text", "rate_limit_exceeded": "频率限制", "invalid_key": "Text", "connection_error": "连接错误", "internal_server_error": "內部伺服器錯誤"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "額度", "times": "Time"}, "serverLogViewer": {"title": "Title", "connecting": "正在連接伺服器...", "downloadSelect": "Option", "nginxConfig": "Text", "directAccess": "Text", "domainAccess": "Text", "buttons": {"pause": "暫停", "resume": "Action", "clear": "清空"}, "errors": {"fetchFailed": "Error", "downloadFailed": "下載日誌檔案失敗", "wsError": "Error"}}, "channelScore": {"score": "得分", "successRate": "Success", "avgResponseTime": "Time", "title": "渠道得分", "hourlyTitle": "Title", "dailyTitle": "渠道日得分", "weeklyTitle": "Title", "monthlyTitle": "渠道月得分", "allTimeTitle": "Title", "infoTooltip": "Description", "tableView": "表格視圖", "chartView": "Text", "refresh": "刷新", "selectModel": "Option", "allModels": "所有模型", "sortByScore": "Text", "sortBySuccessRate": "Success", "sortByResponseTime": "Time", "noData": "暫無數據", "totalItems": "Text", "fetchError": "Error", "aboutScoring": "關於得分計算", "scoringExplanation": "Text", "successRateWeight": "成功率權重 (70%)", "successRateExplanation": "Success", "responseTimeWeight": "Time", "responseTimeExplanation": "Time", "columns": {"rank": "排名", "channelId": "Text", "channelName": "Title", "model": "模型", "totalRequests": "Text", "successRequests": "Success", "failedRequests": "失敗請求數", "successRate": "Success", "avgResponseTime": "Time", "score": "綜合得分", "actions": "Action"}, "actions": {"viewDetails": "Action", "test": "測試渠道", "edit": "Action"}, "tooltips": {"excellent": "優秀", "good": "Tip", "average": "一般", "poor": "Tip", "veryPoor": "很差"}, "scoringExplanation100": "Text"}, "menu": {"channelScores": "渠道得分"}, "relay": {"dispatchOptions": "Option", "preciseWeightCalculation": "Text", "preciseWeightCalculationTip": "Tip", "channelMetricsEnabled": "啟用渠道指標統計", "channelMetricsEnabledTip": "Text", "channelScoreRoutingEnabled": "Text", "channelScoreRoutingEnabledTip": "Tip", "globalIgnoreBillingTypeFilteringEnabled": "全局忽略计费方式篩選", "globalIgnoreBillingTypeFilteringEnabledTip": "Text", "globalIgnoreFunctionCallFilteringEnabled": "Text", "globalIgnoreFunctionCallFilteringEnabledTip": "Text", "globalIgnoreImageSupportFilteringEnabled": "Text", "globalIgnoreImageSupportFilteringEnabledTip": "Text"}, "dynamicRouter": {"title": "Title", "reloadRoutes": "Text", "exportConfig": "導出配置", "clearConfig": "Text", "importantNotice": "重要提示", "reloadLimitation": "Text", "exportDescription": "Description", "clearDescription": "Text", "routeGroups": "Text", "upstreamConfig": "Text", "endpointConfig": "端點配置", "editRouteGroup": "Text", "editUpstream": "Text", "editEndpoint": "編輯端點配置", "editJSON": "Text", "confirmClear": "Action", "confirmClearMessage": "Text", "configCleared": "Text", "configExported": "配置已成功匯出到檔案", "configReloaded": "Text"}, "notification": {"webhookConfig": "Webhook配置", "telegramConfig": "Message", "wxpusherConfig": "WxPusher配置", "qywxbotConfig": "Message", "dingtalkConfig": "釘釘機器人配置", "feishuConfig": "Message", "title": "通知設置", "subscriptionEvents": "Message", "notificationMethods": "通知方式", "alertSettings": "Message", "emailConfig": "郵件配置", "customEmails": "Message", "addEmail": "添加郵箱", "removeEmail": "Message", "emailPlaceholder": "Message", "emailTooltip": "Message", "webhookUrl": "Webhook地址", "webhookUrlPlaceholder": "Message", "webhookSecret": "Message", "webhookSecretPlaceholder": "Message", "telegramBotToken": "Bot <PERSON>", "telegramBotTokenPlaceholder": "Message", "telegramChatId": "Chat ID", "telegramChatIdPlaceholder": "Message", "wxpusherAppToken": "<PERSON><PERSON>", "wxpusherAppTokenPlaceholder": "Message", "wxpusherUids": "Message", "wxpusherUidsPlaceholder": "Message", "qywxbotWebhookUrl": "Webhook地址", "qywxbotWebhookUrlPlaceholder": "Message", "dingtalkWebhookUrl": "Webhook地址", "dingtalkWebhookUrlPlaceholder": "Message", "dingtalkSecret": "加簽密鑰", "dingtalkSecretPlaceholder": "Message", "feishuWebhookUrl": "Message", "feishuWebhookUrlPlaceholder": "Message", "feishuSecret": "簽名校驗", "feishuSecretPlaceholder": "Message", "events": {"lowBalance": "餘額不足", "quotaExpiry": "Message", "securityAlert": "安全警報", "systemAnnouncement": "Message", "promotionalActivity": "促銷活動", "modelPricingUpdate": "Message", "account_balance_low": "余额不足预警", "account_quota_expiry": "Message", "security_alert": "安全警报", "system_announcement": "Message", "promotional_activity": "Message", "model_pricing_update": "模型价格更新", "anti_loss_contact": "Message"}, "lowBalanceThreshold": "Message", "lowBalanceThresholdPlaceholder": "Message", "quotaExpiryThreshold": "額度過期提醒天數", "quotaExpiryThresholdPlaceholder": "Message", "selectEvents": "Message", "eventsDescription": "Description", "selectMethods": "選擇接收通知的方式", "methodsDescription": "Description", "description": "Description", "recommended": "Message", "important": "Message", "testRecommendation": "Message", "testNotification": "測試通知", "testMessage": "Message", "testSuccess": "測試通知發送成功", "testFailed": "Message", "saveSuccess": "通知設置保存成功", "saveFailed": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Description", "alertExplanationTitle": "预警说明", "alertExplanation": "Message", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "請輸入Telegram Bot Token"}, "qywxbotGuide": "Message", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkGuide": "Message", "feishuGuide": "飞书机器人配置指南", "webhookGuide": "Message", "webhookToken": "接口凭证", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramGuide": "Message", "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Message", "detailedDocumentation": "詳細文檔：", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "企業微信群機器人配置说明", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherUserUID": "用戶UID", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Message", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定義機器人接入", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "設置機器人名稱和描述", "feishuStep4": "Message", "feishuStep5": "複製生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "複製获得的Bot Token", "telegramStep5": "Message", "telegramStep6": "访问 https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "消息格式支持：", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建議啟用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "注意事项：", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Chat ID格式说明：", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "• 群組中需要先将機器人添加为成员", "telegramChannelPermission": "Message", "webhookCallUrl": "呼叫地址", "webhookConfigurationGuide": "Message", "webhookDataFormatExample": "資料格式範例：", "webhookConfigurationInstructions": "Message", "webhookRequestMethod": "Message", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "Message", "webhookTimeout": "Message", "webhookRetryMechanism": "Message", "webhookTip": "Message", "telegramStep3Detailed": "Message", "telegramPersonalChatDetailed": "Message", "telegramGroupChatDetailed": "Message", "telegramChannelDetailed": "Message", "telegramQuickChatIdTitle": "Message", "telegramQuickStep1": "Message", "telegramQuickStep2": "Message", "telegramQuickStep3": "Message"}, "legal": {"privacyPolicy": {"title": "Title", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "信息收集", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Text"}}, "informationUsage": {"title": "Title", "description": "Description", "items": ["提供和维护我们的服务", "Description", "改进服务质量和用户体验", "Description", "防止欺诈和滥用"]}, "informationSharing": {"title": "Title", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "数据安全", "description": "Description", "items": ["数据加密传输和存储", "Text", "定期安全审计和更新", "Text"]}, "dataRetention": {"title": "数据保留", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "您的权利", "description": "Description", "items": ["User", "删除您的账户和相关数据", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["维持用户会话", "Text", "Text"]}, "thirdPartyServices": {"title": "第三方服务", "description": "Description", "items": ["Text", "GitHub OAuth：用于用户身份验证", "Text"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "服务描述", "description": "Description", "items": ["API 密钥管理", "Description", "使用统计和监控", "Description", "Description"]}, "userAccount": {"title": "用户账户", "description": "Description", "items": ["Text", "User", "User", "及时更新账户信息", "User"]}, "usageRules": {"title": "使用规则", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "服务可用性", "description": "Description", "items": ["Text", "Text", "Text", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "高级功能可能需要付费", "Text", "Text"]}, "intellectualProperty": {"title": "知识产权", "description": "Description", "items": ["Text", "您获得有限的使用许可", "Text", "Text"]}, "privacyProtection": {"title": "隐私保护", "description": "Description", "items": ["Text", "采取合理措施保护数据安全", "Text"]}, "disclaimer": {"title": "免责声明", "description": "Description", "items": ["Text", "Text", "不对间接损失承担责任", "Text"]}, "serviceTermination": {"title": "服务终止", "description": "Description", "items": ["您违反这些条款", "Text", "Text", "法律要求"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["重大变更会提前通知", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "Text", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "Text"}}}