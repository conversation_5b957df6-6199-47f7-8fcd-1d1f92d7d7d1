{"message": {"copyModelSuccess": "Der Modellname wurde in die Zwischenablage kopiert!", "copyFailed": "<PERSON><PERSON><PERSON> feh<PERSON>, bitte manuell kopieren.", "logoutSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> erfo<PERSON>g<PERSON>ich", "loginSuccess": {"default": "Anmeldung erfolgreich", "welcomeBack": "Willkommen zurück"}, "removeLocalStorage": {"confirm": "Möchten Sie den lokalen Cache löschen?", "success": "Lokaler <PERSON><PERSON> erfolg<PERSON>ich <PERSON>."}, "loadData": {"error": "Laden der {{name}}-Daten fehlgeschlagen."}, "noNotice": "Derzeit keine Ankündigungsinhalte.", "verification": {"turnstileChecking": "Turnstile überprüft die Benutzerumgebung!", "pleaseWait": "Bitte versuchen Sie es später erneut."}, "clipboard": {"inviteCodeDetected": "Einladungscode erkannt, automatisch ausgefüllt!", "clickToCopy": "<PERSON><PERSON><PERSON> Sie auf Kopieren.", "copySuccess": "<PERSON><PERSON>"}}, "common": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "copyAll": "Alles kopieren", "all": "Alles", "more": "mehr", "unlimited": "unbegrenzt", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Schließen", "save": "Speichern", "cancel": "Abbrechen", "create": "<PERSON><PERSON><PERSON><PERSON>", "usd": "US-Dollar", "day": "{{count}} <PERSON>e", "day_plural": "{{count}} <PERSON>e", "days": "<PERSON><PERSON>", "seconds": "Sekunde", "times": "nächste", "submit": "Senden", "bind": "<PERSON>den", "unknown": "Unbekannt", "loading": "Laden...", "copyFailed": "<PERSON><PERSON>ren fehlgeschlagen.", "people": "Menschen", "ok": "Bestätigen", "close": "Schließen", "copied": "<PERSON><PERSON><PERSON>.", "expand": "Entfalten", "collapse": "Einpacken", "none": "keine", "remark": "Anmerkung", "selectPlaceholder": "<PERSON>te wählen Sie {{name}}.", "on": "öffnen", "off": "geschlossen", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Anzeigename", "description": "Beschreibung", "ratio": "Vergrößerungsfaktor", "unnamed": "Unbenannter Kanal", "groups": "Gruppierung", "captchaPlaceholder": "Bitte geben Sie den Bestätigungscode ein.", "confirm": "Bestätigen", "permissions": "Berechtigungen", "actions": "Aktionen", "createdTime": "Erstellungszeit", "expiredTime": "Ablaufzeit", "search": "<PERSON><PERSON>", "reset": "Z<PERSON>ücksetzen", "refresh": "Aktualisieren", "pagination": {"total": "Gesamt {{total}} Einträge"}, "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "add": "Hinzufügen", "update": "Aktualisieren", "back": "Zurück", "next": "<PERSON><PERSON>", "previous": "Zurück", "success": "Erfolg", "error": "<PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "info": "Information"}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "<PERSON><PERSON><PERSON>, um den Link zu öffnen."}, "userRole": {"normal": "<PERSON><PERSON>", "agent": "<PERSON><PERSON><PERSON><PERSON>", "admin": "Administrator", "superAdmin": "Superadministrator", "loading": "Wird geladen..."}, "channelStatus": {"enabled": "Aktivieren", "disabled": "Deaktivieren", "waitingRestart": "<PERSON><PERSON> auf Neustart", "waiting": "<PERSON><PERSON>", "autoStoppedTitle": "Der Kanal hat die maximale Anzahl an automatischen Wiederholungen überschritten oder die Bedingungen für die automatische Deaktivierung ausgelöst.", "stopped": "Deaktivieren", "partiallyDisabled": "Teil<PERSON><PERSON>", "unknown": "Unbekannt", "reason": "<PERSON><PERSON><PERSON>"}, "channelBillingTypes": {"payAsYouGo": "Nach Verbrauch abrechnen", "payPerRequest": "Nach Verbrauch abrechnen", "unknown": "Unbekannte Methode"}, "tokenStatus": {"normal": "normal", "disabled": "Deaktivieren", "expired": "abgelaufen", "exhausted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "Unbekannt"}, "userStatus": {"normal": "normal", "banned": "<PERSON><PERSON><PERSON>", "unknown": "Unbekannt"}, "redemptionStatus": {"normal": "normal", "disabled": "Deaktivieren", "redeemed": "<PERSON><PERSON><PERSON><PERSON>", "expired": "abgelaufen", "unknown": "Unbekannt"}, "duration": {"request": "Anfrage", "firstByte": "<PERSON><PERSON><PERSON>", "total": "Gesamtbetrag", "seconds": "Sekunde", "lessThanOneSecond": "<1 Sekunde"}, "streamType": {"stream": "Streaming", "nonStream": "Nicht-streaming"}, "noSet": {"title": "Der Administrator hat {{name}} nicht festgelegt.", "name": {"about": "<PERSON><PERSON>", "chat": "Gesprä<PERSON>"}}, "buttonText": {"add": "<PERSON><PERSON>", "cancel": "Stornieren", "confirm": "Bestätigen", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "save": "Speichern", "updateBalance": "Kontostand aktualisieren", "test": "<PERSON><PERSON>", "multiple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "channelPage": {"title": "Kanalmanagement"}, "channelStatusCount": {"title": "Kanalstatusstatistik", "summary": "Aktiviert {{enabled}} | Deaktiviert {{disabled}} | Wiederholungsversuch {{retry}} | Gestoppt {{stopped}}", "statusEnabled": "Aktiviert", "statusDisabled": "Deaktiviert", "statusRetry": "Wird erneut versucht.", "statusStopped": "Gestoppt", "statusPartially": "Teil<PERSON><PERSON>"}, "header": {"routes": {"status": "Zustand", "home": "Startseite", "chat": "Gesprä<PERSON>", "pptGen": "PPT-Erstellung", "chart": "Statistik", "agency": "<PERSON><PERSON><PERSON><PERSON>", "channel": "<PERSON><PERSON>", "ability": "Kanalfähigkeit", "channelGroup": "Kanalgruppe", "token": "Token", "log": "Protokoll", "logDetail": "Details", "midjourney": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "config": "Konfiguration", "packagePlanAdmin": "<PERSON><PERSON>", "redemption": "Einlösecode", "group": "Gruppierung", "query": "Abfrage", "about": "<PERSON><PERSON>", "setting": {"default": "Einstellungen", "operation": "Betriebseinstellungen", "system": "Systemeinstellungen", "global": "Globale Einstellungen", "advance": "Eigenschaftseinstellungen", "sensitive": "Konfigu<PERSON> von <PERSON>", "verification": "Konfiguration des Bestätigungscodes", "update": "Überprüfen Sie auf Updates"}, "account": {"default": "Ko<PERSON>", "profile": "Persönliches Zentrum", "cardTopup": "Karten-Code-Einlösung", "onlineTopup": "Online-Aufladung", "recharge": "Guthabenaufladung", "balanceTransfer": "Guthabenübertragung", "pricing": "Kostenbeschreibung", "packagePlan": {"list": "<PERSON><PERSON><PERSON><PERSON>", "record": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notificationSettings": "Benachrichtigungseinstellungen"}, "tools": {"default": "Werkzeug", "fileUpload": "<PERSON><PERSON> ho<PERSON>n", "keyExtraction": "Schlüsselextraktion", "multiplierCalculator": "Verhältnisrechner", "shortLink": "Kurzlink-Generierung", "testConnection": "Zugriffstest", "customPrompts": "Prompt-Management", "redis": "Redis Visualisierung", "ratioCompare": "Vergleich der Vergrößerung", "serverLog": "Server-Log-Viewer"}, "onlineTopupRecord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "channelScores": "Kanalbewertung", "dynamicRouter": "Dynamisches Routing", "task": "Asynchrone Aufgaben", "agencyJoin": "Agentur-Partnerschaft"}, "dropdownMenu": {"profile": "Persönliches Zentrum", "recharge": "Guthabenaufladung", "agencyCenter": "Agenturzentrum", "checkin": "<PERSON><PERSON><PERSON><PERSON>", "darkMode": {"enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disable": "Tagesmodus"}, "fullScreen": {"default": "Vollbildmo<PERSON>", "enable": "Vollbildmodus", "disable": "Vollbild<PERSON><PERSON>den"}, "logout": "Abmelden"}, "checkin": {"default": "<PERSON><PERSON><PERSON><PERSON>", "success": "Erfolgreich angemeldet", "failed": "Anmeldung fehlgeschlagen", "verification": "Bitte führen Sie die Überprüfung durch."}, "avatarProps": {"login": "Anmelden"}}, "settings": {"public": {"titles": {"default": "Öffentliche Einrichtungen"}, "SystemName": "Systemname", "ServerAddress": "Serviceadresse", "TopUpLink": "Auflade-Link", "ChatLink": "Dialog-Link", "Logo": "System-Logo", "HomePageContent": "Startseite Inhalt", "About": "Über den Inhalt", "Notice": "Ankündigungsinhalt", "Footer": "Fußzeileninhalt", "RegisterInfo": "Registrierungsbenachrichtigung", "HeaderScript": "Benutzerdefinierter Header", "SiteDescription": "Standortbeschreibung", "PrivacyPolicy": "Datenschutzrichtlinie", "ServiceAgreement": "Dienstleistungsvereinbarung", "FloatButton": {"FloatButtonEnabled": "<PERSON><PERSON><PERSON>", "DocumentInfo": "Dokumentinformationen", "WechatInfo": "WeChat-Nachricht", "QqInfo": "Q Q Informationen"}, "CustomThemeConfig": "Benutzerdefiniertes Thema", "AppList": "Freundschaftslinks"}}, "home": {"default": {"title": "Willkommen!", "subtitle": "Basierend auf der One API-Weiterentwicklung werden umfassendere Funktionen bereitgestellt.", "start": "Beginnen Sie zu verwenden", "description": {"title": "Neue Funktionen:", "part1": "Völlig neue Benutzeroberfläche, bequem und schnell.", "part2": "Optimierung des Planungssystems, effizient und stabil.", "part3": "<PERSON>ür die Entwicklung von Unternehmen, sicher und zuverlässig.", "part4": "Mehr fortgeschrittene Funktionen warten darauf, von dir entdeckt zu werden."}}}, "dailyUsageChart": {"title": "Tägliche Modellnutzung", "yAxisName": "<PERSON><PERSON><PERSON><PERSON> (USD)", "loadingTip": "Tägliche Nutzungssituation", "fetchError": "Fehler beim Abrufen der täglichen Nutzungsdaten:"}, "modelUsageChart": {"title": "Modellnutzung", "hourlyTitle": "Nutzung des Modells pro Stunde", "dailyTitle": "Nutzung des Modells pro Tag", "weeklyTitle": "Nutzung des Modells pro Woche", "monthlyTitle": "Nutzung des Modells pro Monat"}, "granularity": {"hour": "jede <PERSON>", "day": "jeden <PERSON>", "week": "jede <PERSON>", "month": "monatlich", "all": "Alles"}, "abilitiesTable": {"title": "Kanalfähigkeit", "export": "Exportieren", "group": "Gruppe", "model": "<PERSON><PERSON>", "channelId": "Kanalnummer", "enabled": "Aktiviert", "weight": "Gewichtung", "priority": "Priorität", "billingType": "Abrechnungsart", "functionCallEnabled": "Funktionalitätsaufruf aktiviert", "imageSupported": "Unterstützung von Bildern", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "perToken": "Abrechnung nach Token", "perRequest": "<PERSON><PERSON> Anfrage a<PERSON>nen", "noDataToExport": "<PERSON>s gibt keine Daten zum Exportieren.", "exportConfirm": "Sind <PERSON> sic<PERSON>, dass Sie die Daten der aktuellen Seite exportieren möchten?", "exportSuccess": "Export erfolgreich", "toggleSuccess": "Wechsel erfolgreich", "toggleError": "Wechsel fehlgeschlagen.", "selectOrInputGroup": "<PERSON><PERSON>hlen oder geben Sie die Benutzergruppe ein."}, "logsTable": {"retry": "<PERSON><PERSON><PERSON> versuchen", "retryChannelList": "Wiederholungs-Kanalliste", "retryDurations": "Details zur Wiederholungsdauer", "channel": "<PERSON><PERSON>", "duration": "Z<PERSON>au<PERSON><PERSON><PERSON>", "startTime": "Startzeit", "endTime": "Endzeit", "retryCount": "Wiederholungsversuche", "retryDetails": "Wiederholungsdetails", "totalRetryTime": "Gesamtversuchszeit", "seconds": "Sekunde", "tokenGroup": "Token-Gruppe", "selectGroup": "Gruppenauswahl", "dailyModelUsageStats": "Datenübersicht aufrufen", "time": "Zeit", "moreInfo": "Weitere Informationen", "ip": "IP", "remoteIp": "Remote-IP", "ipTooltip": "IP: {{ip}}  \nRemote IP: {{remoteIp}}", "requestId": "Anforderungs-ID", "username": "<PERSON><PERSON><PERSON><PERSON>", "userId": "Benutzer-ID", "tokenName": "Token-Name", "token": "Token", "type": "<PERSON><PERSON>", "typeUnknown": "Unbekannt", "type充值": "Aufladen", "type消费": "Konsum", "type管理": "Verwaltung", "type系统": "System", "type邀请": "Einladung", "type提示": "<PERSON><PERSON><PERSON><PERSON>", "type警告": "<PERSON><PERSON><PERSON>", "type错误": "<PERSON><PERSON>", "type签到": "<PERSON><PERSON><PERSON><PERSON>", "type日志": "Protokoll", "type退款": "Rückerstattung", "type邀请奖励金划转": "Einladung Belohnungsübertragung", "type代理奖励": "Vertreterprämie", "type下游错误": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type测试渠道": "Testkanal", "typeRecharge": "Aufladen", "typeConsumption": "Konsum", "typeManagement": "Verwaltung", "typeSystem": "System", "typeInvitation": "Einladung", "typePrompt": "<PERSON><PERSON><PERSON><PERSON>", "typeWarning": "<PERSON><PERSON><PERSON>", "typeError": "<PERSON><PERSON>", "typeCheckin": "<PERSON><PERSON><PERSON><PERSON>", "typeLog": "Protokoll", "typeRefund": "Rückerstattung", "typeInviteReward": "Einladungsprämie Überweisung", "typeAgencyBonus": "Vertreterprämie", "typeDownstreamError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeChannelTest": "Testkanal", "channelId": "Kanal-ID", "channelName": "Kanalname", "model": "<PERSON><PERSON>", "modelPlaceholder": "Modellnamen eingeben/auswählen", "info": "Information", "isStream": "Streaming", "isStreamPlaceholder": "Eingabe/Auswahl, ob gestreamt werden soll", "prompt": "<PERSON><PERSON><PERSON><PERSON>", "completion": "Vervollständigen", "consumption": "Konsum", "consumptionRange": "Verbrauchsgrenzen", "description": "Erläuterung", "action": "Operation", "details": "Details", "tokenKey": "Token-Schlüssel", "requestDuration": "<PERSON><PERSON><PERSON><PERSON>s<PERSON><PERSON>", "firstByteDuration": "Erste Byte Zeit", "totalDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lessThanOneSecond": "<1 Sekunde", "modelInvocation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modelUsage": "Modellnutzung", "totalQuota": "Gesamtverbrauchsgrenze: {{quota}}", "totalRpm": "Anfragen pro Minute: {{rpm}}", "totalTpm": "Token pro Minute: {{tpm}}", "totalMpm": "Betrag/Minute: {{mpm}}", "dailyEstimate": "Voraussichtlicher Tagesverbrauch: {{estimate}}", "currentStats": "Aktuelle RPM: {{rpm}} Aktuelle TPM: {{tpm}} Aktuelle MPM: ${{mpm}} Geschätzter täglicher Verbrauch: ${{dailyEstimate}}", "statsTooltip": "Nur nicht archivierte Protokolle zählen, RPM: Anfragen pro Minute, TPM: To<PERSON> pro Minute, MPM: Geldverbrauch pro Minute, der geschätzte Tagesverbrauch wird auf der Grundlage des aktuellen MPM abgeleitet.", "showAll": "Alles anzeigen", "exportConfirm": "Exportieren Sie das Protokoll dieser Seite?", "export": "Exportieren", "statsData": "Statistische Daten", "today": "an diesem Tag", "lastHour": "1 Stunde", "last3Hours": "3 Stunden", "lastDay": "1 Tag", "last3Days": "3 Tage", "last7Days": "7 Tage", "lastMonth": "1 Monat", "last3Months": "3 Monate", "excludeModels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectModelsToExclude": "Wählen Sie das auszuschließende Modell aus.", "excludeErrorCodes": "Fehlercode ausschließen", "excludeErrorCodesPlaceholder": "Wählen Sie die auszuschließenden Fehlercodes aus.", "errorCode": "Fehlercode", "errorCodePlaceholder": "Eingabe/Auswahl Fehlercode", "timezoneTip": "Aktuelle Zeitzone: {timezone}", "timezoneNote": "Zeitzonenhinweis", "timezoneDescription": "Die Statistiken sind nach dem Datum in Ihrer aktuellen Zeitzone gruppiert. Unterschiedliche Zeitzonen können zu unterschiedlichen Zeiträumen bei der Gruppierung der Daten führen. Um Anpassungen vorzunehmen, besuchen Sie bitte Ihr persönliches Zentrum, um die Zeitzoneneinstellungen zu ändern.", "goToProfile": "Gehe zum persönlichen Zentrum.", "realtimeQuota": "Echtzeitverbrauch (1 Minute)", "viewTotalQuota": "Gesamtausgaben anzeigen", "viewTotalQuotaTip": "Überprüfen Sie den gesamten historischen Verbrauchsbetrag (die Abfrage kann einige Sekunden dauern).", "loadingTotalQuota": "<PERSON>te warten, während die Gesamtausgaben überprüft werden...", "totalQuotaTitle": "Historische Gesamtausgabenstatistik", "loadTotalQuotaError": "Fehler beim Abrufen des Gesamtverbrauchs.", "requestLogs": "Anforderungsprotokoll - {{requestId}}", "noRequestLogs": "<PERSON><PERSON>ungsprotokolle vorhanden.", "metricsExplanation": "Nur nicht archivierte Protokolle zählen, RPM: Anfragen pro Minute, TPM: To<PERSON> pro Minute, MPM: Geldverbrauch pro Minute, der geschätzte Tagesverbrauch wird auf der Grundlage des aktuellen MPM abgeleitet.", "autoRefresh": "Automatische Aktualisierung", "autoRefreshTip": "<PERSON><PERSON><PERSON>, um die automatische Aktualisierung ein- oder auszuschalten. Nach dem Aktivieren werden die Daten alle angegebenen Sekunden automatisch aktualisiert.", "autoRefreshOn": "Automatisches Aktualisieren ist aktiviert.", "autoRefreshOff": "Automatisches Aktualisieren wurde deaktiviert.", "refreshInterval": "Aktualisierungsintervall", "stopRefresh": "Stoppen Sie das Aktualisieren.", "secondsWithValue": "{{seconds}} <PERSON><PERSON><PERSON>", "minutesWithValue": "{{minutes}} Minuten", "title": "Log-Verwaltung", "columns": {"id": "ID", "createdTime": "Erstellungszeit", "type": "<PERSON><PERSON>", "content": "Inhalt", "username": "<PERSON><PERSON><PERSON><PERSON>", "tokenName": "Token-Name", "modelName": "<PERSON><PERSON><PERSON>", "quota": "<PERSON><PERSON><PERSON>", "promptTokens": "Prompt-Token", "completionTokens": "Vervollständigungs-Token"}, "actions": {"view": "Anzeigen", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"success": "Erfolg", "failed": "Fehlgeschlagen"}, "messages": {"retrySuccess": "Wiederholung erfolgreich", "retryFailed": "Wiederholung fehlgeschlagen"}}, "mjLogs": {"logId": "Protokoll-ID", "submitTime": "Einreichungszeitpunkt", "type": "<PERSON><PERSON>", "channelId": "Kanal-ID", "userId": "Benutzer-ID", "taskId": "Aufgaben-ID", "submit": "Einreichen", "status": "Zustand", "progress": "Fort<PERSON><PERSON>t", "duration": "Z<PERSON>au<PERSON><PERSON><PERSON>", "result": "<PERSON><PERSON><PERSON><PERSON>", "prompt": "Aufforderung", "promptEn": "EingabeaufforderungEn", "failReason": "<PERSON><PERSON><PERSON><PERSON>", "startTime": "Startzeit", "endTime": "Endzeit", "today": "an diesem Tag", "lastHour": "1 Stunde", "last3Hours": "3 Stunden", "lastDay": "1 Tag", "last3Days": "3 Tage", "last7Days": "7 Tage", "lastMonth": "1 Monat", "last3Months": "3 Monate", "selectTaskType": "Wählen Sie den Aufgabentyp aus.", "selectSubmitStatus": "Auswahl des Einreichungsstatus", "submitSuccess": "Erfolgreich eingereicht", "queueing": "Steht in der Warteschlange.", "duplicateSubmit": "Wiederholte Einreichung", "selectTaskStatus": "Aufgabenstatus aus<PERSON>en", "success": "Erfolg", "waiting": "<PERSON><PERSON>", "queued": "<PERSON>ten in der Schlange", "executing": "Ausführung", "failed": "<PERSON><PERSON>", "seconds": "Sekunde", "unknown": "Unbekannt", "viewImage": "<PERSON><PERSON><PERSON> hier, um anzuzeigen.", "markdownFormat": "Markdown-Format", "midjourneyTaskId": "Midjourney Aufgaben-ID", "copiedAsMarkdown": "Als Markdown-Format kopiert.", "copyFailed": "<PERSON><PERSON>ren fehlgeschlagen.", "copiedMidjourneyTaskId": "Midjourney-Aufgaben-ID wurde kopiert.", "drawingLogs": "Zeichentagebuch", "onlyUnarchived": "Nur nicht archivierte Protokolle zählen.", "imagePreview": "Bildvorschau", "copiedImageUrl": "Bildadresse wurde kop<PERSON>t.", "copy": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resultImage": "Ergebnisbild", "downloadError": "Bilddownload fehlgeschlagen.", "mode": "<PERSON><PERSON>", "selectMode": "<PERSON><PERSON><PERSON><PERSON>", "relax": "Ents<PERSON><PERSON>ngsmodus", "fast": "<PERSON><PERSON><PERSON>", "turbo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actions": "Operation", "refresh": "Aktualisieren", "refreshSuccess": "Aufgabenstatus erfolgreich aktualisiert.", "refreshFailed": "Aktualisierung des Aufgabenstatus fehlgeschlagen.", "refreshError": "Beim Aktualisieren des Aufgabenstatus ist ein Fehler aufgetreten.", "tasks": {"title": "Aufgabenliste", "taskId": "Aufgaben-ID", "platform": "Plattform", "type": "<PERSON><PERSON>", "status": "Status", "progress": "Fort<PERSON><PERSON>t", "submitTime": "Übermittlungszeit", "startTime": "Startzeit", "endTime": "Endzeit", "duration": "<PERSON><PERSON>", "result": "<PERSON><PERSON><PERSON><PERSON>", "taskIdPlaceholder": "Aufgaben-ID <PERSON>ben", "platformPlaceholder": "Plattform auswählen", "typePlaceholder": "Typ auswählen", "statusPlaceholder": "Status auswählen", "videoGeneration": "Video-Generierung", "imageGeneration": "Bild-Generierung", "viewError": "Fehler anzeigen", "taskDetails": "Aufgabendetails", "errorDetails": "Fehlerdetails", "loadError": "Fehler beim Laden der Aufgabenliste", "musicGeneration": "Musik-Generierung", "textGeneration": "Text-Gene<PERSON><PERSON>", "unknown": "Unbekannt", "success": "Erfolg", "failed": "Fehlgeschlagen", "inProgress": "In Bearbeitung", "submitted": "Eingereicht", "queued": "In Warteschlange", "notStarted": "<PERSON>cht gestartet", "viewResult": "Ergebnis anzeigen"}, "viewVideo": "Video anzeigen", "videoPreview": "Video-Vors<PERSON>u", "copyVideoUrl": "Video-<PERSON> k<PERSON>", "copiedVideoUrl": "Video-<PERSON>", "downloadVideo": "Video herunterladen", "videoNotSupported": "Nicht unterstütztes Videoformat", "finishTime": "Abschlusszeit", "imageUrl": "Bild-URL", "videoUrl": "Video-URL", "action": "Aktion", "model": "<PERSON><PERSON>", "platform": "Plattform", "videoUrls": "Video-URL-Liste"}, "mjTaskType": {"IMAGINE": "<PERSON><PERSON><PERSON> gene<PERSON>", "UPSCALE": "Vergrößern", "VARIATION": "Veränderung", "REROLL": "<PERSON><PERSON> gene<PERSON>", "DESCRIBE": "Bild wird Text.", "BLEND": "Mischbild", "OUTPAINT": "Zoom", "DEFAULT": "Unbekannt"}, "mjCode": {"submitSuccess": "Erfolgreich eingereicht", "queueing": "Steht in der Warteschlange.", "duplicateSubmit": "Wiederholte Einreichung", "unknown": "Unbekannt"}, "mjStatus": {"success": "Erfolg", "waiting": "<PERSON><PERSON>", "queued": "<PERSON>ten in der Schlange", "executing": "Ausführung", "failed": "<PERSON><PERSON>", "unknown": "Unbekannt"}, "tokensTable": {"title": "Token-Verwaltung", "table": {"title": "Token-Verwaltung", "toolBar": {"add": "Neues Token erstellen", "delete": "Token löschen", "deleteConfirm": "<PERSON><PERSON> werden {{count}} Token in großen Mengen gelöscht. Dieser Vorgang ist irreversibel.", "export": "Exportieren", "exportConfirm": "Aktuellen Seiten-Token exportieren?"}, "action": "Operation"}, "modal": {"title": {"add": "Neuen Token erstellen", "edit": "Bearbeitungs-Token"}, "field": {"name": "Token-Name", "description": "Token-Beschreibung", "type": {"default": "Abrechnungsart", "type1": "Nach Verbrauch abrechnen", "type2": "Nach Verbrauch abrechnen", "type3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type4": "Mengenpriorität", "type5": "Nach zweiter Priorität"}, "status": "Zustand", "statusEnabled": "normal", "statusDisabled": "Deaktivieren", "statusExpired": "abgelaufen", "statusExhausted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": "Verfügbare Modelle", "usedQuota": "Verbrauchsgrenze", "remainQuota": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createdTime": "Erstellungszeitpunkt", "expiredTime": "Ablaufdatum", "all": "Alles", "more": "mehr", "notEnabled": "Nicht aktiviert", "unlimited": "unbegrenzt", "daysLeft": "{{days}} Tage bis zum Ablauf", "expired": "Bereits abgelaufen seit {{days}} Tagen", "userId": "Benutzer-ID", "key": "API-Schlüssel", "neverExpire": "<PERSON><PERSON>"}, "delete": {"title": "Löschen", "content": "<PERSON>d <PERSON>, dass Si<PERSON> den API-Schlüssel {{name}} löschen möchten?"}, "footer": {"cancel": "Stornieren", "confirm": "Bestätigen", "update": "Aktualisierung"}, "bridge": {"title": "<PERSON><PERSON><PERSON> Anbindung der Kanäle", "placeholder": "<PERSON>te geben Si<PERSON> {{name}}-Serviceadress<PERSON> ein."}, "copy": {"title": "<PERSON><PERSON>"}}, "dropdown": {"onlineChat": "Online-Gespräch", "disableToken": "Token deaktivieren", "enableToken": "Token aktivieren", "editToken": "Bearbeitungs-Token", "requestExample": "Beispielanfrage", "tokenLog": "Token-Protokoll", "shareToken": "Freigabetoken", "quickIntegration": "Ein-Klick-Anbindung"}, "error": {"fetchModelsFailed": "Modellabfrage fehlgeschlagen: {{message}}", "batchDeleteFailed": "Massenlöschung fehlgeschlagen: {{message}}", "deleteTokenFailed": "Tokenlöschung fehlgeschlagen: {{message}}", "refreshTokenFailed": "Fehler beim Aktualisieren des Tokens: {{message}}", "enableTokenFailed": "Token-Aktivierung fehlgeschlagen: {{message}}", "disableTokenFailed": "Token-Deaktivierung fehlgeschlagen: {{message}}", "fetchDataFailed": "Datenab<PERSON>f fehlgeschlagen: {{message}}"}, "success": {"batchDelete": "Erfolgreich {{count}} Token gelöscht.", "shareTextCopied": "Der geteilte Text wurde in die Zwischenablage kopiert.", "tokenCopied": "Das Token wurde in die Zwischenablage kopiert.", "deleteToken": "Token erfolgreich gelöscht.", "refreshToken": "Token erfolgreich aktualisiert.", "enableToken": "Token erfolgreich aktiviert", "disableToken": "Token erfolgreich deaktiviert", "export": "Aktueller Seiten-Token erfolgreich exportiert."}, "warning": {"copyFailed": "<PERSON><PERSON><PERSON> feh<PERSON>, bitte manuell kopieren.", "invalidServerAddress": "<PERSON>te geben Sie die richtige Serveradresse ein."}, "info": {"openingBridgePage": "Die Schnittstellenseite wird geöffnet, das Token wurde für Sie kopiert."}, "export": {"name": "Name", "key": "Schlüssel", "billingType": "Abrechnungsart", "status": "Zustand", "models": "Verfügbare Modelle", "usedQuota": "Verbrauchsgrenze", "remainQuota": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createdTime": "Erstellungszeitpunkt", "expiredTime": "Ablaufdatum", "unlimited": "unbegrenzt", "neverExpire": "<PERSON><PERSON>"}, "billingType": {"1": "Nach Verbrauch abrechnen", "2": "Nach Verbrauch abrechnen", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "Mengenpriorität", "5": "Nach zweiter Priorität"}, "bridge": {"quickIntegration": "Ein-Klick-Anbindung"}}, "editTokenModal": {"editTitle": "Bearbeitungs-Token", "createTitle": "Token erstellen", "defaultTokenName": "{{username}}s Token {{date}}", "tokenName": "Token-Name", "unlimitedQuota": "unbegrenztes Limit", "remainingQuota": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "authorizedQuota": "Genehmigtes Limit", "quotaLimitNote": "Das maximale verfügbare Kontingent für Token ist auf den Kontostand beschränkt.", "quickOptions": "Schnelloptionen", "neverExpire": "<PERSON><PERSON>", "expiryTime": "Ablaufdatum", "billingMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectGroup": "Gruppenauswahl", "switchGroup": "Gruppenauswahl", "switchGroupTooltip": "Wählen Sie die Gruppe aus, zu der das Token gehört. Verschiedene Gruppen haben unterschiedliche Preise und Funktionsberechtigungen. Wenn Sie keine Auswahl treffen, wird standardmäßig die Gruppe des aktuellen Benutzers verwendet.", "switchGroupHint": "Die Auswahl der Gruppierung beeinflusst den Abrechnungsfaktor der Token und die verfügbaren Modelle. Bitte wählen Sie basierend auf Ihren tatsächlichen Anforderungen.", "importantFeature": "<PERSON><PERSON><PERSON><PERSON>", "tokenRemark": "Token-Anmerkung", "discordProxy": "Discord-Proxy", "enableAdvancedOptions": "Erweiterte Optionen aktivieren", "generationAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> Men<PERSON>", "availableModels": "Verfügbare Modelle", "selectModels": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>en/<PERSON><PERSON>uf<PERSON><PERSON> verfüg<PERSON>er <PERSON>le, <PERSON><PERSON> lassen bedeutet unbegrenzt.", "activateOnFirstUse": "Erstmalige Aktivierung", "activateOnFirstUseTooltip": "Nach der ersten Nutzung aktivierte Gültigkeitsdauer. Wenn diese Option aktiviert ist und die Aktivierung bei der ersten Nutzung erfolgt, wird die oben konfigurierte Token-Gültigkeitsdauer überschrieben.", "activationValidPeriod": "Aktivierungszeitraum", "activationValidPeriodTooltip": "Aktivierungszeitraum des Tokens nach der ersten Verwendung (Einheit: Tage)", "ipWhitelist": "IP-Whitelist", "ipWhitelistPlaceholder": "IP-Adressen (Bereiche), unterstützt IPV4 und IPV6, mehrere durch Kommas getrennt.", "rateLimiter": "Strombegrenzer", "rateLimitPeriod": "Drosselungszyklus", "rateLimitPeriodTooltip": "Drosselungszeitraum (Einheit: Sekunden)", "rateLimitCount": "Begrenzte Anzahl an Durchläufen", "rateLimitCountTooltip": "Verfügbare Anzahl der Versuche innerhalb des Drosselungszeitraums", "promptMessage": "Hinweisnachricht", "promptMessageTooltip": "Hinweisnachricht bei Überschreitung des Datenlimits", "promotionPosition": "Werbeplatzierung", "promotionPositionStart": "<PERSON><PERSON><PERSON>", "promotionPositionEnd": "<PERSON><PERSON>", "promotionPositionRandom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "promotionContent": "Werbeinhalte", "currentGroup": "Aktuelle Gruppe", "searchGroupPlaceholder": "Suche nach Gruppennamen, Beschreibungen oder Multiplikatoren...", "mjTranslateConfig": "MJ Übersetzungskonfiguration", "mjTranslateConfigTip": "Nur für Übersetzungskonfigurationen, die für Midjourney-Prompt-Wörter wirksam sind.", "mjTranslateBaseUrlPlaceholder": "Bitte geben Sie die Basis-URL des Übersetzungsdienstes ein.", "mjTranslateApiKeyPlaceholder": "Bitte geben Sie den API-Schlüssel für den Übersetzungsdienst ein.", "mjTranslateModelPlaceholder": "Bitte geben Sie den Namen des für den Übersetzungsdienst verwendeten Modells ein.", "mjTranslateBaseUrlRequired": "Beim Aktivieren der Übersetzung muss die Basis-URL angegeben werden.", "mjTranslateApiKeyRequired": "Beim Aktivieren der Übersetzung muss ein API-Schlüssel bereitgestellt werden.", "mjTranslateModelRequired": "Beim Aktivieren der Übersetzung muss der Modellname angegeben werden."}, "addTokenQuotaModal": {"title": "Token-Bestandsverwaltung {{username}}", "defaultReason": "Administrator-<PERSON><PERSON><PERSON>", "enterRechargeAmount": "Bitte geben Sie den Aufladebetrag ein.", "enterRemark": "<PERSON>te geben Si<PERSON> eine Bemerkungsnachricht ein.", "confirmOperation": "Bestätigung der Aktion", "confirmContent": "<PERSON>ätigen <PERSON>, dass {{username}} {{action}} {{amount}} Dollar {{updateExpiry}}?", "recharge": "Aufladen", "deduct": "Abzug", "andUpdateExpiry": "und aktualisieren Sie die Gültigkeitsdauer des Guthabens auf {{days}} Tage.", "alertMessage": "Die Eingabe einer negativen Zahl kann das Benutzerkonto belasten.", "rechargeAmount": "Aufladebetrag", "operationReason": "Betriebsgrund", "finalBalance": "<PERSON><PERSON><PERSON><PERSON>"}, "billingType": {"1": "Nach Verbrauch abrechnen", "2": "Nach Verbrauch abrechnen", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "Mengenpriorität", "5": "Nach zweiter Priorität", "payAsYouGo": "Nach Verbrauch abrechnen", "payPerRequest": "Nach Verbrauch abrechnen", "hybrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "payAsYouGoPriority": "Mengenpriorität", "payPerRequestPriority": "Nach zweiter Priorität", "unknown": "Unbekannte Methode"}, "packagePlanAdmin": {"title": "<PERSON><PERSON>", "table": {"title": "Paketverwaltung", "toolBar": {"add": "Neues Paket erstellen", "delete": "<PERSON><PERSON> l<PERSON>"}, "action": {"edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "detail": "Details", "recovery": "Auflegen", "offline": "Aus dem Sortiment nehmen"}}, "modal": {"title": {"add": "Neues Paket erstellen", "edit": "Bearbeiten Sie das Paket"}, "field": {"name": "Paketname", "type": {"default": "Pakettyp", "type1": "Kontingentpaket", "type2": "<PERSON>et mit mehreren Buchungen", "type3": "Zeitpaket"}, "group": "Paketgruppen", "description": "Paketbeschreibung", "price": "Paketpreise", "valid_period": "G<PERSON>lt<PERSON>keitsdauer", "first_buy_discount": "Erstkäufer-Ra<PERSON><PERSON>", "rate_limit_num": "Beschränkung der Anzahl", "rate_limit_duration": "Einschränkungszeitraum", "inventory": "Paketbestand", "available_models": "Verfügbare Modelle", "quota": "Paketlimit", "times": "Paketanzahl"}, "footer": {"cancel": "Stornieren", "confirm": "Bestätigen", "update": "Aktualisierung"}}}, "login": {"title": "Anmelden", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "login": "Anmelden", "otherLoginMethods": "Andere Anmeldemethoden", "register": "Konto registrieren", "accountLogin": "Kontoanmeldung", "phoneLogin": "Handynummer-Login", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "usernameRequired": "Bitte geben Si<PERSON> Ihren Benutzernamen ein!", "passwordPlaceholder": "Passwort", "passwordRequired": "<PERSON>te geben Si<PERSON> das Passwort ein!", "passwordMaxLength": "Die Passwortlänge darf 20 Zeichen nicht überschreiten!", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "phoneRequired": "Bitte geben Si<PERSON> Ihre Handynummer ein!", "phoneFormatError": "Die Handynummer hat ein falsches Format!", "smsCodePlaceholder": "SMS-Bestätigungscode", "smsCodeCountdown": "Erneut abrufen in {{count}} Sekunden.", "getSmsCode": "Bestätigungs-<PERSON> an<PERSON>", "agreementText": "Ich stimme zu.", "privacyPolicy": "Datenschutzrichtlinie", "and": "und", "serviceAgreement": "Dienstleistungsvertrag", "alreadyLoggedIn": "Sie sind angemeldet.", "weakPasswordWarning": "Ihr Passwort ist zu ein<PERSON>ch, bitte ändern Si<PERSON> es umgehend!", "welcomeMessage": "Willkommen!", "captchaError": "Der Bestätigungscode ist falsch.", "credentialsError": "Benutzername oder Passwort falsch", "resetPassword": "Passwort zurücksetzen", "captchaExpired": "Der Bestätigungscode existiert nicht oder ist abgelaufen.", "loginFailed": "Anmeldung fehlgeschlagen: {{message}}", "captchaRequired": "Bitte geben Sie den Bestätigungscode ein!", "captchaPlaceholder": "Bestätigungscode", "smsSent": "Der SMS-Verifizierungscode wurde erfolgreich gesendet.", "smsSendFailed": "Die SMS-Bestätigungscode-Übertragung ist fehlgeschlagen.", "agreementWarning": "Bitte stimmen Sie zuerst der Datenschutzrichtlinie und den Nutzungsbedingungen zu.", "turnstileWarning": "Bitte versuchen Sie es später erneut, Turnstile überprüft die Benutzerumgebung!", "loginSuccess": "Anmeldung erfolgreich"}, "register": {"title": "Registrierung", "usernameRequired": "Bitte geben Si<PERSON> Ihren Benutzernamen ein!", "usernameNoAt": "Der Benutzername darf kein @-Symbol enthalten.", "usernameNoChinese": "Der Benutzername darf keine chinesischen Zeichen enthalten.", "usernameLength": "Die Länge des Benutzernamens sollte zwischen 4 und 12 Zeichen liegen.", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "passwordRequired": "<PERSON>te geben Si<PERSON> das Passwort ein!", "passwordLength": "Die Passwortlänge sollte zwischen 8 und 20 Zeichen liegen.", "passwordPlaceholder": "Passwort", "confirmPasswordRequired": "Bitte bestätigen Sie das Passwort!", "passwordMismatch": "Die beiden eingegebenen Passwörter stimmen nicht überein!", "confirmPasswordPlaceholder": "Passwort bestätigen", "emailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein!", "emailRequired": "Bitte geben Sie Ihre E-Mail-Adresse ein!", "emailPlaceholder": "E-Mail-Adresse", "emailCodeRequired": "Bitte geben Sie den E-Mail-Bestätigungscode ein!", "emailCodePlaceholder": "E-Mail-Bestätigungscode", "enterCaptcha": "Bitte geben Sie den Bestätigungscode ein.", "resendEmailCode": "Bitte in {{seconds}} Sekunden erneut senden.", "getEmailCode": "Bestätigungs-<PERSON> an<PERSON>", "phoneRequired": "Bitte geben Si<PERSON> Ihre Handynummer ein!", "phoneInvalid": "Die Telefonnummer ist im falschen Format!", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "smsCodeRequired": "<PERSON>te geben Sie den SMS-Bestätigungscode ein!", "smsCodePlaceholder": "SMS-Bestätigungscode", "resendSmsCode": "Nach {{seconds}} Sekunden erneut senden.", "getSmsCode": "Bestätigungs-<PERSON> an<PERSON>", "captchaRequired": "Bitte geben Sie den Bestätigungscode ein!", "captchaPlaceholder": "Bestätigungscode", "inviteCodePlaceholder": "Einladungs-Code (optional)", "submit": "Registrierung", "successMessage": "Registrierung erfolgreich", "failMessage": "Registrierung fehlgeschlagen", "emailCodeSent": "Der Bestätigungscode wurde an die E-Mail-Adresse gesendet.", "smsCodeSent": "Der SMS-Bestätigungscode wurde gesendet.", "confirm": "Bestätigen", "emailVerifyTitle": "E-Mail-Bestätigung", "smsVerifyTitle": "SMS-Verifizierung", "registerVerifyTitle": "Registrierungsbestätigung"}, "profile": {"timezone": "Zeitzone", "phoneNumber": "<PERSON><PERSON><PERSON><PERSON>", "emailAddress": "E-Mail-Adresse", "wechatAccount": "WeChat-Konto", "telegramAccount": "Telegram-Konto", "bindTelegram": "Telegram binden", "balanceValidPeriod": "Gültigkeitsdauer des Guthabens", "lastLoginIP": "Letzte Anmelde-IP", "lastLoginTime": "Letzte Anmeldezeit", "inviteCode": "Einladungscode", "inviteLink": "Einladungslink", "generate": "Generierung", "pendingEarnings": "Warte auf Erträge", "transfer": "Übertragung", "totalEarnings": "Gesamtertrag", "accountBalance": "Kontostand", "totalConsumption": "<PERSON><PERSON><PERSON>", "callCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invitedUsers": "<PERSON><PERSON><PERSON> e<PERSON>n", "promotionInfo": "Description", "inviteDescription": "<PERSON><PERSON> Einladung, lebenslange Rückvergütung. Je mehr Einladungen, desto mehr Rückvergütung.", "userInfo": "Benutzerinformationen", "availableModels": "Verfügbare Modelle", "modelNameCopied": "<PERSON><PERSON><PERSON> wurde kop<PERSON>t.", "noAvailableModels": "Derzeit sind keine verfügbaren Modelle vorhanden.", "accountOptions": "Kontoeinstellungen", "changePassword": "Passwort ändern", "systemToken": "Systemtoken", "unsubscribe": "Abmelden", "educationCertification": "Bildungszertifizierung", "timezoneUpdateSuccess": "Zeitzonenaktualisierung erfolgreich", "inviteLinkCopied": "Einladungslink wurde kopiert.", "inviteLinkCopyFailed": "Einladungslink konnte nicht kopiert werden.", "inviteLinkGenerationFailed": "Einladungslink-Generierung fehlgeschlagen.", "allModelsCopied": "Alle Modelle wurden in die Zwischenablage kopiert.", "copyAllModels": "Alle Modelle kopieren", "totalModels": "Verfügbar<PERSON>", "expired": "Abgelaufen", "validPeriod": "G<PERSON>lt<PERSON>keitsdauer", "longTermValid": "Langfristig gültig", "failedToLoadModels": "Fehler beim Laden der Modellliste", "accessTokens": "Text", "accessTokensManagement": "访问令牌verwaltung", "accessTokenDescription": "Text", "tokenNameLabel": "Title", "tokenNamePlaceholder": "Title", "presetPermissions": "预设权限", "detailPermissions": "Text", "validityPeriod": "Text", "validityPeriodExtra": "0表示läuft nie ab", "remarkLabel": "Label", "remarkPlaceholder": "Please enter...", "createNewToken": "neuen token erstellen", "tokenCreatedSuccess": "zugriffstoken erfolgreich erstellt", "tokenSavePrompt": "Text", "copyToken": "复制令牌", "readPermission": "Text", "writePermission": "写入权限", "deletePermission": "Text", "tokenManagement": "令牌管理", "channelManagement": "Kanalverwaltung", "logView": "Text", "statisticsView": "统计信息", "userManagement": "User", "quotaManagement": "额度verwaltung", "readOnlyPermission": "Text", "writeOnlyPermission": "只写权限", "readWritePermission": "Text", "standardPermission": "标准权限", "fullPermission": "Text", "selectPermission": "Option", "tokenStatus": "状态", "tokenEnabled": "aktivieren", "tokenDisabled": "Text", "enableToken": "aktivieren", "disableToken": "Text", "deleteToken": "löschen", "deleteTokenConfirm": "Text", "disableTokenConfirm": "Action", "enableTokenConfirm": "Action", "tokenExpiryNever": "läuft nie ab", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Text", "noPermission": "Text"}, "topup": {"onlineRecharge": "Online-Aufladung", "cardRedemption": "Einlösung des Codes", "accountBalance": "Kontostand", "rechargeReminder": "Aufladeerinnerung", "reminder1": "1. <PERSON> G<PERSON>abe<PERSON> kann für Modellaufrufe, Paketkäufe usw. verwendet werden.", "reminder2": "2. <PERSON><PERSON> das Guthaben nach der Zahlung nicht gutgeschrieben wurde, wenden <PERSON> sich bitte an den Kundenservice.", "reminder3": "3. <PERSON> kann nicht abgehoben werden, kann jedoch innerhalb derselben Benutzergruppe überwiesen werden.", "reminder4WithTransfer": "4. Nach erfolgreicher Aufladung wird die Gültigkeitsdauer des Kontostands zurückgesetzt auf", "reminder4WithoutTransfer": "Nach erfolgreicher Aufladung wird die Gültigkeitsdauer des Kontostands zurückgesetzt auf", "days": "<PERSON><PERSON>", "paymentSuccess": "Zahlung erfolgreich", "paymentError": "Zahlungsfehler", "paymentAmount": "Zahlungsbetrag:", "purchaseAmount": "Kaufbetrag: $", "yuan": "Yuan", "or": "oder", "usd": "US-Dollar", "cny": "Yuan", "enterAmount": "Bitte geben Si<PERSON> den Aufladebetrag ein!", "amountPlaceholder": "<PERSON>te geben Sie den Betrag für die Aufladung ein, ab {{min}} US-Dollar.", "amountUpdateError": "Fehler beim Aktualisieren des Betrags.", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "Der Gutscheincode hat ein falsches Format.", "redeemSuccess": "{{amount}} erfolgreich umgetauscht!", "redeemError": "Fehler bei der Einlösung, bitte versuchen Sie es später erneut.", "enterCardKey": "Bitte geben Sie den Gutscheincode ein.", "cardKeyPlaceholder": "Bitte geben Sie den Gutscheincode ein.", "buyCardKey": "<PERSON><PERSON>", "redeem": "Sofortige Verrechnung", "record": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amount": "Aufladebetrag", "payment": "Zahlungsbetrag", "paymentMethod": "Zahlungsmethode", "orderNo": "Bestellnummer", "status": "Zustand", "createTime": "Erstellungszeitpunkt", "statusSuccess": "Erfolg", "statusPending": "In Bearbeitung", "statusFailed": "<PERSON><PERSON>"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "Administrator", "paymentMethodRedeem": "Einlösecode", "alipayF2F": "Alipay <PERSON>-<PERSON>-Zahlung"}, "pricing": {"fetchErrorMessage": "Es ist ein Fehler beim Abrufen der Preisinformationen aufgetreten, bitte kontaktieren Sie den Administrator.", "availableModelErrorMessage": "Ein Fehler ist aufgetreten, während das verfügbare Modell abgerufen wurde. Bitte kontaktieren Sie den Administrator.", "modelName": "<PERSON><PERSON><PERSON>", "billingType": "Abrechnungsart", "price": "Pre<PERSON>", "ratio": "Vergrößerungsfaktor", "promptPriceSame": "Hinweispreis: Entspricht dem ursprünglichen Tarif.", "completionPriceSame": "Vollständiger Preis: Entspricht dem ursprünglichen Tarif.", "promptPrice": "Preisvorschlag: $ {{price}} / 1M Tokens", "completionPrice": "Vervollständigungspreis: $ {{price}} / 1M Tokens", "promptRatioSame": "Hinweisverhältnis: Entspricht dem ursprünglichen Verhältnis.", "completionRatioSame": "Ergänzungsfaktor: Entspricht dem ursprünglichen Faktor.", "promptRatio": "Hinweisverhältnis: {{ratio}}", "completionRatio": "Ergänzungsfaktor: {{ratio}}", "payAsYouGo": "Nutzungsabhängige Bezahlung - Chat", "fixedPrice": "$ {{price}} / Mal", "payPerRequest": "Pay-per-Use - Chat", "dynamicPrice": "$ {{price}} / Mal", "payPerRequestAPI": "Pay-per-Use - API", "loadingTip": "Preisinformation wird abgerufen...", "userGroupRatio": "Ihr Benutzergruppenkurs beträgt: {{ratio}}", "readFailed": "Lesevorgang fehlgeschlagen.", "billingFormula": "Abrechnungsgebühren = Konversionsrate × Gruppierungsfaktor × Modellfaktor × (Anzahl der Prompt-Tokens + <PERSON>zahl der Completion-Tokens × Completion-Faktor) / 500000 (Einheit: US-Dollar)", "billingFormula1": "Umrechnungsrate = (neuer Aufladefaktor / ursprünglicher Aufladefaktor) × (neuer Gruppierungsfaktor / ursprünglicher Gruppierungsfaktor)", "generatedBy": "Diese Seite wurde automatisch von {{systemName}} erstellt.", "modalTitle": "Preisinformationen", "perMillionTokens": "/1M Token", "close": "Schließen", "searchPlaceholder": "Suchmodellname", "viewGroups": "Gruppierung anzeigen", "copiedToClipboard": "In die Zwischenablage kopiert.", "copyFailed": "<PERSON><PERSON>ren fehlgeschlagen.", "groupName": "Gruppenname", "availableGroups": "Verfügbare Gruppen für das Modell {{model}}", "noGroupsAvailable": "<PERSON><PERSON> verfügbaren Gruppen.", "modelGroupsErrorMessage": "Fehler beim Abrufen der Modellgruppendaten.", "currentGroup": "Aktuelle Gruppe", "copyModelName": "Modellnamen kopieren", "groupRatio": "Gruppierungsquote", "closeModal": "Schließen", "groupsForModel": "Modell verfügbar in Gruppen", "actions": "Operation", "filterByGroup": "Nach Gruppen filtern", "groupSwitched": "<PERSON>chsel zu Gruppe: {{group}}", "showAdjustedPrice": "Preise nach Gruppenanpassung anzeigen (Aktueller Multiplikator: {{ratio}})"}, "guestQuery": {"usageTime": "Verwendungszeit", "modelName": "<PERSON><PERSON><PERSON>", "promptTooltip": "Eingabe verbraucht Tokens", "completionTooltip": "Ausgabe von Tokenverbrauch", "quotaConsumed": "Verbrauchsgrenze", "pasteConfirm": "Es wurde ein gültiges Token in der Zwischenablage erkannt. Möchten Sie es einfügen?", "queryFailed": "Abfrage fehlgeschlagen", "tokenExpired": "Das Token ist abgelaufen.", "tokenExhausted": "Das Kontingent dieses Tokens ist erschöpft.", "invalidToken": "Bitte geben Sie das richtige Token ein.", "focusRequired": "<PERSON>te stellen Si<PERSON> sicher, dass die Seite im Fokus ist.", "queryFirst": "Bitte zuerst nachschlagen.", "tokenInfoText": "Gesamtbetrag der Token: {{totalQuota}}  \nTokenverbrauch: {{usedQuota}}  \nTokenbilanz: {{remainQuota}}  \nAn<PERSON><PERSON> der Aufrufe: {{callCount}}  \nGültig bis: {{validUntil}}", "unlimited": "unbegrenzt", "neverExpire": "<PERSON><PERSON>", "infoCopied": "Token-Information wurde in die Zwischenablage kopiert.", "copyFailed": "<PERSON><PERSON>ren fehlgeschlagen.", "noDataToExport": "<PERSON>s gibt keine Daten zum Exportieren.", "prompt": "<PERSON><PERSON><PERSON><PERSON>", "completion": "Vervollständigen", "disabled": "Besucherabfrage nicht aktiviert", "tokenQuery": "Token-Abfrage", "tokenPlaceholder": "Bitte geben Sie das zu überprüfende Token (sk-xxx) ein.", "tokenInfo": "Token-Information", "copyInfo": "Information kopieren", "totalQuota": "Token-Gesamtbetrag", "usedQuota": "Tokenverbrauch", "remainQuota": "Token-Balance", "callCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validUntil": "Gültig bis", "currentRPM": "Aktuelle RPM", "currentTPM": "Aktuelles TPM", "callLogs": "Protokoll aufrufen", "exportLogs": "Protokolldatei exportieren"}, "agencyProfile": {"fetchError": "Fehler beim Abrufen der Informationen des Agenten.", "fetchCommissionError": "Fehler beim Abrufen der Provisionsliste.", "systemPreset": "Systemvorgaben", "lowerRatioWarning": "Der Satz ist niedriger als die vom System festgelegte Rate.", "lowerRatioMessage": "Die folgenden Tarife liegen unter dem im System festgelegten Wert, bitte ändern Si<PERSON> dies umgehend:", "cancelRatioEdit": "Stornierung der Änderungsgebühr", "updateSuccess": "Aktualisierung erfolgreich", "updateError": "Aktualisierung der Agenturinformationen fehlgeschlagen:", "updateFailed": "Aktualisierung fehlgeschlagen:", "customPriceUpdateSuccess": "Die benutzerdefinierte Preisaktualisierung war erfolgreich.", "customPriceUpdateError": "Die Aktualisierung des benutzerdefinierten Preises ist fehlgeschlagen:", "time": "Zeit", "type": "<PERSON><PERSON>", "agencyCommission": "Provision des Handelsvertreters", "unknownType": "Unbekannter Typ", "amount": "Betrag", "balance": "<PERSON><PERSON><PERSON><PERSON>", "description": "Beschreibung", "group": "Gruppierung", "customRate": "Benutzerdefinierte Tarife", "systemDefaultRate": "Systemstandardgebühr", "action": "Operation", "save": "Speichern", "cancel": "Stornieren", "edit": "<PERSON><PERSON><PERSON>", "agencyConsole": "Agenten-Dashboard", "agencyInfo": "Agenteninformationen", "editInfo": "<PERSON><PERSON><PERSON> von <PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "agencyLevel": "Händlerstufe", "level1": "Stufe 1", "subordinateUsers": "Untergeordneter Benutzer", "totalSales": "Gesamtumsatz", "commissionIncome": "Provisionseinkommen", "cumulativeEarnings": "Kumulierte Erträge", "agencyFunctions": "Vertretungsfunktion", "hideSubordinateUsers": "Untergeordnete Benutzer ausblenden", "viewSubordinateUsers": "Untergeordnete Benutzer anzeigen", "hideCommissionDetails": "Versteckte Provisionsdetails", "viewCommissionDetails": "Provisionsdetails anzeigen", "hideCustomPrice": "Versteckte benutzerdefinierte Preise", "setCustomPrice": "Benutzerdefinierte Preise festlegen", "subordinateUsersList": "Unterbenutzerliste", "commissionRecords": "Provisionen Aufzeichnungen", "customPriceSettings": "Benutzerdefinierte Preissetzung", "saveChanges": "Änderungen speichern", "editAgencyInfo": "Agenturinformationen bearbeiten", "logo": "Logo", "setAgencyLogo": "Agenten-Logo e<PERSON>tellen", "customHomepage": "Benutzerdefinierte Startseite", "aboutContent": "Über den Inhalt", "newHomepageConfig": "Neue Startseitenkonfiguration", "customAnnouncement": "Benutzerdefinierte Ankündigung", "customRechargeGroupRateJson": "Benutzerdefinierte Aufladegruppen-Gebühren-JSON", "customRechargeRate": "Benutzerdefinierte Aufladegebühren", "viewSystemDefaultRate": "Überprüfen Sie die Standardgebühren des Systems.", "rateComparison": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comparisonResult": "Vergleichsergebnis", "higherThanSystem": "Über dem System", "lowerThanSystem": "Unter dem System", "equalToSystem": "entspricht dem System", "unknown": "Unbekannt", "notAnAgentYet": "<PERSON><PERSON> sind noch kein Agent.", "becomeAnAgent": "Werde Agent.", "startYourOnlineBusiness": "🌟 Starten Sie Ihr Online-Geschäft ganz einfach", "becomeOurAgent": "Werden Sie unser Agent und genießen Sie ein stressfreies Unternehmertum:", "noInventory": "💼 <PERSON><PERSON>, kein Druck auf den Kapitalumschlag.", "instantCommission": "💰 Sofortige Verkaufsbeteiligung, um proportional hohe Rückflüsse zu erzielen.", "easyManagement": "🖥️ <PERSON>ine Webseitentechnologie erforderlich, verwalten Sie Ihren Online-Shop ganz einfach.", "flexibleDomainChoice": "🌐 Flexible Domain-Auswahl", "youCan": "<PERSON>e können:", "useOwnDomain": "🏠 Verwenden Sie Ihre eigene Domain", "orUseOurSubdomain": "🎁 Oder wir bieten Ihnen eine exklusive Subdomain an.", "convenientStart": "🔥 <PERSON><PERSON>, ob <PERSON><PERSON> erfahren sind oder gerade erst anfangen, wir bieten Ihnen einen einfachen Einstieg.", "actNow": "🚀 Sofort handeln!", "contactAdmin": "Kontaktieren Sie den Website-Administrator, um Ihre Reise als Agent zu beginnen! 📞", "applyNow": "Jetzt bewerben", "contactCooperation": "Beratungskooperation", "understandPolicy": "Verstehen Sie die Richtlinien und Kooperationsdetails der Agenten.", "provideDomain": "Domain bereitstellen", "configDomain": "Bitte geben Sie Ihre Domain an, wir helfen Ihnen bei der Konfiguration.", "promoteAndEarn": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "startPromoting": "Beginnen Sie mit der Promotion Ihrer Agentur-Website und verdienen Sie Provisionen.", "noDeploymentWorries": "Keine Sorge um komplexe Cloud-Service-Bereitstellungen, Zahlungsabwicklungen oder Lagerbestandsprobleme.", "easySetup": "Geben Sie einfach den Domainnamen an, konfigurieren Sie ihn gemäß der Anleitung und starten Sie mühelos Ihr Unternehmens-API-Proxy-Geschäft.", "customizeContent": "Sie können Preise, Standortinformationen, SEO, Logos und andere Inhalte anpassen.", "commissionBenefits": "Als Agent erhalten Sie eine Umsatzbeteiligung aus den Nutzeraufladungen. Das System zieht automatisch die Kosten ab, der verbleibende Betrag kann jederzeit abgehoben werden.", "joinNowBenefit": "Tritt jetzt bei uns ein und genieße gemeinsam die Vorteile des AI-Zeitalters!", "groups": {"student": "<PERSON><PERSON>", "studentDesc": "Mit ausreichend Zeit hoffe ich, durch Werbeaktionen leicht zusätzliches Einkommen zu generieren, um einen Teil der Lebenshaltungskosten und Unterhaltungsausgaben zu decken.", "partTime": "Nebenjob oder Nebentätigkeit", "partTimeDesc": "Es ist keine große Zeitinvestition erforderlich, ein<PERSON>ch in der Freizeit ein wenig Werbung machen und man kann leicht zusätzliches Einkommen verdienen.", "mediaWorker": "Selbstmedienpraktiker", "mediaWorkerDesc": "Mit einer gewissen Fangemeinde können Si<PERSON> ganz ein<PERSON>ch zusätzliches Einkommen erzielen, indem Sie am Ende eines Artikels oder Beitrags einen Link hinzufügen.", "freelancer": "Freelancer", "freelancerDesc": "<PERSON>t viel flexibler Zeit kann man durch die Teilnahme an Verkaufsaktivitäten leicht zusätzliches Einkommen generieren."}, "stories": {"story1": {"name": "<PERSON>", "role": "<PERSON><PERSON>"}, "story2": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "story3": {"name": "<PERSON>", "role": "E-Commerce"}, "story4": {"name": "<PERSON>", "role": "Selbstmedien"}, "story5": {"name": "<PERSON>", "role": "Forschungspraktiker"}, "story6": {"name": "<PERSON><PERSON>", "role": "Xiaohongshu-Blogger"}, "story7": {"name": "<PERSON><PERSON>", "role": "Selbstmedien"}, "story8": {"name": "<PERSON>", "role": "IT-Branche"}}, "earnedAmount": "Bereits verdient {{amount}}", "applyForAgentNow": "Jetzt Agent werden!", "businessLinesConnected": "Über 40 Geschäftsbereiche sind bereits integriert.", "agencyJoin": "Agenturfranchise", "becomeExclusiveAgent": "Werden Sie unser exklusiver Agent.", "startBusinessJourney": "Starten Sie Ihre Geschäftreise ganz entspannt~", "welcomeToAgencyPage": "Willkommen auf unserer Agenturseite!", "earningsTitle": "Über hundert Personen haben bereits über 3000 Yuan verdient.", "becomeAgentSteps": "Die Schritte zur Becoming eines Vertreters.", "agencyRules": "Vertretungsregeln", "suitableGroups": "Geeignete Personengruppe", "agencyImages": {"becomeAgent": "Werde Agent.", "agencyBusiness": "Vertretungsgeschäft"}, "rules": {"howToEstablishRelation": "Wie kann ein Benutzer eine Proxy-Beziehung zu mir aufbauen?", "howToEstablishRelationAnswer": "Registriere dich auf deiner Agentur-Website, um dein Benutzer zu sein.", "canSetPrice": "Kann ich den Verkaufspreis festlegen?", "canSetPriceAnswer": "Ja! Aber dein Verkaufspreis muss mindestens 10% über dem Einkaufspreis liegen.", "commissionShare": "Wie viel Provision kann ich erhalten?", "commissionShareAnswer": {"assumption": "Angenommen: Dein Einkaufspreis beträgt $1=1 Yuan, dein Verkaufspreis beträgt $1=2 Yuan, und deine Provisionsrate beträgt 90%.", "example": "Der Benutzer kauft für 10 $ auf deiner Seite und gibt 20 Yuan aus.", "calculation": "<PERSON> kannst erhalten: (2-1)*10*0,9 = 9 Yuan.", "explanation": "Interpretation: (Verkaufspreis - Einkaufspreis) * Verkaufsmenge * Provisionssatz"}}}, "error": {"title": "<PERSON><PERSON>", "content": "Ein Fehler ist aufgetreten."}, "loading": {"title": "Wird geladen...", "content": "Wird geladen..."}, "notfound": {"title": "404", "content": "Seite nicht gefunden"}, "servererror": {"title": "500", "content": "<PERSON><PERSON><PERSON>"}, "unauthorized": {"title": "401", "content": "Nicht autorisiert"}, "forbidden": {"title": "403", "content": "Zugang verboten"}, "networkerror": {"title": "Netzwerkfehler", "content": "Netzwerkfehler"}, "timeout": {"title": "Überziehung", "content": "Zeitüberschreitung der Anfrage"}, "noresult": {"title": "<PERSON><PERSON>", "content": "<PERSON><PERSON>"}, "nopermission": {"title": "<PERSON><PERSON>", "content": "<PERSON><PERSON>"}, "channelBridge": {"title": "<PERSON><PERSON><PERSON> Anbindung der Kanäle", "channelPlatform": "Vertriebskanalplattform", "billingMethod": "Abrechnungsart", "channelName": "Kanalname", "remark": "Anmerkung", "availableGroups": "Verfügbare Gruppen", "availableModels": "Verfügbare Modelle", "channelKey": "Kanal-Schlüssel", "proxyAddress": "Schnittstelle Adresse", "cancel": "Stornieren", "submit": "Einreichen", "gpt35Models": "GPT-3.5-Modell", "gpt4Models": "GPT-4 Modell", "clear": "<PERSON><PERSON>", "customModelName": "Benutzerdefinierter Modellname", "add": "Hinzufügen", "moreConfigReminder": "Weitere Konfigurationen bitte nach dem Speichern des Kanals bearbeiten.", "quickIntegration": "Ein-Klick-Anbindung", "selectBillingMethod": "Bitte wählen Sie die Abrechnungsart.", "enterChannelName": "<PERSON>te geben Si<PERSON> den Kanalnamen ein.", "enterChannelRemark": "<PERSON>te geben Sie die Kanalnotiz ein.", "selectAvailableGroups": "Bitte wählen Sie die Gruppen aus, die diesen Kanal nutzen können.", "selectAvailableModels": "<PERSON><PERSON><PERSON><PERSON>/Suchen Sie das verfügbare Modell für diesen Kanal.", "enterChannelKey": "Bitte geben Sie den Kanal-Schlüssel ein.", "proxyAddressPlaceholder": "Diese Option ist optional und dient dazu, API-Aufrufe über einen Proxy-Server durchzuführen. Bitte geben Sie die Adresse des Proxy-Servers ein.", "includes16kModels": "Enthält ein 16k-Modell.", "excludes32kModels": "<PERSON>cht enthalten ist das 32k-Modell.", "cleared": "<PERSON><PERSON><PERSON><PERSON>.", "addCustomModel": "Benutzerdefiniertes Modell hinzufügen", "clipboardTokenDetected": "Es wurde ein gültiges Token in der Zwischenablage erkannt. Möchten Sie es einfügen?", "channelIntegrationSuccess": "Die Anbindung des Kanals war erfolgreich!", "channelIntegrationFailed": "Fehler bei der Schnittstellenanbindung:"}, "about": {"loading": "Aktuelle Inhalte abrufen...", "noContent": "Der Administrator hat den Inhalt der Über-Seite nicht festgelegt.", "loadFailed": "Inhalt konnte nicht geladen werden..."}, "onlineTopupRecord": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columns": {"id": "ID", "username": "<PERSON><PERSON><PERSON>", "amount": "Aufladebetrag", "money": "Zahlungsbetrag", "paymentMethod": "Zahlungsmethode", "tradeNo": "Bestellnummer", "status": "Zustand", "createTime": "Erstellungszeitpunkt"}, "status": {"success": "Erfolg", "pending": "In Bearbeitung", "failed": "<PERSON><PERSON>"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Beschreibende Informationen", "downstreamError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "originalError": "Ursprüng<PERSON><PERSON>", "requestParams": "Anforderungsparameter", "copy": "<PERSON><PERSON><PERSON>"}, "viewMode": {"switchTo": "Wechseln Sie zur {{mode}}-Perspektive.", "cost": "<PERSON><PERSON>", "usage": "Verbrauchsmengen"}, "agenciesTable": {"title": "Agenturverwaltung", "addAgency": "Neue Agenten", "columns": {"id": "ID", "userId": "Benutzer-ID", "name": "Name", "domain": "Domainname", "commissionRate": "Provisionensatz", "salesVolume": "Umsatz", "userCount": "<PERSON><PERSON><PERSON> der Benutzer", "commissionIncome": "Provisionseinkommen", "historicalCommission": "Kumulierte Erträge", "actions": "Operation"}, "confirm": {"deleteTitle": "Möchten Sie diesen Agenten wirklich löschen?", "updateName": "Aktualisiere den Namen des Agenten...", "updateSuccess": "Aktualisierung erfolgreich", "updateFailed": "Aktualisierung fehlgeschlagen.", "deleteSuccess": "Löschung erfolgreich!"}, "messages": {"getListFailed": "<PERSON><PERSON> beim Abrufen der Liste der Agenten: {{message}}", "deleteSuccess": "Löschung erfolgreich!", "loadingData": "Wird geladen..."}}, "units": {"times": "nächste", "percentage": "{{Wert}}%", "formatUsage": "{{name}}: {{value}} Mal ({{percent}}%)"}, "dailyUsage": {"total": "Gesamtbetrag", "totalCost": "Gesamtkosten", "tooltipTitle": {"cost": "Kostenlage", "usage": "Nutzungssituation"}, "yAxisName": {"cost": "<PERSON><PERSON> (USD)", "usage": "<PERSON><PERSON><PERSON><PERSON> (USD)"}}, "dailyUsageByModel": {"total": "Gesamtbetrag", "tooltipTotal": "Gesamt: $ {{value}}", "switchTo": "<PERSON><PERSON><PERSON><PERSON> zu", "cost": "<PERSON><PERSON>", "usage": "Verbrauchsmengen", "perspective": "Perspektive", "granularity": {"hour": "nach Stunden", "day": "nach Tag", "week": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "month": "monatlich"}}, "checkinModal": {"title": "Bitte führen Sie die Überprüfung durch.", "captchaPlaceholder": "Bestätigungscode", "confirm": "Bestätigen", "close": "Schließen"}, "balanceTransfer": {"title": "Überweisung zwischen Konten", "accountInfo": {"balance": "Kontostand", "transferFee": "Überweisungsgebühren", "groupNote": "Überweisungen sind nur zwischen denselben Benutzergruppen möglich."}, "form": {"receiverId": "Empfänger-ID", "receiverUsername": "Empfänger-Benutzername", "remark": "Bemerkungsinformationen", "amount": "Überweisungsbetrag", "expectedFee": "Voraussichtliche Abrechnung", "submit": "Überweisung initiieren"}, "result": {"success": "Überweisung erfolgreich", "continueTransfer": "<PERSON><PERSON> überwei<PERSON>", "viewRecord": "Aufzeichnungen anzeigen"}, "warning": {"disabled": "Der Administrator hat die Überweisungsfunktion nicht aktiviert, daher kann sie vorübergehend nicht verwendet werden."}, "placeholder": {"autoCalculate": "Der Betrag für die Überweisung wird automatisch berechnet."}}, "channelsTable": {"title": "Kanalmanagement", "columns": {"id": "ID", "name": "Name", "type": "<PERSON><PERSON>", "key": "Schlüssel", "base": "Schnittstellenadresse", "models": "<PERSON><PERSON>", "weight": "Gewichtung", "priority": "Priorität", "retryInterval": "Wiederholungsintervall", "responseTime": "Antwortzeit", "status": "Zustand", "quota": "<PERSON><PERSON><PERSON><PERSON>", "expireTime": "Ablaufdatum", "group": "Gruppierung", "billingType": "Abrechnungsart", "actions": "Operation", "fusing": "Sicherungsschmelze", "sort": "Priorität", "balance": "<PERSON><PERSON><PERSON><PERSON>", "balanceUpdatedTime": "Guthaben-Aktualisierungszeit", "testTime": "Testzeit", "rpm": "RPM", "createdTime": "Erstellungszeit", "disableReason": "Deaktivierungsgrund"}, "status": {"all": "alle", "normal": "normal", "enabled": "Normalzustand", "manualDisabled": "<PERSON><PERSON>", "waitingRetry": "<PERSON><PERSON> auf Neustart", "suspended": "Eingestellt", "specified": "Festgelegter Status", "allDisabled": "Deaktivieren", "specifiedDisabled": "Festgelegter Deaktivierungstyp", "partiallyDisabled": "Teil<PERSON><PERSON>"}, "placeholder": {"selectGroup": "<PERSON>te w<PERSON>hlen/suchen Sie eine Gruppe.", "selectStatus": "Wählen Sie den Kanalstatus aus.", "inputSelectModel": "Modellnamen eingeben/auswählen", "selectFusingStatus": "Wählen Sie den automatischen Auslösestatus."}, "quota": {"usageAmount": "Verbrauch: {amount}", "remainingAmount": "Verbleibend: {amount}", "customTotalAmount": "Benutzerdefinierter Gesamtbetrag: {amount}", "updateNotSupported": "Aktualisierung des Guthabens wird vorübergehend nicht unterstützt, bitte verwenden Sie das benutzerdefinierte Guthaben.", "details": "Details", "sufficient": "ausreichend"}, "actions": {"edit": "<PERSON><PERSON><PERSON>", "copy": "Klonkanal", "delete": "Kanal löschen", "enable": "Aktivieren", "disable": "Deaktivieren", "test": "Test", "advancedTest": "Hochwertiger Test", "viewLog": "Kanalprotokoll", "viewAbility": "Fähigkeiten überprüfen", "cleanUsage": "<PERSON><PERSON> den verwendeten Speicher.", "updateBalance": "Kontostand aktualisieren", "copyKey": "Schlüssel kopieren", "topup": "Aufladen", "viewModels": "<PERSON><PERSON> anzeigen"}, "confirm": {"deleteTitle": "Löschbestätigung", "deleteContent": "<PERSON><PERSON> <PERSON>, dass Si<PERSON> den Kanal {{name}} (#{{id}}) löschen möchten?", "cleanUsageTitle": "Nutzungsmenge zurücksetzen bestätigen", "cleanUsageContent": "<PERSON>d <PERSON>, dass Sie den bereits verbrauchten Betrag für den Kanal {{name}} (#{{id}}) zurücksetzen möchten?", "testTitle": "Testbestätigung", "testContent": "<PERSON>d <PERSON>, dass Sie den Kanal für {{status}} testen möchten?", "testNote": "Hinweis: Diese Funktion muss zusammen mit [Konfiguration] -> [<PERSON><PERSON>] -> [Überwachungseinstellungen] -> [Kanal bei Fehler deaktivieren, Kanal bei Erfolg aktivieren] verwendet werden. Wenn die entsprechenden Einstellungen nicht aktiviert sind, werden die Kanäle nach Abschluss des Tests nicht automatisch deaktiviert oder aktiviert.", "deleteDisabledTitle": "Löschbestätigung", "deleteDisabledContent": "<PERSON><PERSON>cht<PERSON> Si<PERSON> wirklich alle {{type}}-<PERSON><PERSON><PERSON><PERSON> löschen?"}, "messages": {"operationSuccess": "Operation erfolgreich", "operationSuccessWithSort": "Operation erfo<PERSON><PERSON><PERSON><PERSON>, die Reihenfolge der Kanäle könnte sich geändert haben, es wird empfohlen, nach <PERSON> zu sortieren!", "operationFailed": "Operation fehlgeschlagen：{{message}}", "testRunning": "Kanal {{name}}(#{{id}}) Test läuft, bitte warten...", "testSuccess": "Kanal「{{name}}(#{{id}})」{{model}}Test erfolgreich, Antwortzeit {{time}} Se<PERSON>nden", "testFailed": "Kanal「{{name}}(#{{id}})」Test fehlgeschlagen：{{message}}", "testStarted": "Beginnen Sie mit dem Test des Kanals {{status}}. Bitte aktualisieren Sie später die Seite, um die Ergebnisse zu sehen. Die Anwendung der Testergebnisse hängt von Ihren Überwachungseinstellungen ab.", "testOperationFailed": "Test fehlgeschlagen", "deleteSuccess": "Erfolgreich {{count}} <PERSON><PERSON><PERSON><PERSON>.", "deleteFailed": "Löschung fehlgeschlagen: {{message}}", "modelPrefix": "Modell {{model}}", "channelInfo": "Kanalinformationen", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "Das Guthaben des Kanals {{name}} wurde erfolgreich aktualisiert.", "updateBalanceFailed": "Das Guthaben des Kanals {{name}} konnte nicht aktualisiert werden: {{message}}", "updateAllBalanceStarted": "Beginnen Sie mit der Aktualisierung des Kontostands aller normalen Kanäle.", "updateAllBalanceSuccess": "Alle Kanalbilanzen wurden erfolgreich aktualisiert.", "fetchGroupError": "Fehler beim Abrufen der Kanalgruppendaten: {{response}}", "fetchChannelError": "Fehler beim Abrufen der Kanaldaten: {{message}}", "selectChannelFirst": "Bitte wählen Sie zu<PERSON>t den Kanal aus, den Sie löschen möchten.", "deleteDisabledSuccess": "Alle {{type}} Ka<PERSON><PERSON><PERSON> wurden gelöscht, insgesamt {{count}} Stück.", "deleteOperationFailed": "Löschen fehlgeschlagen", "copySuccess": "<PERSON><PERSON>", "copyFailed": "<PERSON><PERSON><PERSON> fehlgeschlagen: {{message}}", "emptyKey": "Der Schlüssel ist leer.", "deleteConfirm": "<PERSON><PERSON>chten Sie den Kanal「{{name}}」wirklich löschen?", "batchDeleteConfirm": "<PERSON><PERSON><PERSON><PERSON> wirk<PERSON> {{count}} <PERSON><PERSON><PERSON><PERSON> löschen?", "testSuccessWithWarnings": "Message", "viewDetails": "Details anzeigen", "fetchChannelDetailError": "Message", "topupSuccess": "Aufladung erfolgreich", "topupFailed": "Aufladung fehlgeschlagen：{{message}}"}, "popover": {"channelInfo": "Kanalinformationen"}, "menu": {"deleteManualDisabled": "<PERSON>l deaktivierte Kanäle löschen", "deleteWaitingRetry": "Wartekanäle zum Neustart entfernen", "deleteSuspended": "Löschen Sie die pausierten Kanäle.", "testAll": "Alle Kanäle testen", "testNormal": "Testen Sie den normalen Kanal.", "testManualDisabled": "Test manuelles Deaktivierung des Kanals", "testWaitingRetry": "Testen Sie den Neustartkanal.", "testSuspended": "Testpause der Nutzungskanäle", "deleteDisabledAccount": "Deaktivierte Konten löschen", "deleteQuotaExceeded": "Kontingent-überschrittene Kanäle löschen", "deleteRateLimitExceeded": "löschenfrequenzlimitKanal", "deleteInvalidKey": "Text", "deleteConnectionError": "Error"}, "tooltip": {"testNote": "<PERSON><PERSON> ist er<PERSON><PERSON>, [Konfiguration] -> [<PERSON><PERSON>] -> [Überwachungseinstellungen] -> [Kanal bei Fehlern deaktivieren, Kanal bei Erfolg aktivieren] zu verwenden. <PERSON><PERSON> dies nicht aktiviert ist, wird der Kanal nach Abschluss des Geschwindigkeitstests nicht automatisch deaktiviert oder aktiviert."}, "disableReasons": {"account_deactivated": "konto de<PERSON>", "quota_exceeded": "Text", "rate_limit_exceeded": "frequenzlimit", "invalid_key": "Text", "connection_error": "连接fehler"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "Limit", "times": "<PERSON><PERSON><PERSON>"}, "serverLogViewer": {"title": "Server-Log-Viewer", "connecting": "Verbindung zum Server wird hergestellt...", "downloadSelect": "<PERSON>ählen Sie die Protokolldatei zum Herunterladen aus.", "nginxConfig": "Nginx WebSocket Konfigurationsanleitung", "directAccess": "Wenn Sie über eine Domain zugreifen und keine WebSocket-Unterstützung konfiguriert ist, funktioniert der Protokollbetrachter nicht. In diesem Fall können Sie direkt über die Server-IP und den Port zugreifen (z. B.: http://your-ip:9527).", "domainAccess": "Um über die Domain zuzugreifen, müssen Sie die folgende Konfiguration in der Nginx-Konfiguration hinzufügen, um WebSocket zu unterstützen:", "buttons": {"pause": "Pause", "resume": "Fortfahren", "clear": "<PERSON><PERSON>"}, "errors": {"fetchFailed": "Fehler beim Abrufen der Protokolldateiliste.", "downloadFailed": "Herunterladen der Protokolldatei fehlgeschlagen.", "wsError": "WebSocket-Verbindungsfehler"}}, "channelScore": {"score": "Punkte", "successRate": "Erfolgsquote", "avgResponseTime": "Durchschnittliche Antwortzeit", "title": "Kanalbewertung", "hourlyTitle": "Kanalstundenpunktzahl", "dailyTitle": "Kanal tägliche Punktzahl", "weeklyTitle": "Kanal-W<PERSON>enpunktzahl", "monthlyTitle": "Kanalmonatspunktzahl", "allTimeTitle": "Gesamtbewertung des Kanals", "infoTooltip": "Der Kanal-Score ist eine Gesamtnote, die auf der Erfolgsquote und der Reaktionszeit basiert.", "tableView": "Tabellenansicht", "chartView": "Diagrammansicht", "refresh": "Aktualisieren", "selectModel": "<PERSON><PERSON><PERSON><PERSON>", "allModels": "Alle Modelle", "sortByScore": "<PERSON><PERSON> sortieren", "sortBySuccessRate": "Nach Erfolgsquote sortieren", "sortByResponseTime": "Nach Antwortzeit sortieren", "noData": "<PERSON><PERSON> verfüg<PERSON>.", "totalItems": "Insgesamt {{total}} Elemente", "fetchError": "Fehler beim Abrufen der Kanalbewertungsdaten.", "aboutScoring": "Zur Berechnung der Punkte", "scoringExplanation": "Der Kanalwert ist eine Gesamtnote, die auf Erfolgsquote und Reaktionszeit basiert, mit einer Höchstpunktzahl von 1 Punkt.", "successRateWeight": "Erfolgsquote Gewichtung (70%)", "successRateExplanation": "Je höher die Erfolgsquote, desto höher die Punktzahl.", "responseTimeWeight": "Reaktionszeitgewichtung (30%)", "responseTimeExplanation": "Eine Reaktionszeit von unter 1000 ms erhält die volle Punktzahl, bei Überschreitung erfolgt eine proportionale Abwertung.", "columns": {"rank": "<PERSON><PERSON><PERSON><PERSON>", "channelId": "Kanal-ID", "channelName": "Kanalname", "model": "<PERSON><PERSON>", "totalRequests": "Gesamtanzahl der Anfragen", "successRequests": "Anzahl der erfolgreichen Anfragen", "failedRequests": "Anzahl der fehlgeschlagenen Anfragen", "successRate": "Erfolgsquote", "avgResponseTime": "Durchschnittliche Antwortzeit", "score": "Gesamtpunktzahl", "actions": "Operation"}, "actions": {"viewDetails": "Details anzeigen", "test": "Testkanal", "edit": "Bearbeitungskanal"}, "tooltips": {"excellent": "ausgezeichnet", "good": "Gut", "average": "allgemein", "poor": "schlecht", "veryPoor": "<PERSON><PERSON> schlecht"}, "scoringExplanation100": "Der Kanal-Score ist eine Gesamtnote, die auf Erfolgsquote und Reaktionszeit basiert, mit einer maximalen Punktzahl von 100."}, "menu": {"channelScores": "Kanalbewertung"}, "relay": {"dispatchOptions": "Planungsoptionen", "preciseWeightCalculation": "Gewicht genaue Berechnung", "preciseWeightCalculationTip": "Nach der Aktivierung wird ein präziserer Algorithmus zur Berechnung des Kanalgewichts verwendet, was möglicherweise die CPU-Auslastung erhöht.", "channelMetricsEnabled": "Aktivieren Sie die Kanalindikatorstatistik.", "channelMetricsEnabledTip": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, werden Erfolgsraten, Reaktionszeiten und andere Kennzahlen des Kanals gesammelt, um die Leistung des Kanals zu bewerten. <PERSON><PERSON> de<PERSON>ti<PERSON><PERSON>, werden diese Daten nicht gesammelt, was die Nutzung von Systemressourcen reduzieren kann.", "channelScoreRoutingEnabled": "Aktivieren Sie die routenbasierte Punktzahl basierend auf Kanälen.", "channelScoreRoutingEnabledTip": "Nach der Aktivierung passt das System automatisch die Priorität der Anfragenverteilung basierend auf der historischen Leistung der Kanäle an. Kanäle mit besserer Leistung erhalten eine höhere Wahrscheinlichkeit für die Anfragenverteilung.", "globalIgnoreBillingTypeFilteringEnabled": "Global ignorierenabrechnungsmethodeFilterung", "globalIgnoreBillingTypeFilteringEnabledTip": "Tip", "globalIgnoreFunctionCallFilteringEnabled": "Global ignorierenFunktionsaufrufFilterung", "globalIgnoreFunctionCallFilteringEnabledTip": "Text", "globalIgnoreImageSupportFilteringEnabled": "Global ignorierenBildunterstützungFilterung", "globalIgnoreImageSupportFilteringEnabledTip": "Text"}, "dynamicRouter": {"title": "Dynamisches Routenmanagement", "reloadRoutes": "Routen neu laden", "exportConfig": "Exportkonfiguration", "clearConfig": "Konfiguration zurücksetzen", "importantNotice": "<PERSON><PERSON><PERSON><PERSON>", "reloadLimitation": "1. Das erneute Laden der Routen kann nur die Konfiguration der vorhandenen Routen aktualisieren, nicht jedoch <PERSON>n hinzufügen oder löschen. Um die gesamte Routenstruktur vollständig neu zu laden, starten Sie die Anwendung neu.", "exportDescription": "2. Der Export der Konfiguration wird die Konfiguration aus der aktuellen Datenbank in die Datei router.json exportieren und dabei leere Werte und Nullwerte herausfiltern.", "clearDescription": "3. Das Leeren der Konfiguration entfernt alle dynamischen Routen-Konfigurationen aus der Datenbank, und nach dem Neustart der Anwendung werden sie erneut aus der router.json-Datei geladen.", "routeGroups": "Routing-Gruppe", "upstreamConfig": "Upstream-Konfiguration", "endpointConfig": "Endpunktkonfiguration", "editRouteGroup": "Bearbeiten der Routergruppe", "editUpstream": "Bearbeiten Sie die obere Konfiguration.", "editEndpoint": "Bearbeiten der Endpunktkonfiguration", "editJSON": "JSON bearbeiten", "confirmClear": "Bestätigen Sie das Leeren der Konfiguration.", "confirmClearMessage": "Dieser Vorgang wird alle dynamischen Routen-Konfigurationen in der Datenbank löschen. Nach dem nächsten Neustart der Anwendung werden die Konfigurationen aus der Datei neu geladen. Möchten Si<PERSON> fortfahren?", "configCleared": "Die dynamische Routen-Konfiguration wurde zurückgesetzt. Bitte starten Sie die Anwendung neu, um die Änderungen anzuwenden.", "configExported": "Die Konfiguration wurde erfolgreich in die Datei exportiert.", "configReloaded": "Die Routerkonfiguration wurde erfolgreich neu geladen."}, "notification": {"title": "benachrichtigungseinstellungen", "subscriptionEvents": "Message", "notificationMethods": "通知方式", "alertSettings": "Message", "emailConfig": "e-mail-konfiguration", "customEmails": "Message", "addEmail": "添加邮箱", "removeEmail": "löschen", "emailPlaceholder": "Message", "emailTooltip": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Description", "alertExplanationTitle": "预警说明", "alertExplanation": "Message", "selectEvents": "Message", "eventsDescription": "Message", "selectMethods": "Message", "methodsDescription": "Message", "description": "Description", "recommended": "Message", "important": "Message", "testRecommendation": "Message", "testNotification": "测试通知", "testMessage": "Message", "testSuccess": "测试通知发送成功", "testFailed": "Message", "saveSuccess": "Message", "saveFailed": "保存设置fehlgeschlagen", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Bitte geben Sie das Telegram Bot Token ein"}, "qywxbotConfig": "Enterprise WeChat Bot Konfiguration", "qywxbotGuide": "Message", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkConfig": "DingTalk Bot Konfiguration", "dingtalkGuide": "Message", "feishuConfig": "<PERSON><PERSON><PERSON> Ko<PERSON>figuration", "feishuGuide": "Message", "webhookConfig": "Webhook配置", "webhookGuide": "Message", "webhookUrl": "调用地址", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "Telegram Bot Konfiguration", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "额度即将过期", "security_alert": "sicherheitsalarm", "system_announcement": "Message", "promotional_activity": "werbeaktivitätsbenachrichtigung", "model_pricing_update": "Message", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Konfigurationsschritte:", "detailedDocumentation": "Detaillierte Dokumentation:", "qywxbotConfigurationGuide": "Enterprise WeChat Bot Konfigurationsanleitung", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "EingebenWebhook URLin die obige Konfiguration eingeben", "qywxbotDocumentationLink": "Message", "wxpusherConfiguration": "Wx<PERSON><PERSON><PERSON> Konfiguration", "wxpusherConfigurationGuide": "WxPusher Konfigurationsanleitung", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Bitte eingebenWxPusher APP Token", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Besuchen Sie die offizielle WxPusher-Website, um ein Konto zu registrieren", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "EingebenAPP Token和用户UIDin die obige Konfiguration eingeben", "wxpusherOfficialWebsite": "WxPusherOffizielle Website", "dingtalkConfigurationGuide": "DingTalk Bot Konfigurationsanleitung", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "Fe<PERSON>u Bot Konfigurationsanleitung", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "Message", "feishuStep4": "Message", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Telegram Bot Konfigurationsanleitung", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "复制获得的Bot Token", "telegramStep5": "Message", "telegramStep6": "Besuchen https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "EingebenWebhook URLin die obige Konfiguration eingeben", "dingtalkNoticeTitle": "Hinweise:", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "EingebenWebhook URLin die obige Konfiguration eingeben", "feishuMessageFormatsTitle": "Unterstützte Nachrichtenformate:", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Hinweise:", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "EingebenBot Token和Chat IDin die obige Konfiguration eingeben", "telegramNoticeTitle": "Hinweise:", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Title", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "Message", "telegramChannelPermission": "• 频道中机器人需要有发送消息的权限", "webhookCallUrl": "Aufrufadresse", "webhookConfigurationGuide": "Webhook-Konfigurationsleitfaden", "webhookDataFormatExample": "Datenformat-Beispiel：", "webhookConfigurationInstructions": "Konfigurationsanweisungen：", "webhookRequestMethod": "• Anfragemethode：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• Authentifizierungsmethode：<PERSON><PERSON>（optional, nach Eingabe wird Authorization: Bearer {token} im Anfrage-Header hinzugefügt）", "webhookTimeout": "• Timeout-Zeit：30 Sekunden", "webhookRetryMechanism": "• Wiederholungsmechanismus：Versucht nach Fehlschlag 2 Mal erneut", "webhookTip": "💡 Tipp：<PERSON><PERSON><PERSON>, dass Ihr Webhook-Endpunkt POST-Anfragen empfangen und 2xx-Statuscodes zurückgeben kann", "telegramStep3Detailed": "Konfigurieren Sie Bot-Name und Benutzername gemäß den Eingabeaufforderungen（Benutzername muss mit bot enden）", "telegramPersonalChatDetailed": "• Persönlicher Chat：Senden Sie eine Nachricht an den Bot, dann zugreifen", "telegramGroupChatDetailed": "• Gruppen-Chat：Fügen Sie den Bot zur Gruppe hinzu, nach dem Senden einer Nachricht auf denselben Link zugreifen", "telegramChannelDetailed": "• Kanal：<PERSON>ügen Sie den Bot als Administrator hinzu, Chat-ID beginnt normalerweise mit -100", "telegramQuickChatIdTitle": "Beispiel für schnelle Chat-ID-Beschaffung：", "telegramQuickStep1": "BOT_TOKEN ersetzen：https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "Greifen Sie im Browser auf den obigen Link zu", "telegramQuickStep3": "<PERSON><PERSON> in der JSON-Antwort：\"chat\":{\"id\":*********}"}, "legal": {"privacyPolicy": {"title": "Title", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "信息收集", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Description"}}, "informationUsage": {"title": "信息使用", "description": "Description", "items": ["提供和维护我们的服务", "Description", "Description", "发送重要的服务通知", "Description"]}, "informationSharing": {"title": "信息共享", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "数据安全", "description": "Description", "items": ["数据加密传输和存储", "Text", "Text", "员工隐私培训"]}, "dataRetention": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "您的权利", "description": "Description", "items": ["User", "删除您的账户和相关数据", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["维持用户会话", "Text", "Text"]}, "thirdPartyServices": {"title": "第三方服务", "description": "Description", "items": ["Text", "GitHub OAuth：用于用户身份验证", "Text"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "服务描述", "description": "Description", "items": ["Description", "Description", "使用统计和监控", "Description", "Description"]}, "userAccount": {"title": "用户账户", "description": "Description", "items": ["Text", "User", "User", "及时更新账户信息", "User"]}, "usageRules": {"title": "使用规则", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "Description", "items": ["尝试未经授权访问system", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "服务可用性", "description": "Description", "items": ["Text", "Text", "Text", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "高级功能可能需要付费", "Text", "Text"]}, "intellectualProperty": {"title": "Title", "description": "Description", "items": ["Text", "您获得有限的使用许可", "Text", "Text"]}, "privacyProtection": {"title": "隐私保护", "description": "Description", "items": ["Text", "采取合理措施保护数据安全", "Text"]}, "disclaimer": {"title": "免责声明", "description": "Description", "items": ["Text", "Text", "Text", "Text"]}, "serviceTermination": {"title": "服务终止", "description": "Description", "items": ["您违反这些条款", "Text", "Text", "法律要求"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["重大变更会提前通知", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "Text", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "Text"}}}