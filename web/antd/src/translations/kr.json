{"message": {"copyModelSuccess": "모델 이름이 클립보드에 복사되었습니다!", "copyFailed": "복사 실패, 수동으로 복사해 주세요.", "logoutSuccess": "로그아웃 성공", "loginSuccess": {"default": "로그인 성공", "welcomeBack": "환영합니다 돌아오신 것을!"}, "removeLocalStorage": {"confirm": "로컬 캐시를 지우시겠습니까?", "success": "로컬 캐시가 성공적으로 지워졌습니다."}, "loadData": {"error": "{{name}} 데이터 로드 실패"}, "noNotice": "현재 공지 내용이 없습니다.", "verification": {"turnstileChecking": "턴스타일이 사용자 환경을 확인하고 있습니다!", "pleaseWait": "나중에 다시 시도해 주세요."}, "clipboard": {"inviteCodeDetected": "초대 코드가 감지되어 자동으로 입력되었습니다!", "clickToCopy": "클릭하여 복사", "copySuccess": "복사 성공"}}, "common": {"yes": "네", "no": "아니요", "copyAll": "모두 복사하기", "all": "모두", "more": "더 많은", "unlimited": "무제한", "enabled": "열기", "disabled": "닫기", "save": "저장", "cancel": "취소", "create": "생성하다", "usd": "달러", "day": "{{count}} 일", "day_plural": "{{count}} 일", "days": "하늘", "seconds": "초", "times": "다음", "submit": "제출", "bind": "바인딩", "unknown": "미지", "loading": "로딩 중...", "copyFailed": "복사 실패", "people": "사람", "ok": "확인하다", "close": "닫기", "copied": "복사되었습니다.", "expand": "전개하다", "collapse": "접다", "none": "없음", "remark": "비고", "selectPlaceholder": "{{name}}를 선택하세요.", "on": "열다", "off": "닫다", "name": "식별자", "displayName": "표시 이름", "description": "설명", "ratio": "배율", "unnamed": "미지정 채널", "groups": "그룹화", "captchaPlaceholder": "인증 코드를 입력하세요.", "confirm": "확인", "permissions": "권한", "actions": "작업", "createdTime": "생성 시간", "expiredTime": "만료 시간", "search": "검색", "reset": "재설정", "refresh": "새로고침", "pagination": {"total": "총 {{total}} 개"}, "delete": "삭제", "edit": "편집", "add": "추가", "update": "업데이트", "back": "뒤로", "next": "다음", "previous": "이전", "success": "성공", "error": "오류", "warning": "경고", "info": "정보"}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "링크를 클릭하여 열기"}, "userRole": {"normal": "일반 사용자", "agent": "대리점", "admin": "관리자", "superAdmin": "슈퍼 관리자", "loading": "로딩 중..."}, "channelStatus": {"enabled": "활성화", "disabled": "사용 금지", "waitingRestart": "재시작 대기 중", "waiting": "기다림", "autoStoppedTitle": "채널 자동 재시도가 최대 횟수를 초과했거나 자동 비활성화 조건이 발생했습니다.", "stopped": "정지", "partiallyDisabled": "부분 사용 금지", "unknown": "미지", "reason": "원인"}, "channelBillingTypes": {"payAsYouGo": "량별 과금", "payPerRequest": "건당 요금제", "unknown": "미지의 방식"}, "tokenStatus": {"normal": "정상", "disabled": "사용 금지", "expired": "유효기간이 지났습니다.", "exhausted": "소진", "unknown": "미지"}, "userStatus": {"normal": "정상", "banned": "봉인", "unknown": "미지"}, "redemptionStatus": {"normal": "정상", "disabled": "사용 금지", "redeemed": "이미 교환됨", "expired": "유효기간이 지난", "unknown": "미지"}, "duration": {"request": "요청", "firstByte": "첫 바이트", "total": "총계", "seconds": "초", "lessThanOneSecond": "<1초"}, "streamType": {"stream": "스트리밍", "nonStream": "비스트리밍"}, "noSet": {"title": "관리자가 {{name}}을(를) 설정하지 않았습니다.", "name": {"about": "관한", "chat": "대화"}}, "buttonText": {"add": "추가됨", "cancel": "취소", "confirm": "확인", "delete": "삭제", "edit": "편집", "save": "저장", "updateBalance": "잔액 업데이트", "test": "테스트", "multiple": "다중 선택"}, "channelPage": {"title": "채널 관리"}, "channelStatusCount": {"title": "채널 상태 통계", "summary": "이미 활성화 {{enabled}} | 이미 비활성화 {{disabled}} | 재시도 중 {{retry}} | 이미 중지됨 {{stopped}}", "statusEnabled": "활성화됨", "statusDisabled": "비활성화되었습니다.", "statusRetry": "재시도 중", "statusStopped": "중단되었습니다.", "statusPartially": "부분 비활성화"}, "header": {"routes": {"status": "상태", "home": "홈페이지", "chat": "대화", "pptGen": "PPT 생성", "chart": "통계", "agency": "대리점", "channel": "채널", "ability": "채널 능력", "channelGroup": "채널 그룹", "token": "토큰", "log": "로그", "logDetail": "명세", "midjourney": "그림 그리기", "user": "사용자", "config": "구성", "packagePlanAdmin": "세트 메뉴", "redemption": "교환 코드", "group": "그룹화", "query": "조회", "about": "관한", "setting": {"default": "설정", "operation": "운영 설정", "system": "시스템 설정", "global": "전역 설정", "advance": "특성 설정", "sensitive": "민감한 단어 설정", "verification": "인증 코드 설정", "update": "업데이트 확인"}, "account": {"default": "계좌", "profile": "개인 센터", "cardTopup": "카드 코드 교환", "onlineTopup": "온라인 충전", "recharge": "잔액 충전", "balanceTransfer": "잔액 이체", "pricing": "비용 설명", "packagePlan": {"list": "패키지 구매", "record": "구매 기록"}, "notificationSettings": "알림 설정"}, "tools": {"default": "도구", "fileUpload": "파일 업로드", "keyExtraction": "키 추출", "multiplierCalculator": "배율 계산기", "shortLink": "단축 링크 생성", "testConnection": "방문 테스트", "customPrompts": "프롬프트 관리", "redis": "Redis 시각화", "ratioCompare": "배율 비교", "serverLog": "서버 로그 뷰어"}, "onlineTopupRecord": "충전 기록", "channelScores": "채널 점수", "dynamicRouter": "동적 라우팅", "task": "비동기 작업", "agencyJoin": "대리 가입"}, "dropdownMenu": {"profile": "개인 센터", "recharge": "잔액 충전", "agencyCenter": "대리점 센터", "checkin": "체크인", "darkMode": {"enable": "어두운 모드", "disable": "주간 모드"}, "fullScreen": {"default": "전체 화면 전환", "enable": "전체 화면 모드", "disable": "전체 화면 종료"}, "logout": "로그아웃"}, "checkin": {"default": "체크인", "success": "체크인 성공", "failed": "출석 실패", "verification": "검증을 완료해 주세요."}, "avatarProps": {"login": "로그인"}}, "settings": {"public": {"titles": {"default": "공공 설정"}, "SystemName": "시스템 이름", "ServerAddress": "서비스 주소", "TopUpLink": "충전 링크", "ChatLink": "대화 링크", "Logo": "시스템 로고", "HomePageContent": "홈페이지 내용", "About": "내용에 관하여", "Notice": "공지 내용", "Footer": "바닥글 내용", "RegisterInfo": "등록 알림", "HeaderScript": "사용자 정의 헤더", "SiteDescription": "사이트 설명", "PrivacyPolicy": "개인정보 처리방침", "ServiceAgreement": "서비스 계약", "FloatButton": {"FloatButtonEnabled": "열기", "DocumentInfo": "문서 정보", "WechatInfo": "위챗 메시지", "QqInfo": "QQ 정보"}, "CustomThemeConfig": "사용자 정의 테마", "AppList": "링크 교환"}}, "home": {"default": {"title": "환영합니다", "subtitle": "One API를 기반으로 한 2차 개발로 더 완벽한 기능을 제공합니다.", "start": "시작하기", "description": {"title": "신기능:", "part1": "전혀 새로운 사용자 인터페이스, 편리하고 빠름", "part2": "최적화된 스케줄링 메커니즘, 효율적이고 안정적", "part3": "기업 개발을 위한, 안전하고 신뢰할 수 있습니다.", "part4": "더 많은 고급 기능이 당신을 기다립니다."}}}, "dailyUsageChart": {"title": "일일 모델 사용 현황", "yAxisName": "사용량 (USD)", "loadingTip": "일일 사용 현황", "fetchError": "매일 사용 데이터를 가져오는 중 오류 발생:"}, "modelUsageChart": {"title": "모델 사용 현황", "hourlyTitle": "모델 매시간 사용 현황", "dailyTitle": "모델 일일 사용 현황", "weeklyTitle": "모델 주간 사용 현황", "monthlyTitle": "모델 매월 사용 현황"}, "granularity": {"hour": "매시간", "day": "매일", "week": "매주", "month": "매달", "all": "모두"}, "abilitiesTable": {"title": "채널 능력", "export": "내보내기", "group": "그룹", "model": "모델", "channelId": "채널 번호", "enabled": "활성화됨", "weight": "가중치", "priority": "우선순위", "billingType": "청구 유형", "functionCallEnabled": "기능 호출 활성화", "imageSupported": "이미지 지원", "yes": "네", "no": "아니요", "perToken": "토큰 기준 요금제", "perRequest": "요청에 따른 요금 청구", "noDataToExport": "내보낼 데이터가 없습니다.", "exportConfirm": "현재 페이지의 데이터를 내보내시겠습니까?", "exportSuccess": "내보내기 성공", "toggleSuccess": "전환 성공", "toggleError": "전환 실패", "selectOrInputGroup": "사용자 그룹 선택 또는 입력"}, "logsTable": {"retry": "재시도", "retryChannelList": "재시도 채널 목록", "retryDurations": "재시도 소요 시간 세부정보", "channel": "채널", "duration": "소요 시간", "startTime": "시작 시간", "endTime": "종료 시간", "retryCount": "재시도 횟수", "retryDetails": "재시도 세부정보", "totalRetryTime": "총 재시도 시간", "seconds": "초", "tokenGroup": "토큰 그룹화", "selectGroup": "선택 그룹", "dailyModelUsageStats": "데이터 호출 개요", "time": "시간", "moreInfo": "더 많은 정보", "ip": "IP", "remoteIp": "원격 IP", "ipTooltip": "IP: {{ip}}  \n원격 IP: {{remoteIp}}", "requestId": "요청 ID", "username": "사용자 이름", "userId": "사용자 ID", "tokenName": "토큰 이름", "token": "토큰", "type": "유형", "typeUnknown": "미지", "type充值": "충전", "type消费": "소비", "type管理": "관리", "type系统": "시스템", "type邀请": "초대", "type提示": "힌트", "type警告": "경고", "type错误": "오류", "type签到": "체크인", "type日志": "로그", "type退款": "환불", "type邀请奖励金划转": "초대 보상금 이체", "type代理奖励": "대리 보상", "type下游错误": "하류 오류", "type测试渠道": "테스트 채널", "typeRecharge": "충전", "typeConsumption": "소비", "typeManagement": "관리", "typeSystem": "시스템", "typeInvitation": "초대", "typePrompt": "힌트", "typeWarning": "경고", "typeError": "오류", "typeCheckin": "체크인", "typeLog": "로그", "typeRefund": "환불", "typeInviteReward": "초대 보상금 이체", "typeAgencyBonus": "대리 보상", "typeDownstreamError": "하류 오류", "typeChannelTest": "테스트 채널", "channelId": "채널 ID", "channelName": "채널 이름", "model": "모델", "modelPlaceholder": "모델 이름 입력/선택", "info": "정보", "isStream": "스트리밍", "isStreamPlaceholder": "입력/선택 여부 스트리밍", "prompt": "힌트", "completion": "보완하다", "consumption": "소비", "consumptionRange": "소비 한도 범위", "description": "설명", "action": "작업", "details": "상세 정보", "tokenKey": "토큰 키", "requestDuration": "요청 소요 시간", "firstByteDuration": "첫 바이트 소요 시간", "totalDuration": "총 소요 시간", "lessThanOneSecond": "<1초", "modelInvocation": "모델 호출", "modelUsage": "모델 사용 현황", "totalQuota": "총 소비 한도: {{quota}}", "totalRpm": "요청 수/분: {{rpm}}", "totalTpm": "토큰 수/분: {{tpm}}", "totalMpm": "금액/분: {{mpm}}", "dailyEstimate": "예상 일 소비: {{estimate}}", "currentStats": "현재 RPM:{{rpm}} 현재 TPM:{{tpm}} 현재 MPM:${{mpm}} 예상 일 소비:${{dailyEstimate}}", "statsTooltip": "미귀속 로그만 통계, RPM: 분당 요청 수, TPM: 분당 토큰 수, MPM: 분당 소비된 돈, 예상 일 소비는 현재 MPM을 기반으로 추론된 것입니다.", "showAll": "모두 보여주기", "exportConfirm": "이 페이지 로그 내보내기?", "export": "내보내기", "statsData": "통계 데이터", "today": "그날", "lastHour": "1시간", "last3Hours": "3시간", "lastDay": "1일", "last3Days": "3일", "last7Days": "7일", "lastMonth": "1개월", "last3Months": "3개월", "excludeModels": "배제 모델", "selectModelsToExclude": "제외할 모델을 선택하세요.", "excludeErrorCodes": "오류 코드를 제외하다", "excludeErrorCodesPlaceholder": "제외할 오류 코드를 선택하세요.", "errorCode": "오류 코드", "errorCodePlaceholder": "입력/선택 오류 코드", "timezoneTip": "현재 시간대: {timezone}", "timezoneNote": "시간대 알림", "timezoneDescription": "통계 데이터는 현재 설정된 시간대에 따라 날짜별로 그룹화됩니다. 서로 다른 시간대는 데이터 그룹화의 시간 범위에 차이를 초래할 수 있습니다. 조정이 필요하면 개인 센터로 가서 시간대 설정을 수정하세요.", "goToProfile": "개인 센터로 이동", "realtimeQuota": "실시간 소비(1분)", "viewTotalQuota": "총 소비 확인", "viewTotalQuotaTip": "역사적인 총 소비 금액 조회(조회에 몇 초가 걸릴 수 있습니다)", "loadingTotalQuota": "총 소비 금액을 조회하는 중입니다. 잠시만 기다려 주십시오...", "totalQuotaTitle": "역사 총 소비 통계", "loadTotalQuotaError": "총 소비 금액을 가져오는 데 실패했습니다.", "requestLogs": "요청 로그 - {{requestId}}", "noRequestLogs": "현재 요청 로그가 없습니다.", "metricsExplanation": "미귀속 로그만 통계, RPM: 분당 요청 수, TPM: 분당 토큰 수, MPM: 분당 소비되는 금액, 예상 일 소비는 현재 MPM을 기반으로 추정됩니다.", "autoRefresh": "자동 새로 고침", "autoRefreshTip": "자동 새로 고침을 켜거나 끄려면 클릭하세요. 켜면 지정된 초마다 데이터가 자동으로 새로 고쳐집니다.", "autoRefreshOn": "자동 새로 고침이 활성화되었습니다.", "autoRefreshOff": "자동 새로 고침이 종료되었습니다.", "refreshInterval": "새로 고침 간격", "stopRefresh": "새로 고침 중지", "secondsWithValue": "{{seconds}}초", "minutesWithValue": "{{minutes}}분", "title": "로그 관리", "columns": {"id": "ID", "createdTime": "생성 시간", "type": "유형", "content": "내용", "username": "사용자명", "tokenName": "토큰명", "modelName": "모델명", "quota": "할당량", "promptTokens": "프롬프트 토큰", "completionTokens": "완료 토큰"}, "actions": {"view": "보기", "retry": "재시도"}, "status": {"success": "성공", "failed": "실패"}, "messages": {"retrySuccess": "재시도 성공", "retryFailed": "재시도 실패"}}, "mjLogs": {"logId": "로그 ID", "submitTime": "제출 시간", "type": "유형", "channelId": "채널 ID", "userId": "사용자 ID", "taskId": "작업 ID", "submit": "제출", "status": "상태", "progress": "진행률", "duration": "소요 시간", "result": "결과", "prompt": "프롬프트", "promptEn": "프롬프트(영어)", "failReason": "실패 이유", "startTime": "시작 시간", "endTime": "종료 시간", "today": "그날", "lastHour": "1시간", "last3Hours": "3시간", "lastDay": "1일", "last3Days": "3일", "last7Days": "7일", "lastMonth": "1개월", "last3Months": "3개월", "selectTaskType": "작업 유형 선택", "selectSubmitStatus": "제출 상황 선택", "submitSuccess": "제출 성공", "queueing": "줄 서고 있습니다.", "duplicateSubmit": "중복 제출", "selectTaskStatus": "작업 상태 선택", "success": "성공", "waiting": "기다림", "queued": "줄 서다", "executing": "실행", "failed": "실패", "seconds": "초", "unknown": "미지", "viewImage": "클릭하여 보기", "markdownFormat": "마크다운 형식", "midjourneyTaskId": "미드저니 작업 ID", "copiedAsMarkdown": "Markdown 형식으로 복사되었습니다.", "copyFailed": "복사 실패", "copiedMidjourneyTaskId": "Midjourney 작업 ID가 복사되었습니다.", "drawingLogs": "그림 일지", "onlyUnarchived": "아직 아카이브되지 않은 로그만 통계합니다.", "imagePreview": "이미지 미리보기", "copiedImageUrl": "이미지 주소가 복사되었습니다.", "copy": "복사하다", "download": "다운로드", "resultImage": "결과 이미지", "downloadError": "이미지 다운로드 실패", "mode": "모드", "selectMode": "모드 선택", "relax": "쉬운 모드", "fast": "빠른 모드", "turbo": "고속 모드", "actions": "작업", "refresh": "새로 고침"}, "mjTaskType": {"IMAGINE": "이미지 생성", "UPSCALE": "확대", "VARIATION": "변화", "REROLL": "다시 생성하다", "DESCRIBE": "그림이 글을 낳다", "BLEND": "혼합 이미지", "OUTPAINT": "줌", "DEFAULT": "미지"}, "mjCode": {"submitSuccess": "제출 성공", "queueing": "줄 서고 있습니다.", "duplicateSubmit": "중복 제출", "unknown": "미지"}, "mjStatus": {"success": "성공", "waiting": "기다림", "queued": "줄 서다", "executing": "실행", "failed": "실패", "unknown": "미지"}, "tokensTable": {"title": "토큰 관리", "table": {"title": "토큰 관리", "toolBar": {"add": "새 토큰 만들기", "delete": "토큰 삭제", "deleteConfirm": "{{count}}개의 토큰을 일괄 삭제 중입니다. 이 작업은 되돌릴 수 없습니다.", "export": "내보내기", "exportConfirm": "현재 페이지 토큰 내보내기?"}, "action": "작업"}, "modal": {"title": {"add": "새 토큰 생성", "edit": "편집 토큰"}, "field": {"name": "토큰 이름", "description": "토큰 설명", "type": {"default": "청구 방식", "type1": "량별 과금", "type2": "건당 요금제", "type3": "혼합 요금제", "type4": "량 우선", "type5": "차순 우선"}, "status": "상태", "statusEnabled": "정상", "statusDisabled": "사용 금지", "statusExpired": "유효기간이 지난", "statusExhausted": "소진", "models": "모델", "usedQuota": "소비 한도", "remainQuota": "잔여 한도", "createdTime": "생성 시간", "expiredTime": "만료 시간", "all": "모두", "more": "더 많은", "notEnabled": "사용하지 않음", "unlimited": "무제한", "daysLeft": "{{days}} 일 후 만료됩니다.", "expired": "이미 만료된 지 {{days}} 일입니다.", "userId": "사용자 ID", "key": "API 키", "neverExpire": "영원히 만료되지 않음", "quota": "할당량", "unlimitedQuota": "무제한 할당량", "unlimitedExpired": "영구"}, "delete": {"title": "삭제", "content": "정말로 API 키 {{name}}를 삭제하시겠습니까?"}, "footer": {"cancel": "취소", "confirm": "확인", "update": "업데이트"}, "bridge": {"title": "채널 빠른 연결", "placeholder": "당신의 {{name}} 서비스 주소를 입력하세요."}, "copy": {"title": "수동 복사"}}, "dropdown": {"onlineChat": "온라인 대화", "disableToken": "토큰 사용 금지", "enableToken": "토큰 활성화", "editToken": "편집 토큰", "requestExample": "요청 예시", "tokenLog": "토큰 로그", "shareToken": "공유 토큰", "quickIntegration": "원클릭 연결"}, "error": {"fetchModelsFailed": "모델을 가져오는 데 실패했습니다: {{message}}", "batchDeleteFailed": "일괄 삭제 실패: {{message}}", "deleteTokenFailed": "토큰 삭제 실패：{{message}}", "refreshTokenFailed": "토큰 갱신 실패: {{message}}", "enableTokenFailed": "토큰 활성화 실패: {{message}}", "disableTokenFailed": "토큰 비활성화 실패: {{message}}", "fetchDataFailed": "데이터 가져오기 실패：{{message}}", "createTokenFailed": "토큰 생성 실패：{{message}}", "updateTokenFailed": "토큰 업데이트 실패：{{message}}"}, "success": {"batchDelete": "일괄 삭제 성공", "shareTextCopied": "공유 텍스트가 클립보드에 복사되었습니다.", "tokenCopied": "토큰이 클립보드에 복사되었습니다.", "deleteToken": "토큰 삭제 성공", "refreshToken": "토큰 갱신 성공", "enableToken": "토큰 활성화 성공", "disableToken": "토큰 비활성화 성공", "export": "현재 페이지 토큰 내보내기 성공", "createToken": "토큰 생성 성공", "updateToken": "토큰 업데이트 성공"}, "warning": {"copyFailed": "복사 실패, 수동으로 복사해 주세요.", "invalidServerAddress": "올바른 서버 주소를 입력하세요."}, "info": {"openingBridgePage": "연결 페이지를 열고 있습니다. 토큰을 복사했습니다."}, "export": {"name": "이름", "key": "키", "billingType": "청구 방식", "status": "상태", "models": "사용 가능한 모델", "usedQuota": "소비 한도", "remainQuota": "잔여 한도", "createdTime": "생성 시간", "expiredTime": "유효 기간", "unlimited": "무제한", "neverExpire": "영원히 만료되지 않음", "filename": "토큰 목록", "success": "내보내기 성공", "failed": "내보내기 실패"}, "billingType": {"1": "량별 과금", "2": "건당 요금제", "3": "혼합 요금제", "4": "량 우선", "5": "차순 우선"}, "bridge": {"quickIntegration": "원클릭 연결"}, "columns": {"name": "이름", "status": "상태", "quota": "할당량", "usedQuota": "사용된 할당량", "remainingQuota": "남은 할당량", "accessedTime": "접근 시간", "expiredTime": "만료 시간"}, "actions": {"copy": "복사", "enable": "활성화", "disable": "비활성화"}, "messages": {"copySuccess": "복사 성공", "enableSuccess": "활성화 성공", "disableSuccess": "비활성화 성공", "deleteSuccess": "삭제 성공"}}, "editTokenModal": {"editTitle": "편집 토큰", "createTitle": "토큰 생성", "defaultTokenName": "{{username}}의 토큰 {{date}}", "tokenName": "토큰 이름", "unlimitedQuota": "무한 한도", "remainingQuota": "잔여 한도", "authorizedQuota": "허가 한도", "quotaLimitNote": "토큰의 최대 사용 한도는 계좌 잔액에 따라 제한됩니다.", "quickOptions": "빠른 선택", "neverExpire": "영원히 만료되지 않음", "expiryTime": "유효 기간", "billingMode": "청구 방식", "selectGroup": "선택 그룹", "switchGroup": "선택 그룹", "switchGroupTooltip": "토큰이 속한 그룹을 선택하세요. 각 그룹마다 가격과 기능 권한이 다릅니다. 선택하지 않으면 현재 사용자가 속한 그룹이 기본으로 사용됩니다.", "switchGroupHint": "선택한 그룹은 토큰의 요금 배율과 사용 가능한 모델에 영향을 미치므로 실제 요구 사항에 따라 선택하십시오.", "importantFeature": "중요", "tokenRemark": "토큰 비고", "discordProxy": "디스코드 프록시", "enableAdvancedOptions": "고급 옵션 활성화", "generationAmount": "생성 수량", "availableModels": "사용 가능한 모델", "selectModels": "사용 가능한 모델 선택/검색/추가, 비워두면 제한 없음", "activateOnFirstUse": "첫 번째 사용 활성화", "activateOnFirstUseTooltip": "첫 사용 후 활성화 유효 기간, 이 옵션을 활성화하고 첫 사용을 통해 활성화하면 위에서 구성한 토큰 유효 기간이 덮어씌워집니다.", "activationValidPeriod": "활성화 유효 기간", "activationValidPeriodTooltip": "첫 사용 후 활성화된 토큰 유효 기간(단위: 일)", "ipWhitelist": "IP 화이트리스트", "ipWhitelistPlaceholder": "IP 주소(범위), IPV4 및 IPV6 지원, 여러 개는 쉼표로 구분", "rateLimiter": "제한기", "rateLimitPeriod": "제한 주기", "rateLimitPeriodTooltip": "제한 주기(단위: 초)", "rateLimitCount": "제한된 흐름 횟수", "rateLimitCountTooltip": "제한 주기 내 사용 가능한 횟수", "promptMessage": "알림 메시지", "promptMessageTooltip": "제한 초과 시의 알림 메시지", "promotionPosition": "홍보 위치", "promotionPositionStart": "시작", "promotionPositionEnd": "결말", "promotionPositionRandom": "무작위", "promotionContent": "홍보 내용", "currentGroup": "현재 그룹", "searchGroupPlaceholder": "검색 그룹 이름, 설명 또는 배율...", "mjTranslateConfig": "MJ 번역 설정", "mjTranslateConfigTip": "Midjourney 프롬프트 단어에만 적용되는 번역 설정", "mjTranslateBaseUrlPlaceholder": "번역 서비스의 기본 URL을 입력하세요.", "mjTranslateApiKeyPlaceholder": "번역 서비스의 API 키를 입력하세요.", "mjTranslateModelPlaceholder": "번역 서비스에 사용되는 모델 이름을 입력하세요.", "mjTranslateBaseUrlRequired": "번역을 활성화할 때 기본 URL을 제공해야 합니다.", "mjTranslateApiKeyRequired": "번역을 활성화하려면 API 키를 제공해야 합니다.", "mjTranslateModelRequired": "번역을 활성화할 때 모델 이름을 제공해야 합니다."}, "addTokenQuotaModal": {"title": "토큰 잔액 관리 {{username}}", "defaultReason": "관리자 작업", "enterRechargeAmount": "충전 금액을 입력하세요.", "enterRemark": "비고 메시지를 입력하세요.", "confirmOperation": "확인 작업", "confirmContent": "확인하시겠습니까? {{username}}{{action}}{{amount}}달러 {{updateExpiry}}?", "recharge": "충전", "deduct": "공제", "andUpdateExpiry": "그리고 잔액 유효 기간을 {{days}}일로 업데이트합니다.", "alertMessage": "음수를 입력하면 사용자 잔액이 차감될 수 있습니다.", "rechargeAmount": "충전 한도", "operationReason": "작업 원인", "finalBalance": "최종 잔액"}, "billingType": {"1": "량별 과금", "2": "건당 요금제", "3": "혼합 요금제", "4": "량 우선", "5": "차순 우선", "payAsYouGo": "량별 과금", "payPerRequest": "건당 요금제", "hybrid": "혼합 요금제", "payAsYouGoPriority": "량 우선", "payPerRequestPriority": "차순 우선", "unknown": "미지의 방식"}, "packagePlanAdmin": {"title": "세트 메뉴", "table": {"title": "패키지 관리", "toolBar": {"add": "새로운 패키지 만들기", "delete": "패키지 삭제"}, "action": {"edit": "편집", "delete": "삭제", "detail": "상세 정보", "recovery": "상장", "offline": "하차"}}, "modal": {"title": {"add": "새로운 패키지 만들기", "edit": "편집 패키지"}, "field": {"name": "패키지 이름", "type": {"default": "패키지 유형", "type1": "한도 패키지", "type2": "회차 패키지", "type3": "시간 패키지"}, "group": "패키지 그룹", "description": "패키지 설명", "price": "패키지 가격", "valid_period": "유효 기간", "first_buy_discount": "첫 구매 할인", "rate_limit_num": "횟수 제한", "rate_limit_duration": "제한 주기", "inventory": "패키지 재고", "available_models": "사용 가능한 모델", "quota": "패키지 한도", "times": "패키지 횟수"}, "footer": {"cancel": "취소", "confirm": "확인", "update": "업데이트"}}}, "login": {"title": "로그인", "username": "사용자명", "password": "비밀번호", "login": "로그인", "otherLoginMethods": "다른 로그인 방법", "register": "계정 등록", "accountLogin": "계정 로그인", "phoneLogin": "휴대폰 번호 로그인", "usernamePlaceholder": "사용자 이름", "usernameRequired": "사용자 이름을 입력하세요!", "passwordPlaceholder": "비밀번호", "passwordRequired": "비밀번호를 입력하세요!", "passwordMaxLength": "비밀번호 길이는 20자를 초과할 수 없습니다!", "phonePlaceholder": "휴대폰 번호", "phoneRequired": "휴대폰 번호를 입력하세요!", "phoneFormatError": "휴대폰 번호 형식이 잘못되었습니다!", "smsCodePlaceholder": "문자 인증 코드", "smsCodeCountdown": "{{count}}초 후에 다시 가져오기", "getSmsCode": "인증 코드 받기", "agreementText": "나는 동의합니다.", "privacyPolicy": "《개인정보 처리방침》", "and": "와", "serviceAgreement": "《서비스 계약》", "alreadyLoggedIn": "로그인하셨습니다.", "weakPasswordWarning": "귀하의 비밀번호가 너무 간단합니다. 즉시 수정해 주십시오!", "welcomeMessage": "환영합니다", "captchaError": "인증 코드 오류", "credentialsError": "사용자 이름 또는 비밀번호가 잘못되었습니다.", "resetPassword": "비밀번호 재설정", "captchaExpired": "인증 코드가 존재하지 않거나 만료되었습니다.", "loginFailed": "로그인 실패: {{message}}", "captchaRequired": "인증 코드를 입력하세요!", "captchaPlaceholder": "인증 코드", "smsSent": "문자 메시지 인증 코드 전송 성공", "smsSendFailed": "문자 메시지 인증 코드 전송 실패", "agreementWarning": "먼저 《개인정보 보호정책》과 《서비스 계약》에 동의해 주시기 바랍니다.", "turnstileWarning": "잠시 후 다시 시도해 주세요. Turnstile이 사용자 환경을 검사하고 있습니다!", "loginSuccess": "로그인 성공", "rememberMe": "로그인 상태 유지", "forgotPassword": "비밀번호 찾기", "loginButton": "로그인", "noAccount": "계정이 없으신가요?", "signUp": "회원가입"}, "register": {"title": "등록", "usernameRequired": "사용자 이름을 입력하세요!", "usernameNoAt": "사용자 이름에는 @ 기호를 포함할 수 없습니다.", "usernameNoChinese": "사용자 이름에는 중국어 문자가 포함될 수 없습니다.", "usernameLength": "사용자 이름 길이는 4-12자여야 합니다.", "usernamePlaceholder": "사용자 이름", "passwordRequired": "비밀번호를 입력하세요!", "passwordLength": "비밀번호 길이는 8-20자여야 합니다.", "passwordPlaceholder": "비밀번호", "confirmPasswordRequired": "비밀번호를 확인해 주세요!", "passwordMismatch": "두 번 입력한 비밀번호가 일치하지 않습니다!", "confirmPasswordPlaceholder": "비밀번호 확인", "emailInvalid": "유효한 이메일 주소를 입력하세요!", "emailRequired": "이메일을 입력하세요!", "emailPlaceholder": "이메일 주소", "emailCodeRequired": "이메일 인증 코드를 입력하세요!", "emailCodePlaceholder": "이메일 인증 코드", "enterCaptcha": "인증 코드를 입력하세요.", "resendEmailCode": "{{seconds}}초 후에 다시 전송합니다.", "getEmailCode": "인증 코드 받기", "phoneRequired": "휴대폰 번호를 입력하세요!", "phoneInvalid": "휴대전화 번호 형식이 올바르지 않습니다!", "phonePlaceholder": "휴대전화 번호", "smsCodeRequired": "문자 인증 코드를 입력하세요!", "smsCodePlaceholder": "문자 인증 코드", "resendSmsCode": "{{seconds}}초 후에 다시 전송합니다.", "getSmsCode": "인증 코드 받기", "captchaRequired": "인증 코드를 입력하세요!", "captchaPlaceholder": "인증 코드", "inviteCodePlaceholder": "초대 코드(선택 사항)", "submit": "등록", "successMessage": "등록 성공", "failMessage": "등록 실패", "emailCodeSent": "이메일 인증 코드가 전송되었습니다.", "smsCodeSent": "문자 인증 코드가 전송되었습니다.", "confirm": "확인", "emailVerifyTitle": "이메일 인증", "smsVerifyTitle": "문자 인증", "registerVerifyTitle": "등록 인증", "confirmPassword": "비밀번호 확인", "email": "이메일", "inviteCode": "초대 코드", "registerButton": "회원가입", "hasAccount": "이미 계정이 있으신가요?", "signIn": "로그인", "agreement": "동의합니다", "termsOfService": "서비스 약관"}, "profile": {"timezone": "시간대", "phoneNumber": "휴대폰 번호", "emailAddress": "이메일 주소", "wechatAccount": "위챗 계정", "telegramAccount": "텔레그램 계정", "bindTelegram": "텔레그램 연결", "balanceValidPeriod": "잔액 유효 기간", "lastLoginIP": "마지막 로그인 IP", "lastLoginTime": "마지막 로그인 시간", "inviteCode": "초대 코드", "inviteLink": "초대 링크", "generate": "생성", "pendingEarnings": "사용 대기 수익", "transfer": "전환", "totalEarnings": "총 수익", "accountBalance": "계좌 잔액", "totalConsumption": "누적 소비", "callCount": "호출 횟수", "invitedUsers": "사용자를 초대합니다.", "promotionInfo": "홍보 정보", "inviteDescription": "한 번 초대하면 평생 리베이트, 초대할수록 리베이트가 많아진다.", "userInfo": "사용자 정보", "availableModels": "사용 가능한 모델", "modelNameCopied": "모델 이름이 복사되었습니다.", "noAvailableModels": "사용 가능한 모델이 없습니다.", "accountOptions": "계정 옵션", "changePassword": "비밀번호 변경", "systemToken": "시스템 토큰", "unsubscribe": "해지", "educationCertification": "교육 인증", "timezoneUpdateSuccess": "시간대 업데이트 성공", "inviteLinkCopied": "초대 링크가 복사되었습니다.", "inviteLinkCopyFailed": "초대 링크 복사 실패", "inviteLinkGenerationFailed": "초대 링크 생성 실패", "allModelsCopied": "모든 모델이 클립보드에 복사되었습니다.", "copyAllModels": "모델 모두 복사하기", "totalModels": "사용 가능한 모델 수", "expired": "만료됨", "validPeriod": "유효기간", "longTermValid": "장기적으로 유효하다", "failedToLoadModels": "모델 목록 로드 실패", "accessTokens": "액세스 토큰", "accessTokensManagement": "액세스 토큰 관리", "accessTokenDescription": "액세스 토큰은 API 인증에 사용되며, 계정과 관련된 특정 권한을 가집니다", "tokenNameLabel": "토큰 이름", "tokenNamePlaceholder": "토큰에 이름을 지어주세요 (예: 읽기 전용 토큰, 테스트 도구 토큰 등)", "presetPermissions": "사전 설정 권한", "detailPermissions": "상세 권한", "validityPeriod": "유효 기간 (일)", "validityPeriodExtra": "0은 영구를 의미합니다", "remarkLabel": "비고", "remarkPlaceholder": "선택사항, 토큰에 비고 정보 추가", "createToken": "토큰 생성", "editToken": "토큰 편집", "deleteToken": "토큰 삭제", "copyToken": "토큰 복사", "tokenCreated": "토큰 생성 성공", "tokenUpdated": "토큰 업데이트 성공", "tokenDeleted": "토큰 삭제 성공", "tokenCopied": "토큰이 클립보드에 복사되었습니다", "deleteTokenConfirm": "액세스 토큰 「{{name}}」을 삭제하시겠습니까?", "disableTokenConfirm": "액세스 토큰 「{{name}}」을 비활성화하시겠습니까?", "enableTokenConfirm": "액세스 토큰 「{{name}}」을 활성화하시겠습니까?", "tokenSecurityWarning": "액세스 토큰을 안전하게 관리하고 타인과 공유하지 마세요", "tokenPermissionTip": "토큰 권한은 현재 계정 권한 범위를 초과할 수 없습니다", "tokenExpiryWarning": "토큰이 {{days}}일 후 만료됩니다", "tokenExpired": "토큰이 만료되었습니다", "tokenNeverExpires": "영구", "tokenLastUsed": "마지막 사용 시간", "tokenNeverUsed": "사용 안 함", "tokenUsageCount": "사용 횟수", "tokenCreatedAt": "생성 시간", "tokenStatus": "상태", "tokenActive": "활성", "tokenInactive": "비활성", "tokenDisabled": "비활성화됨", "permissions": {"read": "읽기 권한", "write": "쓰기 권한", "admin": "관리자 권한", "channels": "채널 관리", "tokens": "토큰 관리", "logs": "로그 보기", "users": "사용자 관리", "settings": "시스템 설정", "billing": "과금 관리", "analytics": "데이터 분석", "api": "API 액세스", "webhook": "Webhook 관리"}, "createNewToken": "새 토큰 생성", "tokenCreatedSuccess": "액세스 토큰 생성 성공", "tokenSavePrompt": "이 토큰을 안전하게 보관하세요. 한 번만 표시됩니다!", "readPermission": "읽기 권한", "writePermission": "쓰기 권한", "deletePermission": "삭제 권한", "tokenManagement": "Text", "channelManagement": "채널관리", "logView": "Text", "statisticsView": "통계 정보", "userManagement": "User", "quotaManagement": "할당량관리", "readOnlyPermission": "읽기 전용 권한", "writeOnlyPermission": "쓰기 전용 권한", "readWritePermission": "읽기/쓰기 권한", "standardPermission": "표준 권한", "fullPermission": "완전 권한", "selectPermission": "최소 하나의 권한을 선택하세요", "tokenEnabled": "활성화", "enableToken": "활성화", "disableToken": "비활성화", "tokenExpiryNever": "영구 유효", "accessTokensInfo": "액세스 토큰 설명", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Description", "accessTokensInfoDetail4": "Text", "accessTokensInfoDetail5": "Description", "noPermission": "Text"}, "topup": {"onlineRecharge": "온라인 충전", "cardRedemption": "교환 코드 사용", "accountBalance": "계좌 잔액", "rechargeReminder": "충전 알림", "reminder1": "1. 잔액은 모델 호출, 패키지 구매 등에 사용할 수 있습니다.", "reminder2": "2. 결제 후 한도가 입금되지 않으면 고객 서비스에 문의해 주시기 바랍니다.", "reminder3": "3. 잔액은 출금할 수 없지만 같은 사용자 그룹 내에서 이체할 수 있습니다.", "reminder4WithTransfer": "4. 충전 성공 후, 계좌 잔액 유효 기간이 재설정됩니다.", "reminder4WithoutTransfer": "3. 충전 성공 후, 계좌 잔액 유효 기간이 재설정됩니다.", "days": "하늘", "paymentSuccess": "결제 성공", "paymentError": "결제 오류", "paymentAmount": "지불 금액:", "purchaseAmount": "구매 한도: $", "yuan": "위안", "or": "혹", "usd": "미국 달러", "cny": "원", "enterAmount": "충전 금액을 입력하세요!", "amountPlaceholder": "충전 금액을 입력하세요. {{min}} 미국 달러부터 시작합니다.", "amountUpdateError": "금액 업데이트 중 오류가 발생했습니다.", "alipay": "알리페이", "wechat": "위챗", "visaMastercard": "비자 / 마스터카드", "cardFormatError": "교환 코드 형식 오류", "redeemSuccess": "{{amount}} 환전 성공!", "redeemError": "교환 오류가 발생했습니다. 잠시 후 다시 시도해 주세요.", "enterCardKey": "교환 코드 카드를 입력하세요.", "cardKeyPlaceholder": "교환 코드 또는 카드 번호를 입력하세요.", "buyCardKey": "구매 교환 코드 카드 비밀번호", "redeem": "즉시 사용하다", "record": {"title": "충전 기록", "amount": "충전 한도", "payment": "지불 금액", "paymentMethod": "지불 방법", "orderNo": "주문 번호", "status": "상태", "createTime": "생성 시간", "statusSuccess": "성공", "statusPending": "처리 중", "statusFailed": "실패"}, "paymentMethodAlipay": "알리페이", "paymentMethodWxpay": "위챗", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "이더리움", "paymentMethodPaypal": "페이팔", "paymentMethodAdmin": "관리자", "paymentMethodRedeem": "교환 코드", "alipayF2F": "Alipay 대면 결제"}, "pricing": {"fetchErrorMessage": "가격 정보 조회에 문제가 발생했습니다. 관리자에게 문의해 주세요.", "availableModelErrorMessage": "사용 가능한 모델을 가져오는 중 오류가 발생했습니다. 관리자에게 문의하십시오.", "modelName": "모델 이름", "billingType": "청구 유형", "price": "가격", "ratio": "배율", "promptPriceSame": "제안 가격: 원래 요금과 동일", "completionPriceSame": "가격 보완: 원래 요금과 동일", "promptPrice": "가격 안내: $ {{price}} / 1M 토큰", "completionPrice": "가격 보충: $ {{price}} / 1M 토큰", "promptRatioSame": "힌트 배율: 원래 배율과 동일합니다.", "completionRatioSame": "보정 배율: 원래 배율과 동일하다.", "promptRatio": "힌트 배율：{{ratio}}", "completionRatio": "보완 배율: {{ratio}}", "payAsYouGo": "[TRANSLATION_FAILED] 사용량 기반 요금 - Chat", "fixedPrice": "$ {{price}} / 회", "payPerRequest": "사용량 기반 요금 - 채팅", "dynamicPrice": "$ {{price}} / 회", "payPerRequestAPI": "사용량 기반 요금제 - API", "loadingTip": "가격 정보를 가져오는 중...", "userGroupRatio": "귀하의 사용자 그룹 비율은: {{ratio}}입니다.", "readFailed": "읽기 실패", "billingFormula": "량에 따른 요금 = 전환율 × 그룹 배율 × 모델 배율 × (힌트 토큰 수 + 보완 토큰 수 × 보완 배율) / 500000 (단위: 달러)", "billingFormula1": "전환율 = (새로운 충전 배율 / 원래 충전 배율) × (새로운 그룹 배율 / 원래 그룹 배율)", "generatedBy": "이 페이지는 {{systemName}}에 의해 자동 생성되었습니다.", "modalTitle": "가격 세부정보", "perMillionTokens": "/1M 토큰", "close": "닫기", "searchPlaceholder": "모델 이름 검색", "viewGroups": "그룹 보기", "copiedToClipboard": "클립보드에 복사되었습니다.", "copyFailed": "복사 실패", "groupName": "그룹 이름", "availableGroups": "모델 {{model}}에서 사용할 수 있는 그룹", "noGroupsAvailable": "사용 가능한 그룹이 없습니다.", "modelGroupsErrorMessage": "모델 그룹 데이터 가져오기 실패", "currentGroup": "현재 그룹", "copyModelName": "모델 이름 복사", "groupRatio": "그룹 비율", "closeModal": "닫기", "groupsForModel": "모델 사용 가능한 그룹", "actions": "작업", "filterByGroup": "그룹별 필터링", "groupSwitched": "그룹으로 전환되었습니다: {{group}}", "showAdjustedPrice": "그룹 조정 후 가격 표시 (현재 배율: {{ratio}})"}, "guestQuery": {"usageTime": "사용 시간", "modelName": "모델 이름", "promptTooltip": "입력 소모 토큰", "completionTooltip": "출력 소비 토큰", "quotaConsumed": "소비 한도", "pasteConfirm": "클립보드에 유효한 토큰이 감지되었습니다. 붙여넣으시겠습니까?", "queryFailed": "조회 실패", "tokenExpired": "해당 토큰이 만료되었습니다.", "tokenExhausted": "해당 토큰 한도가 소진되었습니다.", "invalidToken": "올바른 토큰을 입력하세요.", "focusRequired": "페이지가 포커스 상태인지 확인하십시오.", "queryFirst": "먼저 조회해 주세요.", "tokenInfoText": "토큰 총액: {{totalQuota}}  \n토큰 소비: {{usedQuota}}  \n토큰 잔액: {{remainQuota}}  \n호출 횟수: {{callCount}}  \n유효 기간: {{validUntil}}", "unlimited": "무제한", "neverExpire": "영원히 만료되지 않음", "infoCopied": "토큰 정보가 클립보드에 복사되었습니다.", "copyFailed": "복사 실패", "noDataToExport": "내보낼 데이터가 없습니다.", "prompt": "힌트", "completion": "보완하다", "disabled": "방문자 조회가 활성화되지 않았습니다.", "tokenQuery": "토큰 조회", "tokenPlaceholder": "조회할 토큰을 입력하세요 (sk-xxx)", "tokenInfo": "토큰 정보", "copyInfo": "정보 복사", "totalQuota": "토큰 총액", "usedQuota": "토큰 소모", "remainQuota": "토큰 잔액", "callCount": "호출 횟수", "validUntil": "유효기간까지", "currentRPM": "현재 RPM", "currentTPM": "현재 TPM", "callLogs": "호출 로그", "exportLogs": "로그 내보내기"}, "agencyProfile": {"fetchError": "대리점 정보 가져오기 실패", "fetchCommissionError": "수수료 목록을 가져오는 데 실패했습니다.", "systemPreset": "시스템 기본 설정", "lowerRatioWarning": "요금이 시스템 설정보다 낮습니다.", "lowerRatioMessage": "다음 요금이 시스템 설정값보다 낮습니다. 즉시 수정해 주십시오:", "cancelRatioEdit": "수수료 변경 취소", "updateSuccess": "업데이트 성공", "updateError": "대리점 정보 업데이트 실패:", "updateFailed": "업데이트 실패", "customPriceUpdateSuccess": "커스터마이즈된 가격 업데이트 성공", "customPriceUpdateError": "사용자 정의 가격 업데이트 실패:", "time": "시간", "type": "유형", "agencyCommission": "대리점 리베이트", "unknownType": "미지의 유형", "amount": "금액", "balance": "잔액", "description": "설명", "group": "그룹화", "customRate": "사용자 정의 요금", "systemDefaultRate": "시스템 기본 요금", "action": "작업", "save": "저장", "cancel": "취소", "edit": "편집", "agencyConsole": "대리점 관리 콘솔", "agencyInfo": "대리점 정보", "editInfo": "편집 정보", "agencyName": "대리점 이름", "agencyLevel": "대리점 등급", "level1": "레벨 1", "subordinateUsers": "하위 사용자", "totalSales": "총 판매액", "commissionIncome": "수수료 수입", "cumulativeEarnings": "누적 수익", "agencyFunctions": "대리 기능", "hideSubordinateUsers": "하위 사용자를 숨기기", "viewSubordinateUsers": "하위 사용자 확인", "hideCommissionDetails": "숨겨진 수수료 세부사항", "viewCommissionDetails": "커미션 세부사항 확인", "hideCustomPrice": "사용자 정의 가격 숨기기", "setCustomPrice": "맞춤 가격 설정", "subordinateUsersList": "하위 사용자 목록", "commissionRecords": "수수료 기록", "customPriceSettings": "맞춤 가격 설정", "saveChanges": "변경 사항 저장", "editAgencyInfo": "대리점 정보 수정", "logo": "로고", "setAgencyLogo": "대리점 로고 설정", "customHomepage": "맞춤형 홈 페이지", "aboutContent": "내용에 관하여", "newHomepageConfig": "새로운 홈 페이지 구성", "customAnnouncement": "사용자 정의 공지사항", "customRechargeGroupRateJson": "사용자 정의 충전 그룹 요금 JSON", "customRechargeRate": "맞춤형 충전 요금율", "viewSystemDefaultRate": "시스템 기본 요금 확인", "rateComparison": "요금 비교", "comparisonResult": "비교 결과", "higherThanSystem": "시스템보다 높다", "lowerThanSystem": "시스템 이하", "equalToSystem": "시스템과 같다", "unknown": "미지", "notAnAgentYet": "당신은 아직 대리점이 아닙니다.", "becomeAnAgent": "대리점이 되다", "startYourOnlineBusiness": "🌟 쉽게 온라인 사업을 시작하세요", "becomeOurAgent": "우리의 대리점이 되어 스트레스 없는 창업 경험을 누리세요:", "noInventory": "💼 재고 압박 없이, 자금 회전 압박 제로", "instantCommission": "💰 판매 즉시 분배, 비율에 따라 풍부한 보상 획득", "easyManagement": "🖥️ 웹사이트 구축 기술 없이 쉽게 온라인 상점을 관리하세요.", "flexibleDomainChoice": "유연한 도메인 선택", "youCan": "당신은 할 수 있습니다:", "useOwnDomain": "🏠 자신의 도메인 사용하기", "orUseOurSubdomain": "🎁 또는 저희가 귀하를 위해 전용 서브 도메인을 제공해 드립니다.", "convenientStart": "🔥 경험이 풍부하시든 이제 막 시작하시든, 저희는 편리한 시작 방법을 제공합니다.", "actNow": "🚀 즉시 행동하세요!", "contactAdmin": "웹사이트 관리자에게 연락하여 귀하의 대리점 여정을 시작하세요! 📞", "applyNow": "즉시 신청하기", "contactCooperation": "상담 협력", "understandPolicy": "대리점 정책 및 협력 세부사항 이해하기", "provideDomain": "도메인 제공", "configDomain": "귀하의 도메인을 제공해 주시면 저희가 설정해 드리겠습니다.", "promoteAndEarn": "홍보 수익", "startPromoting": "귀하의 대리 사이트를 홍보하여 수수료를 벌어보세요.", "noDeploymentWorries": "복잡한 클라우드 서비스 배포, 결제 채널, 재고 문제에 대해 걱정할 필요가 없습니다.", "easySetup": "도메인만 제공하면, 튜토리얼에 따라 설정하여 쉽게 기업급 API 대리점 비즈니스를 시작할 수 있습니다.", "customizeContent": "가격, 사이트 정보, <PERSON><PERSON>, 로고 등의 내용을 맞춤 설정할 수 있습니다.", "commissionBenefits": "대리점으로서, 귀하는 사용자 충전 수익의 일부를 받게 되며, 시스템은 자동으로 비용을 차감하고 남은 금액은 언제든지 출금할 수 있습니다.", "joinNowBenefit": "지금 바로 우리와 함께 AI 시대의 혜택을 누리세요!", "groups": {"student": "대학생", "studentDesc": "시간이 넉넉하니, 홍보 활동을 통해 수입을 쉽게 늘려 일부 생활비와 오락비를 부담하고 싶습니다.", "partTime": "부업 또는 아르바이트", "partTimeDesc": "많은 시간 투자가 필요하지 않으며, 업무 외 시간에 간단히 홍보하기만 하면 쉽게 추가 수입을 올릴 수 있습니다.", "mediaWorker": "자기 미디어 종사자", "mediaWorkerDesc": "일정한 팬층이 있다면, 글이나 게시물의 끝에 링크를 추가하기만 하면 쉽게 추가 수익을 얻을 수 있습니다.", "freelancer": "프리랜서", "freelancerDesc": "많은 유연한 시간을 가지고, 판매 활동에 참여함으로써 쉽게 추가 수입을 늘릴 수 있습니다."}, "stories": {"story1": {"name": "장 선생님", "role": "대학생"}, "story2": {"name": "리 씨", "role": "중학교 교사"}, "story3": {"name": "리우 선생님", "role": "전자상거래"}, "story4": {"name": "정 선생님", "role": "자기 미디어"}, "story5": {"name": "주 선생님", "role": "연구개발 종사자"}, "story6": {"name": "왕 씨", "role": "샤오홍슈 블로거"}, "story7": {"name": "황 여사", "role": "자기 미디어"}, "story8": {"name": "리우 선생님", "role": "IT 산업"}}, "earnedAmount": "이미 벌어들인 금액: {{amount}}", "applyForAgentNow": "즉시 대리점으로 신청하세요.", "businessLinesConnected": "40개 이상의 비즈니스 라인이 이미 연결되었습니다.", "agencyJoin": "Text", "becomeExclusiveAgent": "우리의 전속 대리인이 되세요.", "startBusinessJourney": "[TRANSLATION_FAILED] 비즈니스 여정을 쉽게 시작하세요~", "welcomeToAgencyPage": "우리의 에이전트 페이지에 오신 것을 환영합니다!", "earningsTitle": "[TRANSLATION_FAILED] 100명 이상이 벌었습니다3000+원", "becomeAgentSteps": "대리점이 되는 단계", "agencyRules": "대리 규칙", "suitableGroups": "적합한 대상", "agencyImages": {"becomeAgent": "대리점이 되다", "agencyBusiness": "대리업무"}, "rules": {"howToEstablishRelation": "사용자가 어떻게 저와 대리 관계를 맺을 수 있나요?", "howToEstablishRelationAnswer": "당신의 대리 사이트에 등록하면, 당신의 사용자입니다.", "canSetPrice": "가격을 설정할 수 있나요?", "canSetPriceAnswer": "괜찮아요! 하지만 당신의 판매가는 원가보다 10% 이상 높아야 합니다.", "commissionShare": "나는 얼마나 많은 수익을 얻을 수 있나요?", "commissionShareAnswer": {"assumption": "가정: 당신의 매입가는 $1=1위안이고, 판매가는 $1=2위안이며, 수수료 비율은 90%입니다.", "example": "[TRANSLATION_FAILED] 사용자가 귀하의 사이트에서 구매$10，소비20원", "calculation": "당신은 다음과 같이 얻을 수 있습니다: (2-1)*10*0.9 = 9위안", "explanation": "해석: (판매가 - 매입가) * 거래량 * 수수료 비율"}}, "title": "에이전트 프로필", "basicInfo": "기본 정보", "commissionSettings": "수수료 설정", "statistics": "통계 정보", "totalUsers": "총 사용자 수", "totalRevenue": "총 수익", "monthlyRevenue": "월 수익", "commissionRate": "수수료율"}, "error": {"title": "오류", "content": "오류가 발생했습니다."}, "loading": {"title": "로딩 중", "content": "로딩 중..."}, "notfound": {"title": "404", "content": "페이지를 찾을 수 없습니다."}, "servererror": {"title": "500", "content": "서버 오류"}, "unauthorized": {"title": "401", "content": "무단 사용"}, "forbidden": {"title": "403", "content": "접근 금지"}, "networkerror": {"title": "네트워크 오류", "content": "네트워크 오류"}, "timeout": {"title": "초과 시간", "content": "요청 시간 초과"}, "noresult": {"title": "결과 없음", "content": "결과 없음"}, "nopermission": {"title": "권한 없음", "content": "권한 없음"}, "channelBridge": {"title": "채널 빠른 연결", "channelPlatform": "채널 플랫폼", "billingMethod": "청구 방식", "channelName": "채널 이름", "remark": "비고", "availableGroups": "사용 가능한 그룹", "availableModels": "사용 가능한 모델", "channelKey": "채널 키", "proxyAddress": "연결 주소", "cancel": "취소", "submit": "제출", "gpt35Models": "GPT-3.5 모델", "gpt4Models": "GPT-4 모델", "clear": "비우기", "customModelName": "사용자 정의 모델 이름", "add": "추가하다", "moreConfigReminder": "더 많은 설정은 채널을 저장한 후 편집하십시오.", "quickIntegration": "원클릭 연결", "selectBillingMethod": "청구 방식을 선택하세요.", "enterChannelName": "채널 이름을 입력하세요.", "enterChannelRemark": "채널 비고를 입력하세요.", "selectAvailableGroups": "해당 채널을 사용할 수 있는 그룹을 선택하세요.", "selectAvailableModels": "채널에서 사용할 수 있는 모델 선택/검색", "enterChannelKey": "채널 키를 입력하세요.", "proxyAddressPlaceholder": "이 항목은 선택 사항이며, API 호출을 위해 프록시 서버를 통해 사용할 수 있습니다. 프록시 서버 주소를 입력해 주세요.", "includes16kModels": "16k 모델 포함", "excludes32kModels": "32k 모델이 포함되어 있지 않습니다.", "cleared": "비워졌습니다.", "addCustomModel": "사용자 정의 모델 추가", "clipboardTokenDetected": "클립보드에 유효한 토큰이 감지되었습니다. 붙여넣으시겠습니까?", "channelIntegrationSuccess": "[TRANSLATION_FAILED] 채널 연결 성공！", "channelIntegrationFailed": "채널 연결 실패:"}, "about": {"loading": "최신 내용 가져오기...", "noContent": "관리자가 정보 페이지 내용을 설정하지 않았습니다.", "loadFailed": "내용 로드 실패..."}, "onlineTopupRecord": {"title": "충전 기록", "columns": {"id": "아이디", "username": "사용자", "amount": "충전 한도", "money": "지불 금액", "paymentMethod": "지불 방법", "tradeNo": "주문 번호", "status": "상태", "createTime": "생성 시간"}, "status": {"success": "성공", "pending": "처리 중", "failed": "실패"}, "paymentMethod": {"alipay": "알리페이", "wxpay": "위챗", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "이더리움", "paypal": "페이팔"}}, "logContentDetail": {"description": "설명 정보", "downstreamError": "하류 오류", "originalError": "원시 오류", "requestParams": "요청 매개변수", "copy": "복사하다"}, "viewMode": {"switchTo": "{{mode}} 시각으로 전환하다", "cost": "비용", "usage": "사용량"}, "agenciesTable": {"title": "대리점 관리", "addAgency": "신규 대리점", "columns": {"id": "아이디", "userId": "사용자 ID", "name": "이름", "domain": "도메인", "commissionRate": "수수료 비율", "salesVolume": "매출액", "userCount": "사용자 수", "commissionIncome": "수수료 수입", "historicalCommission": "누적 수익", "actions": "작업"}, "confirm": {"deleteTitle": "이 대리점을 삭제하시겠습니까?", "updateName": "대리점 이름을 업데이트하는 중입니다...", "updateSuccess": "업데이트 성공", "updateFailed": "업데이트 실패", "deleteSuccess": "삭제 성공!"}, "messages": {"getListFailed": "대리점 목록을 가져오는 데 실패했습니다: {{message}}", "deleteSuccess": "삭제 성공!", "loadingData": "로딩 중..."}}, "units": {"times": "다음", "percentage": "{{value}}%", "formatUsage": "{{name}}：{{value}} 회 ({{percent}}%)"}, "dailyUsage": {"total": "총계", "totalCost": "총 비용", "tooltipTitle": {"cost": "비용 상황", "usage": "사용 상황"}, "yAxisName": {"cost": "비용 (USD)", "usage": "사용량 (USD)"}}, "dailyUsageByModel": {"total": "총계", "tooltipTotal": "총계: $ {{value}}", "switchTo": "전환하다", "cost": "비용", "usage": "사용량", "perspective": "시각", "granularity": {"hour": "시간당", "day": "일별", "week": "주별", "month": "월별"}}, "checkinModal": {"title": "검증을 완료해 주세요.", "captchaPlaceholder": "인증 코드", "confirm": "확인하다", "close": "닫기"}, "balanceTransfer": {"title": "계좌 간 이체", "accountInfo": {"balance": "계좌 잔액", "transferFee": "이체 수수료", "groupNote": "동일한 사용자 그룹 간에만 이체할 수 있습니다."}, "form": {"receiverId": "수신자 ID", "receiverUsername": "수신자 사용자 이름", "remark": "비고 정보", "amount": "이체 금액", "expectedFee": "예상 차감액", "submit": "이체 시작"}, "result": {"success": "이체 성공", "continueTransfer": "계속 이체하세요.", "viewRecord": "기록 보기"}, "warning": {"disabled": "관리자가 전송 기능을 활성화하지 않아 현재 사용할 수 없습니다."}, "placeholder": {"autoCalculate": "이체 금액 자동 계산 입력"}}, "channelsTable": {"title": "채널 관리", "columns": {"id": "아이디", "name": "이름", "type": "유형", "key": "키", "base": "인터페이스 주소", "models": "모델", "weight": "가중치", "priority": "우선순위", "retryInterval": "재시도 간격", "responseTime": "응답 시간", "rpm": "RPM", "status": "상태", "quota": "잔액", "expireTime": "유효 기간", "group": "그룹화", "billingType": "청구 유형", "actions": "작업", "fusing": "서킷 브레이커", "sort": "우선순위", "balance": "잔액", "balanceUpdatedTime": "잔액 업데이트 시간", "testTime": "테스트 시간", "createdTime": "생성 시간", "disableReason": "비활성화 이유"}, "status": {"all": "모든", "normal": "정상", "enabled": "정상 상태", "manualDisabled": "수동 비활성화", "waitingRetry": "재시작 대기 중", "suspended": "사용 중지", "specified": "지정된 상태", "allDisabled": "사용 금지", "specifiedDisabled": "지정된 비활성화 유형", "partiallyDisabled": "부분 비활성화"}, "placeholder": {"selectGroup": "그룹 선택/검색", "selectStatus": "채널 상태 선택", "inputSelectModel": "모델 이름 입력/선택", "selectFusingStatus": "자동 차단 상태 선택"}, "quota": {"usageAmount": "소모: {amount}", "remainingAmount": "잔여: {amount}", "customTotalAmount": "사용자 정의 총액: {amount}", "updateNotSupported": "현재 잔액 업데이트를 지원하지 않습니다. 사용자 정의 잔액을 사용해 주세요.", "details": "상세 정보", "sufficient": "충분하다"}, "actions": {"edit": "편집", "copy": "클론 채널", "delete": "삭제", "enable": "활성화", "disable": "비활성화", "test": "테스트", "advancedTest": "고급 테스트", "viewLog": "채널 로그", "viewAbility": "능력 확인", "cleanUsage": "사용된 항목 지우기", "updateBalance": "잔액 업데이트", "copyKey": "키 복사", "topup": "충전", "viewModels": "모델 보기"}, "confirm": {"deleteTitle": "삭제 확인", "deleteContent": "채널 {{name}}(#{{id}})을(를) 정말로 삭제하시겠습니까?", "cleanUsageTitle": "사용량 초기화 확인", "cleanUsageContent": "채널 {{name}}（#{{id}}）의 사용된 금액을 정말로 비우시겠습니까?", "testTitle": "확인 테스트", "testContent": "{{status}} 채널을 테스트하시겠습니까?", "testNote": "주의: 이 기능은 [설정] -> [중계] -> [모니터링 설정] -> [실패 시 채널 비활성화, 성공 시 채널 활성화]와 함께 사용해야 합니다. 관련 설정이 활성화되지 않으면 테스트 완료 후 채널이 자동으로 비활성화되거나 활성화되지 않습니다.", "deleteDisabledTitle": "삭제 확인", "deleteDisabledContent": "모든 {{type}} 채널을 삭제하시겠습니까?"}, "messages": {"operationSuccess": "작업 성공", "operationSuccessWithSort": "작업이 성공적으로 완료되었습니다. 채널 순서가 변경될 수 있으니 ID로 정렬하는 것을 권장합니다!", "operationFailed": "작업 실패：{{message}}", "testRunning": "채널 {{name}}(#{{id}}) 테스트 실행 중, 잠시 기다려주세요...", "testSuccess": "채널「{{name}}(#{{id}})」{{model}}테스트 성공, 응답 시간 {{time}} 초", "testFailed": "채널「{{name}}(#{{id}})」테스트 실패：{{message}}", "testStarted": "{{status}} 채널의 테스트를 시작합니다. 잠시 후 결과를 확인하려면 새로 고침하세요. 테스트 결과의 적용은 귀하의 모니터링 설정에 따라 다릅니다.", "testOperationFailed": "테스트 실패", "deleteSuccess": "삭제 성공", "deleteFailed": "삭제 실패: {{message}}", "modelPrefix": "모델 {{model}}", "channelInfo": "채널 정보", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "채널「{{name}}」잔액 업데이트 성공", "updateBalanceFailed": "채널「{{name}}」 잔액 업데이트 실패: {{message}}", "updateAllBalanceStarted": "모든 정상 상태의 채널 잔액을 업데이트하기 시작합니다.", "updateAllBalanceSuccess": "모든 채널 잔액이 성공적으로 업데이트되었습니다.", "fetchGroupError": "채널 그룹 데이터 가져오기 오류: {{response}}", "fetchChannelError": "채널 데이터 가져오기 실패: {{message}}", "selectChannelFirst": "삭제할 채널을 먼저 선택하세요.", "deleteDisabledSuccess": "모든 {{type}} 채널이 삭제되었습니다. 총 {{count}} 개입니다.", "deleteOperationFailed": "삭제 실패", "copySuccess": "복사 성공", "copyFailed": "복사 실패: {{message}}", "emptyKey": "키가 비어 있습니다.", "enableSuccess": "활성화 성공", "disableSuccess": "비활성화 성공", "updateSuccess": "업데이트 성공", "deleteConfirm": "채널「{{name}}」을 삭제하시겠습니까?", "batchDeleteConfirm": "{{count}} 개의 채널을 일괄 삭제하시겠습니까?", "testSuccessWithWarnings": "Message", "viewDetails": "세부정보 보기", "fetchChannelDetailError": "가져오기채널세부정보 가져오기 실패：{{message}}", "topupSuccess": "충전 성공", "topupFailed": "충전 실패：{{message}}"}, "popover": {"channelInfo": "채널 정보"}, "menu": {"deleteManualDisabled": "수동으로 비활성화된 채널 삭제", "deleteWaitingRetry": "대기 중인 재시작 채널 삭제", "deleteSuspended": "사용 중지된 채널 삭제", "testAll": "모든 채널 테스트", "testNormal": "정상 채널 테스트", "testManualDisabled": "수동 채널 비활성화 테스트", "testWaitingRetry": "테스트 대기 재시작 채널", "testSuspended": "테스트 중단 사용 채널", "deleteDisabledAccount": "비활성화된 계정 삭제", "deleteQuotaExceeded": "할당량 초과 채널 삭제", "deleteRateLimitExceeded": "빈도 제한 삭제채널", "deleteInvalidKey": "유효하지 않은 키 삭제채널", "deleteConnectionError": "Error"}, "tooltip": {"testNote": "[설정] -> [중계] -> [모니터링 설정] -> [실패 시 채널 비활성화, 성공 시 채널 활성화]와 함께 사용해야 합니다. 이 기능이 활성화되지 않으면 속도 측정이 완료된 후 자동으로 비활성화되거나 활성화되지 않습니다."}, "disableReasons": {"account_deactivated": "계정 비활성화", "quota_exceeded": "할당량 초과", "rate_limit_exceeded": "빈도 제한", "invalid_key": "유효하지 않은 키", "connection_error": "Error"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "한도", "times": "횟수"}, "serverLogViewer": {"title": "서버 로그 뷰어", "connecting": "서버에 연결 중...", "downloadSelect": "로그 파일 다운로드 선택", "nginxConfig": "Nginx WebSocket 구성 설명", "directAccess": "도메인을 사용하여 접근하고 WebSocket 지원이 구성되지 않은 경우, 로그 뷰어가 작동하지 않습니다. 이 경우 서버 IP와 포트를 통해 직접 접근할 수 있습니다(예: http://your-ip:9527).", "domainAccess": "도메인을 통해 접근하려면 Nginx 설정에 다음 구성을 추가하여 WebSocket을 지원해야 합니다:", "buttons": {"pause": "일시 정지", "resume": "계속", "clear": "비우기"}, "errors": {"fetchFailed": "로그 파일 목록을 가져오는 데 실패했습니다.", "downloadFailed": "로그 파일 다운로드 실패", "wsError": "WebSocket 연결 오류"}}, "channelScore": {"score": "득점", "successRate": "성공률", "avgResponseTime": "평균 응답 시간", "title": "채널 점수", "hourlyTitle": "채널 시간당 점수", "dailyTitle": "채널 일일 점수", "weeklyTitle": "채널 주간 점수", "monthlyTitle": "채널 월 점수", "allTimeTitle": "채널 전체 점수", "infoTooltip": "채널 점수는 성공률과 응답 시간을 기반으로 계산된 종합 점수입니다.", "tableView": "표 형식 보기", "chartView": "차트 뷰", "refresh": "새로 고침", "selectModel": "모델 선택", "allModels": "모든 모델", "sortByScore": "점수 순으로 정렬", "sortBySuccessRate": "성공률 순으로 정렬", "sortByResponseTime": "응답 시간 순으로 정렬", "noData": "데이터가 없습니다.", "totalItems": "총 {{total}} 항목", "fetchError": "채널 점수 데이터 가져오기 실패", "aboutScoring": "점수 계산에 관하여", "scoringExplanation": "채널 점수는 성공률과 응답 시간을 기반으로 계산된 종합 점수로, 만점은 1점입니다.", "successRateWeight": "성공률 가중치 (70%)", "successRateExplanation": "성공률이 높을수록 점수가 높아진다.", "responseTimeWeight": "응답 시간 가중치 (30%)", "responseTimeExplanation": "응답 시간이 1000ms 이하일 경우 만점을 받고, 이를 초과하면 비례적으로 감점됩니다.", "columns": {"rank": "순위", "channelId": "채널 ID", "channelName": "채널 이름", "model": "모델", "totalRequests": "총 요청 수", "successRequests": "성공 요청 수", "failedRequests": "실패 요청 수", "successRate": "성공률", "avgResponseTime": "평균 응답 시간", "score": "종합 점수", "actions": "작업"}, "actions": {"viewDetails": "자세히 보기", "test": "테스트 채널", "edit": "편집 채널"}, "tooltips": {"excellent": "우수하다", "good": "좋음", "average": "일반", "poor": "열악하다", "veryPoor": "매우 나쁨"}, "scoringExplanation100": "채널 점수는 성공률과 응답 시간을 기준으로 계산된 종합 점수로, 만점은 100점입니다."}, "menu": {"channelScores": "채널 점수"}, "relay": {"dispatchOptions": "스케줄링 옵션", "preciseWeightCalculation": "가중치 정확한 계산", "preciseWeightCalculationTip": "활성화하면 더 정밀한 알고리즘을 사용하여 채널 가중치를 계산하게 되며, CPU 오버헤드가 증가할 수 있습니다.", "channelMetricsEnabled": "채널 지표 통계 활성화", "channelMetricsEnabledTip": "활성화하면 채널의 성공률, 응답 시간 등의 지표를 수집하여 채널 성능을 평가하는 데 사용됩니다. 비활성화하면 이러한 데이터를 수집하지 않아 시스템 자원 사용을 줄일 수 있습니다.", "channelScoreRoutingEnabled": "채널 점수 기반 라우팅 활성화", "channelScoreRoutingEnabledTip": "시스템이 활성화되면 채널의 역사적 성과에 따라 요청 할당 우선 순위를 자동으로 조정하며, 성능이 더 좋은 채널은 더 높은 요청 할당 확률을 얻게 됩니다.", "globalIgnoreBillingTypeFilteringEnabled": "전역 무시과금 방식필터링", "globalIgnoreBillingTypeFilteringEnabledTip": "Tip", "globalIgnoreFunctionCallFilteringEnabled": "전역 무시함수 호출필터링", "globalIgnoreFunctionCallFilteringEnabledTip": "활성화하면 무시됩니다함수 호출(Function Call)능력의필터링，더 이상 전문적이지 않음필터링지원함수 호출의채널，감소리소스 사용량。", "globalIgnoreImageSupportFilteringEnabled": "전역 무시이미지 지원필터링", "globalIgnoreImageSupportFilteringEnabledTip": "Tip"}, "dynamicRouter": {"title": "동적 라우팅 관리", "reloadRoutes": "라우트 다시 로드", "exportConfig": "구성 내보내기", "clearConfig": "구성 초기화", "importantNotice": "중요한 알림", "reloadLimitation": "1. 라우트를 다시 로드하면 기존 라우트의 구성만 업데이트할 수 있으며, 라우트를 추가하거나 삭제할 수 없습니다. 라우트 구조를 완전히 다시 로드하려면 애플리케이션을 재시작하십시오.", "exportDescription": "2. 구성 내보내기는 현재 데이터베이스의 구성을 router.json 파일로 내보내며, 빈 값과 0 값을 필터링합니다.", "clearDescription": "3. 구성 초기화는 데이터베이스의 모든 동적 라우트 구성을 삭제하며, 애플리케이션을 재시작하면 router.json 파일에서 다시 로드됩니다.", "routeGroups": "라우팅 그룹", "upstreamConfig": "상류 구성", "endpointConfig": "단말기 구성", "editRouteGroup": "라우팅 그룹 편집", "editUpstream": "상위 구성 편집", "editEndpoint": "편집 엔드포인트 구성", "editJSON": "JSON 편집", "confirmClear": "구성 초기화 확인", "confirmClearMessage": "이 작업은 데이터베이스의 모든 동적 라우팅 구성을 지우며, 다음 번 애플리케이션 재시작 시 구성 파일에서 다시 로드됩니다. 계속 진행하시겠습니까?", "configCleared": "동적 라우트 구성이 초기화되었습니다. 변경 사항을 적용하려면 애플리케이션을 재시작하십시오.", "configExported": "구성이 파일로 성공적으로 내보내졌습니다.", "configReloaded": "라우팅 구성이 성공적으로 다시 로드되었습니다."}, "notification": {"webhookConfig": "Webhook 설정", "telegramConfig": "Telegram 봇 설정", "wxpusherConfig": "WxPusher 설정", "qywxbotConfig": "기업 WeChat 봇 설정", "dingtalkConfig": "DingTalk 봇 설정", "feishuConfig": "<PERSON><PERSON><PERSON> 봇 설정", "title": "알림 설정", "subscriptionEvents": "구독 이벤트", "notificationMethods": "알림 방법", "alertSettings": "경고 설정", "emailConfig": "이메일 설정", "customEmails": "사용자 정의 이메일 주소", "addEmail": "이메일 추가", "removeEmail": "삭제", "emailPlaceholder": "이메일 주소를 입력하세요", "emailTooltip": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "계정 잔액이 이 임계값 아래로 떨어지면 경고 알림을 보냅니다", "balanceThresholdDescription": "Description", "alertExplanationTitle": "경고 설명", "alertExplanation": "Message", "selectEvents": "Message", "eventsDescription": "Description", "selectMethods": "알림 수신 방법 선택", "methodsDescription": "Message", "description": "Description", "recommended": "권장 활성화", "important": "Message", "testRecommendation": "Message", "testNotification": "테스트 알림", "testMessage": "Message", "testSuccess": "테스트 알림 전송 성공", "testFailed": "Message", "saveSuccess": "Message", "saveFailed": "설정 저장 실패", "validation": {"invalidEmail": "Message", "emailRequired": "Message", "invalidUrl": "유효한 URL을 입력하세요", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "WxPusher APP Token을 입력하세요", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "Feishu 봇 Webhook URL을 입력하세요", "webhookUrlRequired": "Message", "telegramTokenRequired": "Telegram Bot Token을 입력하세요", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Telegram Bot Token을 입력하세요"}, "qywxbotGuide": "Message", "wxpusherGuide": "Message", "wxpusherUid": "사용자 UID", "dingtalkGuide": "Message", "feishuGuide": "<PERSON><PERSON><PERSON> 봇 설정 가이드", "webhookGuide": "Message", "webhookUrl": "호출 주소", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "<PERSON><PERSON> (선택사항)", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 또는 @username", "events": {"account_balance_low": "잔액 부족 경고", "account_quota_expiry": "할당량 만료 임박", "security_alert": "보안 경고", "system_announcement": "Message", "promotional_activity": "프로모션 활동 알림", "model_pricing_update": "모델 가격 업데이트", "anti_loss_contact": "연락 두절 방지 - 정기 알림"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "계정 할당량이 만료되기 전에 미리 알림", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "모델 가격 변동 및 청구 규칙 업데이트 알림", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "이메일로 알림 메시지 수신", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "기업 WeChat 봇으로 알림 수신", "dingtalk": "Description", "feishu": "<PERSON><PERSON><PERSON> 봇으로 알림 수신"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook 알림", "wxpusher": "Message", "qywxbot": "기업 WeChat 봇", "dingtalk": "Message", "feishu": "<PERSON><PERSON><PERSON> 봇"}, "configurationSteps": "Message", "detailedDocumentation": "자세한 문서:", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "기업 WeChat 그룹 봇 설정 설명", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "WxPusher 공식 웹사이트를 방문하여 계정 등록", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "DingTalk 사용자 정의 봇 연동", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "봇 이름과 설명 설정", "feishuStep4": "Message", "feishuStep5": "생성된 Webhook URL 복사", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "Message", "telegramStep5": "Message", "telegramStep6": "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates 방문", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 공식 문서", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "주의사항:", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "메시지 형식 지원:", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 보안 향상을 위해 서명 검증 활성화 권장", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "Title", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Chat ID 형식 설명:", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "Message", "telegramChannelPermission": "Message", "webhookCallUrl": "호출 주소", "webhookConfigurationGuide": "Webhook 설정 가이드", "webhookDataFormatExample": "데이터 형식 예시：", "webhookConfigurationInstructions": "설정 설명：", "webhookRequestMethod": "• 요청 방법：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• 인증 방식：Bearer <PERSON>（선택사항, 입력 후 요청 헤더에 Authorization: Bearer {token} 추가）", "webhookTimeout": "• 타임아웃 시간：30초", "webhookRetryMechanism": "• 재시도 메커니즘：실패 후 2회 재시도", "webhookTip": "💡 팁：Webhook 엔드포인트가 POST 요청을 받고 2xx 상태 코드를 반환하는지 확인하세요", "telegramStep3Detailed": "프롬프트에 따라 봇 이름과 사용자 이름 설정（사용자 이름은 bot으로 끝나야 함）", "telegramPersonalChatDetailed": "• 개인 채팅：봇에게 메시지를 보낸 후 액세스", "telegramGroupChatDetailed": "• 그룹 채팅：봇을 그룹에 추가하고 메시지를 보낸 후 동일한 링크에 액세스", "telegramChannelDetailed": "• 채널：봇을 관리자로 추가, Chat ID는 보통 -100으로 시작", "telegramQuickChatIdTitle": "Chat ID 빠르게 얻기 예시：", "telegramQuickStep1": "BOT_TOKEN 교체：https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "브라우저에서 위 링크에 액세스", "telegramQuickStep3": "JSON 응답에서 검색：\"chat\":{\"id\":*********}"}, "legal": {"privacyPolicy": {"title": "개인정보 처리방침", "lastUpdated": "최종 업데이트 날짜：{{date}}", "sections": {"informationCollection": {"title": "정보 수집", "description": "저희는 다음과 같은 유형의 정보를 수집합니다：", "items": {"accountInfo": "계정 정보：Google 로그인을 통해 귀하의 이름, 이메일 주소 및 기본 프로필 정보를 수집합니다", "usageData": "사용 데이터：API 호출 기록, 사용 통계 및 시스템 로그", "technicalInfo": "기술 정보：IP 주소, 브라우저 유형, 장치 정보"}}, "informationUsage": {"title": "정보 사용", "description": "수집된 정보는 다음 용도로 사용됩니다：", "items": ["서비스 제공 및 유지", "사용자 인증 및 계정 관리", "서비스 품질 및 사용자 경험 개선", "중요한 서비스 알림 발송", "사기 및 남용 방지"]}}}, "serviceAgreement": {"title": "서비스 이용약관", "lastUpdated": "최종 업데이트 날짜：{{date}}", "sections": {"serviceDescription": {"title": "서비스 설명", "description": "Shell API Pro Max는 AI API 서비스를 제공하는 플랫폼입니다.", "features": ["여러 AI 모델에 대한 통합 액세스", "사용량 모니터링 및 관리", "유연한 가격 설정 옵션", "개발자용 도구 및 문서"]}}}}, "tasks": {"title": "작업 목록", "taskId": "작업 ID", "platform": "플랫폼", "type": "유형", "status": "상태", "progress": "진행률", "submitTime": "제출 시간", "startTime": "시작 시간", "endTime": "종료 시간", "duration": "지속 시간", "result": "결과", "taskIdPlaceholder": "작업 ID를 입력하세요", "platformPlaceholder": "플랫폼을 선택하세요", "typePlaceholder": "유형을 선택하세요", "statusPlaceholder": "상태를 선택하세요", "videoGeneration": "비디오 생성", "imageGeneration": "이미지 생성", "musicGeneration": "음악 생성", "textGeneration": "텍스트 생성", "unknown": "알 수 없음", "success": "성공", "failed": "실패", "inProgress": "진행 중", "submitted": "제출됨", "queued": "대기 중", "notStarted": "시작되지 않음", "viewResult": "결과 보기", "retry": "다시 시도", "cancel": "취소", "viewError": "오류 보기", "taskDetails": "작업 세부정보", "errorDetails": "오류 세부정보", "loadError": "작업 목록 로드 실패", "refreshSuccess": "작업 상태가 성공적으로 새로 고쳐졌습니다.", "refreshFailed": "작업 상태 새로 고침 실패", "refreshError": "작업 상태를 새로 고치는 중 오류가 발생했습니다.", "viewVideo": "비디오 보기", "videoPreview": "비디오 미리보기", "copyVideoUrl": "비디오 링크 복사", "copiedVideoUrl": "비디오 링크가 복사되었습니다", "downloadVideo": "비디오 다운로드", "videoNotSupported": "지원되지 않는 비디오 형식", "finishTime": "완료 시간", "imageUrl": "이미지 URL", "videoUrl": "비디오 URL", "action": "작업", "model": "모델", "videoUrls": "비디오 URL 목록"}}