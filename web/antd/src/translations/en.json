{"message": {"copyModelSuccess": "The model name has been copied to the clipboard!", "copyFailed": "Copy failed, please copy manually.", "logoutSuccess": "Logout successful.", "loginSuccess": {"default": "Login successful.", "welcomeBack": "Welcome back."}, "removeLocalStorage": {"confirm": "Do you want to clear the local cache?", "success": "Local cache cleared successfully."}, "loadData": {"error": "Failed to load {{name}} data."}, "noNotice": "No announcement content available.", "verification": {"turnstileChecking": "Turnstile is checking the user environment!", "pleaseWait": "Please try again later."}, "clipboard": {"inviteCodeDetected": "The invitation code has been detected and filled in automatically!", "clickToCopy": "Click to copy", "copySuccess": "Copy successful."}}, "common": {"yes": "Yes", "no": "No", "copyAll": "Copy All", "all": "All", "more": "More", "unlimited": "Unlimited", "enabled": "Enabled", "disabled": "Disabled", "save": "Save", "cancel": "Cancel", "create": "Create", "usd": "USD", "day": "{{count}} day", "day_plural": "{{count}} days", "days": "days", "seconds": "seconds", "times": "times", "submit": "Submit", "bind": "Bind", "unknown": "Unknown", "loading": "Loading...", "copyFailed": "Co<PERSON> failed", "people": "people", "ok": "OK", "close": "Close", "copied": "<PERSON>pied", "expand": "Expand", "collapse": "Collapse", "none": "None", "remark": "Remark", "selectPlaceholder": "Please select {{name}}", "on": "On", "off": "Off", "name": "Identifier", "displayName": "Display Name", "description": "Description", "ratio": "Rate", "unnamed": "Unnamed Channel", "groups": "Groups", "captchaPlaceholder": "Please enter verification code", "confirm": "Confirm", "permissions": "Permissions", "actions": "Actions", "createdTime": "Created Time", "expiredTime": "Expired Time", "search": "Search", "reset": "Reset", "refresh": "Refresh", "pagination": {"total": "{{start}} - {{end}} of {{total}} items"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "Click to open the link."}, "userRole": {"normal": "Regular user", "agent": "Agent", "admin": "Administrator", "superAdmin": "Super Administrator", "loading": "Loading..."}, "channelStatus": {"enabled": "Enabled", "disabled": "Disabled", "waitingRestart": "Waiting Restart", "waiting": "Waiting", "autoStoppedTitle": "Channel automatic retry has exceeded maximum attempts or triggered auto-disable condition", "stopped": "Stopped", "partiallyDisabled": "Partially Disabled", "unknown": "Unknown", "reason": "Reason"}, "channelBillingTypes": {"payAsYouGo": "Pay-as-you-go", "payPerRequest": "Pay-per-request", "unknown": "Unknown"}, "tokenStatus": {"normal": "Normal", "disabled": "Disabled", "expired": "Expired", "exhausted": "Exhausted", "unknown": "Unknown"}, "userStatus": {"normal": "Normal", "banned": "Banned", "unknown": "Unknown"}, "redemptionStatus": {"normal": "Normal", "disabled": "Disabled", "redeemed": "Redeemed", "expired": "Expired", "unknown": "Unknown"}, "duration": {"request": "Request", "firstByte": "First Byte", "total": "Total", "seconds": "seconds", "lessThanOneSecond": "<1s"}, "streamType": {"stream": "Stream", "nonStream": "Non-stream"}, "noSet": {"title": "Administrator has not set {{name}}", "name": {"about": "About", "chat": "Cha<PERSON>"}}, "buttonText": {"add": "Add", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "save": "Save", "updateBalance": "Update Balance", "test": "Test", "multiple": "Multiple"}, "channelPage": {"title": "Channel Management"}, "channelStatusCount": {"title": "Channel Status Statistics", "summary": "Enabled {{enabled}} | Disabled {{disabled}} | Retrying {{retry}} | Stopped {{stopped}} | Partially Disabled {{partial}}", "statusEnabled": "Enabled", "statusDisabled": "Disabled", "statusRetry": "Retrying", "statusStopped": "Stopped", "statusPartially": "Partially Disabled"}, "header": {"routes": {"status": "Status", "home": "Home", "chat": "Cha<PERSON>", "pptGen": "PPT Generation", "chart": "Statistics", "agency": "Agency", "channel": "Channels", "ability": "Channel Abilities", "channelGroup": "Channel Groups", "token": "Tokens", "log": "Logs", "logDetail": "Log Details", "midjourney": "Image Generation", "task": "Async Tasks", "user": "Users", "config": "Configuration", "packagePlanAdmin": "Packages", "redemption": "Redemption Codes", "group": "Groups", "query": "Query", "about": "About", "agencyJoin": "Agency Partnership", "setting": {"default": "Settings", "operation": "Operational Settings", "system": "System Settings", "global": "Global Settings", "advance": "Feature Settings", "sensitive": "Sensitive Word Configuration", "verification": "Verification Configuration", "update": "Check for updates."}, "account": {"default": "Account", "profile": "Personal Center", "cardTopup": "Card code redemption", "onlineTopup": "Online recharge", "recharge": "Balance recharge", "balanceTransfer": "Balance transfer", "pricing": "Pricing", "notificationSettings": "Notification Settings", "packagePlan": {"list": "Package purchase", "record": "Purchase Record"}}, "tools": {"default": "Tools", "fileUpload": "File upload", "keyExtraction": "Key extraction", "multiplierCalculator": "Rate Calculator", "shortLink": "Short link generation", "testConnection": "Access test", "customPrompts": "Prompt management", "redis": "Redis Visualization", "ratioCompare": "Rate Comparison", "serverLog": "Server Log Viewer"}, "onlineTopupRecord": "Recharge record", "channelScores": "Channel Score", "dynamicRouter": "Dynamic Routing"}, "dropdownMenu": {"profile": "Personal Center", "recharge": "Balance recharge", "agencyCenter": "Agent Center", "checkin": "Sign in", "darkMode": {"enable": "Dark mode", "disable": "Day mode"}, "fullScreen": {"default": "Switch to full screen.", "enable": "Full screen mode", "disable": "Exit full screen."}, "logout": "Log out"}, "checkin": {"default": "Sign in", "success": "Sign-in successful.", "failed": "Sign-in failed.", "verification": "Please complete the verification."}, "avatarProps": {"login": "<PERSON><PERSON>"}}, "settings": {"public": {"titles": {"default": "Public settings"}, "SystemName": "System Name", "ServerAddress": "Service address", "TopUpLink": "Recharge link", "ChatLink": "Chat Link", "Logo": "System Logo", "HomePageContent": "Home Page Content", "About": "About the content", "Notice": "Announcement content", "Footer": "Footer content", "RegisterInfo": "Registration Notice", "HeaderScript": "Custom Header", "SiteDescription": "Site description", "PrivacyPolicy": "Privacy Policy", "ServiceAgreement": "Service Agreement", "FloatButton": {"FloatButtonEnabled": "Open", "DocumentInfo": "Document Information", "WechatInfo": "WeChat message", "QqInfo": "QQ information"}, "CustomThemeConfig": "Custom theme", "AppList": "Friendship links"}}, "home": {"default": {"title": "Welcome to use.", "subtitle": "Secondary development based on One API to provide more comprehensive features.", "start": "Get started", "description": {"title": "New features:", "part1": "Brand new user interface, convenient and fast.", "part2": "Optimize the scheduling mechanism for efficiency and stability.", "part3": "Developed for enterprises, safe and reliable.", "part4": "More advanced features await your discovery."}}}, "dailyUsageChart": {"title": "Daily Model Usage", "yAxisName": "Usage (USD)", "loadingTip": "Daily Usage", "fetchError": "Error occurred while retrieving daily usage data:"}, "modelUsageChart": {"title": "Model Usage", "hourlyTitle": "Hourly Model Usage", "dailyTitle": "Daily Model Usage", "weeklyTitle": "Weekly Model Usage", "monthlyTitle": "Monthly Model Usage"}, "granularity": {"hour": "per hour", "day": "Every day", "week": "Every week", "month": "Every month", "all": "All"}, "abilitiesTable": {"title": "Channel capability", "export": "Export", "group": "Group", "model": "Model", "channelId": "Channel number", "enabled": "Enabled", "weight": "Weight", "priority": "Priority", "billingType": "Billing type", "functionCallEnabled": "Function call enabled", "imageSupported": "Support for images", "yes": "Yes.", "no": "No.", "perToken": "Billing by token", "perRequest": "Billing on demand", "noDataToExport": "No data available for export.", "exportConfirm": "Are you sure you want to export the data from the current page?", "exportSuccess": "Export successful.", "toggleSuccess": "Switch successful.", "toggleError": "Switching failed.", "selectOrInputGroup": "Select or enter user group."}, "logsTable": {"retry": "Retry", "retryChannelList": "Retry channel list", "retryDurations": "Retry duration details", "channel": "Channel", "duration": "Time-consuming", "startTime": "Start time", "endTime": "End time", "retryCount": "Retry count", "retryDetails": "Retry details", "totalRetryTime": "Total retry time", "seconds": "second", "tokenGroup": "Token Group", "selectGroup": "Select Group", "dailyModelUsageStats": "Data Overview", "time": "Time", "moreInfo": "More information", "ip": "IP", "remoteIp": "Remote IP", "xForwardedFor": "X-Forwarded-For", "xRealIp": "X-Real-IP", "cfConnectingIp": "CloudFlare IP", "primaryIp": "Primary IP", "proxyIp": "Proxy IP", "cdnIp": "CDN IP", "ipTooltip": "IP: {{ip}}  \nRemote IP: {{remoteIp}}  \nX-Forwarded-For: {{xForwardedFor}}  \nX-Real-IP: {{xRealIp}}  \nCloudFlare IP: {{cfConnectingIp}}", "requestId": "Request ID", "username": "Username", "userId": "User ID", "tokenName": "Token Name", "token": "Token", "type": "Type", "typeUnknown": "Unknown", "type充值": "Top-up", "type消费": "Consumption", "type管理": "Management", "type系统": "System", "type邀请": "Invitation", "type提示": "Prompt", "type警告": "Warning", "type错误": "Error", "type签到": "Sign in", "type日志": "Log", "type退款": "Refund", "type邀请奖励金划转": "Invitation reward transfer", "type代理奖励": "Agency rewards", "type下游错误": "Downstream error", "type测试渠道": "Testing channel", "typeRecharge": "Top-up", "typeConsumption": "Consumption", "typeManagement": "Management", "typeSystem": "System", "typeInvitation": "Invitation", "typePrompt": "Prompt", "typeWarning": "Warning", "typeError": "Error", "typeCheckin": "Sign in", "typeLog": "Log", "typeRefund": "Refund", "typeInviteReward": "Transfer of invitation reward funds", "typeAgencyBonus": "Agency rewards", "typeDownstreamError": "Downstream error", "typeChannelTest": "Test channel", "channelId": "Channel ID", "channelName": "Channel Name", "model": "Model", "modelPlaceholder": "Input/Select model name", "info": "Information", "isStream": "Streaming", "isStreamPlaceholder": "Input/Select whether to stream", "prompt": "Prompt", "completion": "Completion", "consumption": "Consumption", "consumptionRange": "Consumption limit range", "description": "Description", "action": "Actions", "details": "Details", "tokenKey": "Token key", "requestDuration": "Request duration", "firstByteDuration": "First byte latency", "totalDuration": "Total time spent", "lessThanOneSecond": "<1 second", "modelInvocation": "Model invocation", "modelUsage": "Model Usage", "totalQuota": "Total consumption limit: {{quota}}", "totalRpm": "Requests per minute: {{rpm}}", "totalTpm": "Tokens per minute: {{tpm}}", "totalMpm": "Amount/minute: {{mpm}}", "dailyEstimate": "Estimated daily consumption: {{estimate}}", "currentStats": "Current RPM: {{rpm}} Current TPM: {{tpm}} Current MPM: ${{mpm}} Estimated Daily Consumption: ${{dailyEstimate}}", "statsTooltip": "Only count the unarchived logs. RPM: requests per minute, TPM: tokens per minute, MPM: money consumed per minute, the estimated daily consumption is inferred from the current MPM.", "showAll": "Show all.", "exportConfirm": "Export the logs of this page?", "export": "Export", "statsData": "Statistical data", "today": "that day", "lastHour": "1 hour", "last3Hours": "3 hours", "lastDay": "1 day", "last3Days": "3 days", "last7Days": "7 days", "lastMonth": "1 month", "last3Months": "3 months", "excludeModels": "Exclusion model", "selectModelsToExclude": "Select the model to exclude.", "excludeErrorCodes": "Exclude error codes.", "excludeErrorCodesPlaceholder": "Select the error codes to exclude.", "errorCode": "Error code", "errorCodePlaceholder": "Input/Select error code", "timezoneTip": "Current timezone: {timezone}", "timezoneNote": "Time zone reminder", "timezoneDescription": "The statistical data is grouped by date according to your current time zone. Different time zones may result in varying time periods for data grouping. To make adjustments, please go to your personal center to modify the time zone settings.", "goToProfile": "Go to personal center.", "realtimeQuota": "Real-time Consumption (1 minute)", "viewTotalQuota": "Check total consumption.", "viewTotalQuotaTip": "Check the total historical consumption amount (the query may take a few seconds).", "loadingTotalQuota": "Querying total consumption amount, please wait...", "totalQuotaTitle": "Historical Total Consumption Statistics", "loadTotalQuotaError": "Failed to retrieve total consumption amount.", "requestLogs": "Request log - {{requestId}}", "noRequestLogs": "No request logs available.", "metricsExplanation": "Only count the unarchived logs. RPM: requests per minute, TPM: tokens per minute, MPM: money consumed per minute, the estimated daily consumption is inferred from the current MPM.", "autoRefresh": "Auto refresh", "autoRefreshTip": "Click to enable/disable auto-refresh. When enabled, data will refresh automatically every specified number of seconds.", "autoRefreshOn": "Auto-refresh has been enabled.", "autoRefreshOff": "Auto-refresh has been disabled.", "refreshInterval": "Refresh interval", "stopRefresh": "Stop refreshing.", "secondsWithValue": "{{seconds}} seconds", "minutesWithValue": "{{minutes}} minutes"}, "mjLogs": {"logId": "Log ID", "submitTime": "Submission time", "type": "Type", "channelId": "Channel ID", "userId": "User ID", "taskId": "Task ID", "submit": "Submit", "status": "Status", "progress": "Progress", "duration": "Time-consuming", "result": "Result", "prompt": "Prompt", "promptEn": "PromptEn", "failReason": "Reasons for failure", "startTime": "Start time", "endTime": "End time", "today": "that day", "lastHour": "1 hour", "last3Hours": "3 hours", "lastDay": "1 day", "last3Days": "3 days", "last7Days": "7 days", "lastMonth": "1 month", "last3Months": "3 months", "selectTaskType": "Select task type.", "selectSubmitStatus": "Select submission status.", "submitSuccess": "Submission successful.", "queueing": "Waiting in line.", "duplicateSubmit": "Duplicate submission", "selectTaskStatus": "Select task status", "success": "Success", "waiting": "Waiting", "queued": "Queueing", "executing": "Execute", "failed": "Failure", "seconds": "second", "unknown": "Unknown", "viewImage": "Click to view", "markdownFormat": "Markdown format", "midjourneyTaskId": "Midjourney Task ID", "copiedAsMarkdown": "Copied in Markdown format.", "copyFailed": "<PERSON><PERSON> failed.", "copiedMidjourneyTaskId": "Midjourney task ID has been copied.", "drawingLogs": "Image Generation Logs", "onlyUnarchived": "Only count the unarchived logs.", "imagePreview": "Image preview", "copiedImageUrl": "Image address has been copied.", "copy": "Copy", "download": "Download", "resultImage": "Result image", "downloadError": "Failed to download the image.", "mode": "Mode", "selectMode": "Select mode", "relax": "Easy mode", "fast": "Quick mode", "turbo": "Turbo mode", "actions": "Actions", "refresh": "Refresh", "tasks": {"title": "Async Tasks", "taskId": "Task ID", "platform": "Platform", "type": "Type", "status": "Status", "progress": "Progress", "submitTime": "Submit Time", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "result": "Result", "taskIdPlaceholder": "Enter task ID", "platformPlaceholder": "Select platform", "typePlaceholder": "Select task type", "statusPlaceholder": "Select status", "videoGeneration": "Video Generation", "imageGeneration": "Image Generation", "musicGeneration": "Music Generation", "textGeneration": "Text Generation", "unknown": "Unknown", "success": "Success", "failed": "Failed", "inProgress": "In Progress", "submitted": "Submitted", "queued": "Queued", "notStarted": "Not Started", "viewResult": "View Result", "viewError": "View Error", "taskDetails": "Task Details", "errorDetails": "<PERSON><PERSON><PERSON>", "loadError": "Failed to load task list", "refreshSuccess": "Task status refreshed successfully.", "refreshFailed": "Task status refresh failed.", "refreshError": "An error occurred while refreshing the task status.", "viewVideo": "View Video", "videoPreview": "Video Preview", "copyVideoUrl": "Copy Video URL", "copiedVideoUrl": "Video URL has been copied", "downloadVideo": "Download Video", "videoNotSupported": "Your browser does not support video playback", "videoUrl": "Video URL", "videoUrls": "Video URLs"}}, "mjTaskType": {"IMAGINE": "Generate image.", "UPSCALE": "Magnify", "VARIATION": "Transformation", "REROLL": "Regenerate", "DESCRIBE": "Image generates text.", "BLEND": "Mixed images", "OUTPAINT": "Zoom", "DEFAULT": "Unknown"}, "mjCode": {"submitSuccess": "Submission successful.", "queueing": "Waiting in line.", "duplicateSubmit": "Duplicate submission", "unknown": "Unknown"}, "mjStatus": {"success": "Success", "waiting": "Waiting", "queued": "Queueing", "executing": "Execute", "failed": "Failure", "unknown": "Unknown"}, "tokensTable": {"title": "Token Management", "table": {"title": "Token Management", "toolBar": {"add": "Create a new token", "delete": "Delete token", "deleteConfirm": "Deleting {{count}} tokens in bulk, this action is irreversible.", "export": "Export", "exportConfirm": "Export the current page token?"}, "action": "Actions"}, "modal": {"title": {"add": "Create a new token.", "edit": "Edit token"}, "field": {"name": "Token Name", "description": "Token description", "type": {"default": "Billing method", "type1": "Pay-per-use", "type2": "Pay-per-use", "type3": "Mixed billing", "type4": "Quantity priority", "type5": "Priority by order of preference."}, "status": "Status", "statusEnabled": "Normal", "statusDisabled": "Disabled", "statusExpired": "Expired", "statusExhausted": "Exhausted", "models": "Available models", "usedQuota": "Consumption quota", "remainQuota": "Remaining balance", "createdTime": "Created Time", "expiredTime": "Expiration Date", "all": "All", "more": "More", "notEnabled": "Not enabled", "unlimited": "Unlimited", "daysLeft": "Expires in {{days}} days.", "expired": "Expired {{days}} days ago.", "userId": "User ID", "key": "API key", "neverExpire": "Never expires."}, "delete": {"title": "Delete", "content": "Are you sure you want to delete the API key {{name}}?"}, "footer": {"cancel": "Cancel", "confirm": "Confirm", "update": "Update"}, "bridge": {"title": "Quick channel integration", "placeholder": "Please enter your {{name}} service address."}, "copy": {"title": "Manual copy"}}, "dropdown": {"onlineChat": "Online conversation", "disableToken": "Disable token", "enableToken": "Enable token", "editToken": "Edit token", "requestExample": "Request example", "tokenLog": "Token log", "shareToken": "Share token", "quickIntegration": "One-click connection"}, "error": {"fetchModelsFailed": "Failed to obtain the model: {{message}}", "batchDeleteFailed": "Batch deletion failed: {{message}}", "deleteTokenFailed": "Failed to delete token: {{message}}", "refreshTokenFailed": "Failed to refresh token: {{message}}", "enableTokenFailed": "Failed to activate token: {{message}}", "disableTokenFailed": "Token revocation failed: {{message}}", "fetchDataFailed": "Failed to retrieve data: {{message}}"}, "success": {"batchDelete": "Successfully deleted {{count}} tokens.", "shareTextCopied": "The shared text has been copied to the clipboard.", "tokenCopied": "The token has been copied to the clipboard.", "deleteToken": "<PERSON><PERSON> deleted successfully.", "refreshToken": "Refresh token successful.", "enableToken": "Token activated successfully.", "disableToken": "<PERSON><PERSON> disabled successfully.", "export": "Successfully exported the current page token."}, "warning": {"copyFailed": "Copy failed, please copy manually.", "invalidServerAddress": "Please enter the correct server address."}, "info": {"openingBridgePage": "Opening the docking page, the token has been copied for you."}, "export": {"name": "Name", "key": "Key", "billingType": "Billing method", "status": "Status", "models": "Available models", "usedQuota": "Consumption quota", "remainQuota": "Remaining balance", "createdTime": "Created Time", "expiredTime": "Expiration Date", "unlimited": "Unlimited", "neverExpire": "Never expires."}, "billingType": {"1": "Pay-per-use", "2": "Pay-per-use", "3": "Mixed billing", "4": "Quantity priority", "5": "Priority by order of preference."}, "bridge": {"quickIntegration": "One-click connection"}}, "editTokenModal": {"editTitle": "Edit token", "createTitle": "Create a token", "defaultTokenName": "{{username}}'s token {{date}}", "tokenName": "Token Name", "unlimitedQuota": "Unlimited quota", "remainingQuota": "Remaining balance", "authorizedQuota": "Authorized limit", "quotaLimitNote": "The maximum available limit of the token is restricted by the account balance.", "quickOptions": "Quick options", "neverExpire": "Never expires.", "expiryTime": "Expiration Date", "billingMode": "Billing model", "selectGroup": "Select Group", "switchGroup": "Select Group", "switchGroupTooltip": "Select the group to which the token belongs; different groups have different pricing and functional permissions. If not selected, the current user's group will be used by default.", "switchGroupHint": "Choosing a group will affect the billing multiplier of the token and the available models. Please select according to your actual needs.", "importantFeature": "Important", "tokenRemark": "<PERSON><PERSON> remark", "discordProxy": "Discord proxy", "enableAdvancedOptions": "Enable advanced options.", "generationAmount": "Generate quantity", "availableModels": "Available models", "selectModels": "Select/Search/Add available models; leaving it blank means no restrictions.", "activateOnFirstUse": "First-time activation", "activateOnFirstUseTooltip": "The activation validity period after the first use will override the token validity period configured above if this option is enabled and activated through the first use.", "activationValidPeriod": "Activation validity period", "activationValidPeriodTooltip": "The validity period of the activation token after the first use (unit: days)", "ipWhitelist": "IP whitelist", "ipWhitelistPlaceholder": "IP address (range), supports IPV4 and IPV6, multiple separated by commas.", "rateLimiter": "Current limiter", "rateLimitPeriod": "Flow restriction period", "rateLimitPeriodTooltip": "Flow limit period (unit: seconds)", "rateLimitCount": "Rate Limit Count", "rateLimitCountTooltip": "Number of available times during the throttling period.", "promptMessage": "Notification message", "promptMessageTooltip": "Alert message when the flow limit is exceeded.", "promotionPosition": "Promotion location", "promotionPositionStart": "Beginning", "promotionPositionEnd": "Ending", "promotionPositionRandom": "Random", "promotionContent": "Promotional content", "currentGroup": "Current group", "searchGroupPlaceholder": "Search for group name, description, or multiplier...", "mjTranslateConfig": "MJ translation configuration", "mjTranslateConfigTip": "Translation configuration effective only for Midjourney prompts.", "mjTranslateBaseUrlPlaceholder": "Please enter the base URL for the translation service.", "mjTranslateApiKeyPlaceholder": "Please enter the API key for the translation service.", "mjTranslateModelPlaceholder": "Please enter the name of the model used for translation services.", "mjTranslateBaseUrlRequired": "A base URL must be provided when enabling translation.", "mjTranslateApiKeyRequired": "An API key must be provided when enabling translation.", "mjTranslateModelRequired": "The model name must be provided when enabling translation."}, "addTokenQuotaModal": {"title": "Token balance management {{username}}", "defaultReason": "Administrator Operation", "enterRechargeAmount": "Please enter the recharge amount.", "enterRemark": "Please enter a remark message.", "confirmOperation": "Confirm operation", "confirmContent": "Are you sure you want to {{action}} {{amount}} dollars for {{username}} {{updateExpiry}}?", "recharge": "Top-up", "deduct": "Deduction", "andUpdateExpiry": "and update the balance validity period to {{days}} days.", "alertMessage": "Entering a negative number can deduct from the user's balance.", "rechargeAmount": "Top-up limit", "operationReason": "Reason for operation", "finalBalance": "Final balance"}, "billingType": {"1": "Pay-per-use", "2": "Pay-per-use", "3": "Mixed billing", "4": "Quantity priority", "5": "Priority by order of preference.", "payAsYouGo": "Pay-per-use", "payPerRequest": "Pay-per-use", "hybrid": "Mixed billing", "payAsYouGoPriority": "Quantity priority", "payPerRequestPriority": "Priority by order of preference.", "unknown": "Unknown method"}, "packagePlanAdmin": {"title": "Packages", "table": {"title": "Package Management", "toolBar": {"add": "Create a new package", "delete": "Delete package"}, "action": {"edit": "Editor", "delete": "Delete", "detail": "Details", "recovery": "On the shelf", "offline": "Remove from shelves."}}, "modal": {"title": {"add": "Create a new package", "edit": "Edit package"}, "field": {"name": "Package Name", "type": {"default": "Meal type", "type1": "Quota package", "type2": "Package with limited sessions", "type3": "Duration package"}, "group": "Package Group", "description": "Package description", "price": "Package price", "valid_period": "Validity period", "first_buy_discount": "First purchase discount", "rate_limit_num": "Limit the number of times", "rate_limit_duration": "Restriction period", "inventory": "Package inventory", "available_models": "Available models", "quota": "Package limit", "times": "Number of packages"}, "footer": {"cancel": "Cancel", "confirm": "Confirm", "update": "Update"}}}, "login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "login": "<PERSON><PERSON>", "otherLoginMethods": "Other login methods", "register": "Register an account", "noAccount": "Don't have an account?", "accountLogin": "Account <PERSON>gin", "phoneLogin": "Phone number login", "usernamePlaceholder": "Username", "usernameRequired": "Please enter your username!", "passwordPlaceholder": "Password", "passwordRequired": "Please enter the password!", "passwordMaxLength": "The password length cannot exceed 20 characters!", "phonePlaceholder": "Mobile phone number", "phoneRequired": "Please enter your phone number!", "phoneFormatError": "The phone number format is incorrect!", "smsCodePlaceholder": "SMS verification code", "smsCodeCountdown": "Reacquire in {{count}} seconds.", "getSmsCode": "Get verification code", "agreementText": "I agree.", "privacyPolicy": "\"Privacy Policy\"", "and": "and", "serviceAgreement": "\"Service Agreement\"", "alreadyLoggedIn": "You are logged in.", "weakPasswordWarning": "Your password is too simple, please change it promptly!", "welcomeMessage": "Welcome to use.", "captchaError": "Verification code is incorrect.", "credentialsError": "Incorrect username or password.", "resetPassword": "Reset password", "captchaExpired": "The verification code does not exist or has expired.", "loginFailed": "<PERSON><PERSON> failed: {{message}}", "captchaRequired": "Please enter the verification code!", "captchaPlaceholder": "Verification code", "smsSent": "SMS verification code sent successfully.", "smsSendFailed": "SMS verification code sending failed.", "agreementWarning": "Please agree to the \"Privacy Policy\" and \"Service Agreement\" first.", "turnstileWarning": "Please try again later, Turnstile is checking the user environment!", "loginSuccess": "Login successful."}, "register": {"title": "Register", "usernameRequired": "Please enter your username!", "usernameNoAt": "The username cannot contain the @ symbol.", "usernameNoChinese": "The username cannot contain Chinese characters.", "usernameLength": "The username length should be 4-12 characters.", "usernamePlaceholder": "Username", "passwordRequired": "Please enter the password!", "passwordLength": "The password length should be 8-20 characters.", "passwordPlaceholder": "Password", "confirmPasswordRequired": "Please confirm the password!", "passwordMismatch": "The two entered passwords do not match!", "confirmPasswordPlaceholder": "Confirm password", "emailInvalid": "Please enter a valid email address!", "emailRequired": "Please enter your email!", "emailPlaceholder": "Email address", "emailCodeRequired": "Please enter the email verification code!", "emailCodePlaceholder": "Email verification code", "enterCaptcha": "Please enter the verification code.", "resendEmailCode": "Resend after {{seconds}} seconds.", "getEmailCode": "Get verification code", "phoneRequired": "Please enter your phone number!", "phoneInvalid": "The phone number format is incorrect!", "phonePlaceholder": "Mobile phone number", "smsCodeRequired": "Please enter the SMS verification code!", "smsCodePlaceholder": "SMS verification code", "resendSmsCode": "Resend after {{seconds}} seconds.", "getSmsCode": "Get verification code", "captchaRequired": "Please enter the verification code!", "captchaPlaceholder": "Verification code", "inviteCodePlaceholder": "Invitation code (optional)", "submit": "Register", "successMessage": "Registration successful.", "failMessage": "Registration failed.", "emailCodeSent": "The email verification code has been sent.", "smsCodeSent": "The SMS verification code has been sent.", "confirm": "Confirm", "emailVerifyTitle": "Email verification", "smsVerifyTitle": "SMS verification", "registerVerifyTitle": "Registration verification", "hasAccount": "Already have an account?", "goLogin": "Sign in"}, "profile": {"timezone": "Time zone", "phoneNumber": "Mobile phone number", "emailAddress": "Email address", "wechatAccount": "WeChat account", "telegramAccount": "Telegram account", "bindTelegram": "Bind Telegram", "balanceValidPeriod": "Balance validity period", "lastLoginIP": "Last login IP", "lastLoginTime": "Last login time", "inviteCode": "Invitation code", "inviteLink": "Invitation link", "generate": "Generate", "pendingEarnings": "Pending income", "transfer": "Transfer", "totalEarnings": "Total revenue", "accountBalance": "Account balance", "totalConsumption": "Cumulative consumption", "callCount": "Number of calls", "invitedUsers": "Invite users", "promotionInfo": "Promotional information", "inviteDescription": "One invitation, lifetime rebates; the more you invite, the more rebates you get.", "userInfo": "User Information", "availableModels": "Available models", "modelNameCopied": "Model name has been copied.", "noAvailableModels": "No available models at the moment.", "accountOptions": "Account Options", "changePassword": "Change password", "systemToken": "System token", "accessTokens": "Access Tokens", "unsubscribe": "Cancelation", "educationCertification": "Education certification", "timezoneUpdateSuccess": "Time zone updated successfully.", "inviteLinkCopied": "The invitation link has been copied.", "inviteLinkCopyFailed": "Failed to copy the invitation link.", "inviteLinkGenerationFailed": "Invitation link generation failed.", "allModelsCopied": "All models have been copied to the clipboard.", "copyAllModels": "Copy all models.", "totalModels": "Number of available models", "expired": "Expired", "validPeriod": "Validity period", "longTermValid": "Long-term effective", "failedToLoadModels": "Failed to load the model list.", "accessTokensManagement": "Access Token Management", "accessTokenDescription": "Access tokens are used for API authentication and have specific permissions related to your account", "tokenNameLabel": "Token Name", "tokenNamePlaceholder": "Give the token a name, such as: read-only token, test tool token, etc.", "presetPermissions": "Preset Permissions", "detailPermissions": "Detailed Permissions", "validityPeriod": "Validity Period (days)", "validityPeriodExtra": "0 means never expires", "remarkLabel": "Remark", "remarkPlaceholder": "Optional, description of the token's purpose, etc.", "createNewToken": "Create New Token", "tokenCreatedSuccess": "Access token created successfully", "tokenSavePrompt": "Please save this token carefully, it will only be displayed once!", "copyToken": "<PERSON><PERSON>", "readPermission": "Read Permission", "writePermission": "Write Permission", "deletePermission": "Delete Permission", "tokenManagement": "Token Management", "channelManagement": "Channel Management", "logView": "Log View", "statisticsView": "Statistics View", "userManagement": "User Management", "quotaManagement": "Quota Management", "readOnlyPermission": "Read Only", "writeOnlyPermission": "Write Only", "readWritePermission": "Read & Write", "standardPermission": "Standard", "fullPermission": "Full Access", "selectPermission": "Please select at least one permission", "tokenStatus": "Status", "tokenEnabled": "Enabled", "tokenDisabled": "Disabled", "enableToken": "Enable", "disableToken": "Disable", "deleteToken": "Delete", "tokenExpiryNever": "Never Expires", "accessTokensInfo": "About Access Tokens", "accessTokensInfoDetail1": "Access tokens are used for API authentication and have specific permissions related to your account", "accessTokensInfoDetail2": "Each token can be configured with different permissions to meet various use cases", "accessTokensInfoDetail3": "For security reasons, tokens are only displayed once when created, please save them carefully", "accessTokensInfoDetail4": "Please disable or delete tokens that are no longer in use", "accessTokensInfoDetail5": "As a super administrator, you can configure all advanced permissions", "noPermission": "No permission to perform this operation", "deleteTokenConfirm": "Are you sure you want to delete the access token \"{{name}}\"?", "disableTokenConfirm": "Are you sure you want to disable the access token \"{{name}}\"?", "enableTokenConfirm": "Are you sure you want to enable the access token \"{{name}}\"?"}, "topup": {"onlineRecharge": "Online recharge", "cardRedemption": "Redemption code verification", "accountBalance": "Account balance", "rechargeReminder": "<PERSON><PERSON><PERSON>", "reminder1": "1. The balance can be used for model calls, package purchases, etc.", "reminder2": "If the amount has not been credited after payment, please contact customer service for assistance.", "reminder3": "3. The balance cannot be withdrawn, but it can be transferred within the same user group.", "reminder4WithTransfer": "4. After the recharge is successful, the account balance validity period will be reset to", "reminder4WithoutTransfer": "3. After the recharge is successful, the account balance validity period will be reset to", "days": "days", "paymentSuccess": "Payment successful.", "paymentError": "Payment error", "paymentAmount": "Payment amount:", "purchaseAmount": "Purchase limit: $", "yuan": "Yuan", "or": "or", "usd": "US dollar", "cny": "Yuan", "enterAmount": "Please enter the recharge amount!", "amountPlaceholder": "Please enter the recharge amount, starting from {{min}} USD.", "amountUpdateError": "Error occurred while updating the amount.", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "The redemption code format is incorrect.", "redeemSuccess": "{{amount}} exchange successful!", "redeemError": "Exchange error, please try again later.", "enterCardKey": "Please enter the redemption code.", "cardKeyPlaceholder": "Please enter the redemption code.", "buyCardKey": "Purchase redemption code card password", "redeem": "Immediate write-off", "record": {"title": "Recharge record", "amount": "Top-up limit", "payment": "Payment amount", "paymentMethod": "Payment method", "orderNo": "Order number", "status": "Status", "createTime": "Created Time", "statusSuccess": "Success", "statusPending": "Processing", "statusFailed": "Failure"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "Administrator", "paymentMethodRedeem": "Redemption code", "alipayF2F": "<PERSON><PERSON><PERSON> Face-to-Face Payment"}, "pricing": {"fetchErrorMessage": "An error occurred while retrieving price information, please contact the administrator.", "availableModelErrorMessage": "An exception occurred while retrieving available models, please contact the administrator.", "modelName": "Model Name", "billingType": "Billing type", "price": "Price", "ratio": "Rate", "promptPriceSame": "Suggested price: Same as the original rate.", "completionPriceSame": "Supplementary price: Same as the original rate.", "promptPrice": "Suggested price: $ {{price}} / 1M tokens", "completionPrice": "Completion price: $ {{price}} / 1M tokens", "promptRatioSame": "Prompt multiplier: Same as the original multiplier.", "completionRatioSame": "Completion multiplier: Same as the original multiplier.", "promptRatio": "Prompt multiplier: {{ratio}}", "completionRatio": "Completion ratio: {{ratio}}", "payAsYouGo": "Pay-as-you-go - Chat", "fixedPrice": "$ {{price}} / time", "payPerRequest": "Pay-per-request - Chat", "dynamicPrice": "$ {{price}} / time", "payPerRequestAPI": "Pay-per-use - API", "loadingTip": "Fetching price information...", "userGroupRatio": "Your user group ratio is: {{ratio}}", "readFailed": "<PERSON> failed.", "billingFormula": "Pay-per-use cost = Conversion rate × Group multiplier × Model multiplier × (Prompt token count + Completion token count × Completion multiplier) / 500,000 (unit: USD)", "billingFormula1": "Conversion rate = (New recharge multiplier / Original recharge multiplier) × (New group multiplier / Original group multiplier)", "generatedBy": "This page is automatically generated by {{systemName}}.", "modalTitle": "Price details", "perMillionTokens": "/1M tokens", "close": "Close", "searchPlaceholder": "Search model name", "viewGroups": "View groups", "copiedToClipboard": "Copied to clipboard.", "copyFailed": "<PERSON><PERSON> failed.", "groupName": "Group Name", "availableGroups": "Available groups for model {{model}}", "noGroupsAvailable": "No available groups.", "modelGroupsErrorMessage": "Failed to retrieve model group data.", "currentGroup": "Current group", "copyModelName": "Copy model name", "groupRatio": "Group ratio", "closeModal": "Close", "groupsForModel": "Available Groups for Model", "actions": "Actions", "filterByGroup": "Filter by group", "groupSwitched": "Switched to group: {{group}}", "showAdjustedPrice": "Display the adjusted price after grouping (Current ratio: {{ratio}})"}, "guestQuery": {"usageTime": "Usage time", "modelName": "Model Name", "promptTooltip": "Input consumes tokens.", "completionTooltip": "Output consumption tokens", "quotaConsumed": "Consumption quota", "pasteConfirm": "Detected a valid token in the clipboard, do you want to paste it?", "queryFailed": "Query failed.", "tokenExpired": "The token has expired.", "tokenExhausted": "The token limit has been exhausted.", "invalidToken": "Please enter the correct token.", "focusRequired": "Please ensure that the page is in focus.", "queryFirst": "Please check first.", "tokenInfoText": "Total quota: {{totalQuota}}  \nToken consumption: {{usedQuota}}  \nToken balance: {{remainQuota}}  \nNumber of calls: {{callCount}}  \nValid until: {{validUntil}}", "unlimited": "Unlimited", "neverExpire": "Never expires.", "infoCopied": "The token information has been copied to the clipboard.", "copyFailed": "<PERSON><PERSON> failed.", "noDataToExport": "No data available for export.", "prompt": "Prompt", "completion": "Completion", "disabled": "Visitor query not enabled", "tokenQuery": "Token query", "tokenPlaceholder": "Please enter the token you want to query (sk-xxx).", "tokenInfo": "Token information", "copyInfo": "Copy information", "totalQuota": "Total token amount", "usedQuota": "Token consumption", "remainQuota": "Token balance", "callCount": "Number of calls", "validUntil": "Valid until", "currentRPM": "Current RPM", "currentTPM": "Current TPM", "callLogs": "Call log", "exportLogs": "Export log"}, "agencyProfile": {"fetchError": "Failed to obtain agent information.", "fetchCommissionError": "Failed to retrieve the commission list.", "systemPreset": "System preset", "lowerRatioWarning": "The rate is lower than the system preset.", "lowerRatioMessage": "The following rates are below the system preset values, please modify them in a timely manner:", "cancelRatioEdit": "Cancel the modification fee.", "updateSuccess": "Update successful.", "updateError": "Failed to update agent information:", "updateFailed": "Update failed:", "customPriceUpdateSuccess": "Custom price update successful.", "customPriceUpdateError": "Custom price update failed:", "time": "Time", "type": "Type", "agencyCommission": "Agent Commission", "unknownType": "Unknown type", "amount": "Amount", "balance": "Balance", "description": "Description", "group": "Group", "customRate": "Custom rate", "systemDefaultRate": "System default rate", "action": "Actions", "save": "Save", "cancel": "Cancel", "edit": "Edit", "agencyConsole": "Agent <PERSON><PERSON><PERSON>", "agencyInfo": "Agent Information", "editInfo": "Edit Information", "agencyName": "Agent Name", "agencyLevel": "Agent Level", "level1": "Level 1", "subordinateUsers": "Subordinate Users", "totalSales": "Total Sales Revenue", "commissionIncome": "Commission Income", "cumulativeEarnings": "Cumulative Earnings", "agencyFunctions": "Agency Functions", "hideSubordinateUsers": "Hide Subordinate Users", "viewSubordinateUsers": "View Subordinate Users", "hideCommissionDetails": "Hide Commission Details", "viewCommissionDetails": "View Commission Details", "hideCustomPrice": "Hide Custom Price", "setCustomPrice": "Set Custom Price", "subordinateUsersList": "Subordinate Users List", "commissionRecords": "Commission Records", "customPriceSettings": "Custom price settings", "saveChanges": "Save changes", "editAgencyInfo": "Edit agent information", "logo": "Logo", "setAgencyLogo": "Set up the agent's logo.", "customHomepage": "Custom Home Page", "aboutContent": "About the content", "newHomepageConfig": "New homepage configuration", "customAnnouncement": "Custom Announcement", "customRechargeGroupRateJson": "Custom recharge group rate JSON", "customRechargeRate": "Custom recharge rate", "viewSystemDefaultRate": "View system default rates.", "rateComparison": "Rate comparison", "comparisonResult": "Comparison results", "higherThanSystem": "Higher than the system", "lowerThanSystem": "Below the system", "equalToSystem": "Equal to the system.", "unknown": "Unknown", "notAnAgentYet": "You are not an agent yet.", "becomeAnAgent": "Become an agent.", "startYourOnlineBusiness": "🌟 Easily start your online business", "becomeOurAgent": "Become our agent and enjoy a stress-free entrepreneurial experience:", "noInventory": "💼 No need to stock inventory, zero pressure on cash flow.", "instantCommission": "💰 Instant sales commission, earn generous returns proportionally", "easyManagement": "🖥️ No website building skills required, easily manage your online store.", "flexibleDomainChoice": "🌐 Flexible domain name selection", "youCan": "You can:", "useOwnDomain": "🏠 Use your own domain name", "orUseOurSubdomain": "🎁 Or we can provide you with a dedicated subdomain.", "convenientStart": "🔥 Whether you're experienced or just starting out, we provide convenient ways to begin.", "actNow": "🚀 Take action now!", "contactAdmin": "Contact the website administrator to start your journey as an agent! 📞", "applyNow": "Apply now", "contactCooperation": "Consultation and cooperation", "understandPolicy": "Understand the agent policy and cooperation details.", "provideDomain": "Provide domain name.", "configDomain": "Provide your domain name, and we will help you configure it.", "promoteAndEarn": "Promotional profits", "startPromoting": "Start promoting your agency site and earn commissions.", "noDeploymentWorries": "No need to worry about complex cloud service deployment, payment channels, or inventory issues.", "easySetup": "Simply provide the domain name and configure it according to the tutorial to easily start your enterprise-level API reseller business.", "customizeContent": "You can customize prices, site information, SEO, logos, and more.", "commissionBenefits": "As an agent, you will receive a share of user recharge, the system automatically deducts costs, and the remaining amount can be withdrawn at any time.", "joinNowBenefit": "Join us now and enjoy the benefits of the AI era together!", "groups": {"student": "college student", "studentDesc": "With ample time, I hope to easily increase my income through promotional activities to cover some living expenses and entertainment costs.", "partTime": "Part-time job or side hustle", "partTimeDesc": "No need for a large time investment; simply promote during your spare time and you can easily earn extra income.", "mediaWorker": "Self-media practitioner", "mediaWorkerDesc": "With a certain fan base, you can easily achieve additional income by simply adding a link at the end of your article or post.", "freelancer": "Freelancer", "freelancerDesc": "Having a lot of flexible time, you can easily increase your extra income just by participating in sales activities."}, "stories": {"story1": {"name": "Mr. <PERSON>", "role": "college student"}, "story2": {"name": "Ms. <PERSON>", "role": "Middle school teacher"}, "story3": {"name": "Mr. <PERSON>", "role": "E-commerce"}, "story4": {"name": "Mr. <PERSON>", "role": "Self-media"}, "story5": {"name": "Mr. <PERSON>", "role": "Research practitioner"}, "story6": {"name": "Ms. <PERSON>", "role": "Xiaohongshu blogger"}, "story7": {"name": "Ms. <PERSON>", "role": "Self-media"}, "story8": {"name": "Mr. <PERSON>", "role": "IT industry"}}, "earnedAmount": "Earned {{amount}}.", "applyForAgentNow": "Apply to become an agent now.", "businessLinesConnected": "More than 40 business lines have been integrated.", "agencyJoin": "Agency franchise", "becomeExclusiveAgent": "Become our exclusive agent.", "startBusinessJourney": "Easily start your business journey~", "welcomeToAgencyPage": "Welcome to our agency page!", "earningsTitle": "Over 100 people have earned 3000+ yuan", "becomeAgentSteps": "Steps to become an agent.", "agencyRules": "Agency rules", "suitableGroups": "Target audience", "agencyImages": {"becomeAgent": "Become an agent.", "agencyBusiness": "Agency business"}, "rules": {"howToEstablishRelation": "How can users establish an agency relationship with me?", "howToEstablishRelationAnswer": "Register on your proxy site to become your user.", "canSetPrice": "Can I set the selling price?", "canSetPriceAnswer": "Sure! However, your selling price must be at least 10% higher than the cost price.", "commissionShare": "How much commission can I earn?", "commissionShareAnswer": {"assumption": "Assumption: Your purchase price is $1 = 1 yuan, your selling price is $1 = 2 yuan, and your commission rate is 90%.", "example": "User purchases $10 on your site, spending 20 yuan", "calculation": "You can earn: (2-1)*10*0.9 = 9 yuan.", "explanation": "Interpretation: (Selling price - Cost price) * Transaction volume * Commission rate"}}}, "error": {"title": "Error", "content": "An error occurred."}, "loading": {"title": "Loading...", "content": "Loading..."}, "notfound": {"title": "404", "content": "Page not found"}, "servererror": {"title": "500", "content": "Server error"}, "unauthorized": {"title": "401", "content": "Unauthorized"}, "forbidden": {"title": "403", "content": "Access denied."}, "networkerror": {"title": "Network error", "content": "Network error"}, "timeout": {"title": "Timeout", "content": "Request timed out."}, "noresult": {"title": "No results.", "content": "No results."}, "nopermission": {"title": "No permission.", "content": "No permission"}, "channelBridge": {"title": "Quick channel integration", "channelPlatform": "Channel platform", "billingMethod": "Billing method", "channelName": "Channel Name", "remark": "Remarks", "availableGroups": "Available Groups", "availableModels": "Available models", "channelKey": "Channel Key", "proxyAddress": "Interface address", "cancel": "Cancel", "submit": "Submit", "gpt35Models": "GPT-3.5 model", "gpt4Models": "GPT-4 model", "clear": "Clear.", "customModelName": "Custom model name", "add": "Add", "moreConfigReminder": "Please save the channel and then edit for more configurations.", "quickIntegration": "One-click connection", "selectBillingMethod": "Please select a billing method.", "enterChannelName": "Please enter the channel name.", "enterChannelRemark": "Please enter the channel remarks.", "selectAvailableGroups": "Please select the groups that can use this channel.", "selectAvailableModels": "Select/search for available models in this channel.", "enterChannelKey": "Please enter the channel key.", "proxyAddressPlaceholder": "This option is optional and is used to make API calls through a proxy site. Please enter the proxy site address.", "includes16kModels": "Includes 16k model.", "excludes32kModels": "Does not include the 32k model.", "cleared": "Cleared.", "addCustomModel": "Add custom model", "clipboardTokenDetected": "Detected a valid token in the clipboard, do you want to paste it?", "channelIntegrationSuccess": "Channel integration successful!", "channelIntegrationFailed": "Channel connection failed:"}, "about": {"loading": "Get the latest content...", "noContent": "The administrator has not set the content for the about page.", "loadFailed": "Failed to load content about..."}, "onlineTopupRecord": {"title": "Recharge record", "columns": {"id": "ID", "username": "User", "amount": "Top-up limit", "money": "Payment amount", "paymentMethod": "Payment method", "tradeNo": "Order number", "status": "Status", "createTime": "Created Time"}, "status": {"success": "Success", "pending": "Processing", "failed": "Failure"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Description information", "downstreamError": "Downstream error", "originalError": "Original error", "requestParams": "Request parameters", "copy": "Copy"}, "viewMode": {"switchTo": "Switch to {{mode}} perspective.", "cost": "Cost", "usage": "Usage量"}, "agenciesTable": {"title": "Agent Management", "addAgency": "New agent", "columns": {"id": "ID", "userId": "User ID", "name": "Name", "domain": "Domain name", "commissionRate": "Commission rate", "salesVolume": "Sales revenue", "userCount": "Number of users", "commissionIncome": "Commission income", "historicalCommission": "Cumulative income", "actions": "Actions"}, "confirm": {"deleteTitle": "Are you sure you want to delete this agent?", "updateName": "Updating the agent name...", "updateSuccess": "Update successful.", "updateFailed": "Update failed.", "deleteSuccess": "Deletion successful!"}, "messages": {"getListFailed": "Failed to retrieve the list of agents: {{message}}", "deleteSuccess": "Deletion successful!", "loadingData": "Loading..."}}, "units": {"times": "times", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} times ({{percent}}%)"}, "notification": {"title": "Notification Settings", "subscriptionEvents": "Subscription Events", "notificationMethods": "Notification Methods", "alertSettings": "<PERSON><PERSON>", "emailConfig": "Email Configuration", "customEmails": "Custom Email Addresses", "addEmail": "Add <PERSON>", "removeEmail": "Remove", "emailPlaceholder": "Enter email address", "emailTooltip": "If not filled, your account's reserved email will be used", "emailDescription": "If you want to send notifications to other email addresses, please configure them here. Leave empty to use your account's reserved email address.", "balanceThreshold": "Balance <PERSON><PERSON>", "balanceThresholdTooltip": "Send alert notification when account balance is below this threshold", "balanceThresholdDescription": "Send alert notification when balance is below this value (real-time check, maximum 1 notification per 2 hours)", "alertExplanationTitle": "Alert Explanation", "alertExplanation": "• Balance Alert: Real-time check of user balance, notify immediately when below threshold\n• Marketing Notifications: Check once daily to avoid excessive disturbance\n• Security Alerts: Notify immediately when they occur to ensure account security\n• System Announcements: One-time notification to all users for important updates", "selectEvents": "Select event types you are interested in", "eventsDescription": "When these events occur, the system will send notifications through your selected methods", "selectMethods": "Select notification methods", "methodsDescription": "You can enable multiple notification methods simultaneously, and the system will send notifications through all enabled methods", "description": "Manage your notification preferences, choose which types of notifications to receive and how to receive them", "recommended": "Recommended", "important": "Important", "testRecommendation": "It is recommended to test after saving settings to ensure notification functionality works properly", "testNotification": "Test Notification", "testMessage": "This is a test notification message", "testSuccess": "Test notification sent successfully", "testFailed": "Failed to send test notification", "saveSuccess": "Notification settings saved successfully", "saveFailed": "Failed to save settings", "validation": {"invalidEmail": "Please enter a valid email address", "emailRequired": "Email address cannot be empty", "invalidUrl": "Please enter a valid URL", "qywxWebhookRequired": "Please enter Enterprise WeChat bot Webhook URL", "wxpusherTokenRequired": "Please enter WxPusher APP Token", "wxpusherUidRequired": "Please enter WxPusher user UID", "dingtalkWebhookRequired": "Please enter DingTalk bot Webhook URL", "feishuWebhookRequired": "Please enter Feishu bot Webhook URL", "webhookUrlRequired": "Please enter Webhook URL", "telegramTokenRequired": "Please enter Telegram Bot Token", "telegramChatIdRequired": "Please enter Telegram Chat ID", "telegramBotTokenRequired": "Please enter Telegram Bot Token"}, "qywxbotConfig": "Enterprise WeChat Bot Configuration", "qywxbotGuide": "Enterprise WeChat Bot Configuration Guide", "wxpusherConfig": "WxPusher Configuration", "wxpusherGuide": "WxPusher Configuration Guide", "wxpusherUid": "User UID", "dingtalkConfig": "DingTalk Bot Configuration", "dingtalkGuide": "DingTalk Bot Configuration Guide", "feishuConfig": "<PERSON><PERSON><PERSON> Configuration", "feishuGuide": "<PERSON><PERSON>u <PERSON> Configuration Guide", "webhookConfig": "Webhook Configuration", "webhookGuide": "Webhook Configuration Guide", "webhookUrl": "Webhook URL", "webhookToken": "API Token (Optional)", "webhookTokenTooltip": "The secret will be added to the request header as Bear<PERSON> token for webhook request verification", "webhookTokenPlaceholder": "Please enter secret", "telegramConfig": "Telegram Bot Configuration", "telegramGuide": "Telegram Bot Configuration Guide", "telegramChatIdPlaceholder": "********* or @username", "events": {"account_balance_low": "Low Balance Alert", "account_quota_expiry": "<PERSON>uo<PERSON><PERSON><PERSON>", "security_alert": "Security Alert", "system_announcement": "System Announcement", "promotional_activity": "Promotional Activity", "model_pricing_update": "Model Pricing Update", "anti_loss_contact": "Anti-loss Contact"}, "eventDescriptions": {"account_balance_low": "Notify you to recharge when account balance is below the threshold", "account_quota_expiry": "Notify you in advance when account quota is about to expire", "security_alert": "Account abnormal login, password modification and other security-related reminders", "system_announcement": "Important system updates, maintenance notifications and feature releases", "promotional_activity": "New promotional activities, discount information and special promotions", "model_pricing_update": "Model price changes and billing rule update notifications", "anti_loss_contact": "Send notifications regularly to ensure contact information is valid"}, "methodDescriptions": {"email": "Receive notification messages via email", "telegram": "Receive notifications via Telegram bot", "webhook": "Receive notifications via Webhook", "wxpusher": "Via WxPusher WeChat push service", "qywxbot": "Receive notifications via Enterprise WeChat bot", "dingtalk": "Receive notifications via DingTalk bot", "feishu": "Receive notifications via <PERSON><PERSON><PERSON> bot"}, "methods": {"email": "Email Notification", "telegram": "Telegram Notification", "webhook": "Webhook Notification", "wxpusher": "WxPusher Notification", "qywxbot": "Enterprise WeChat Bot", "dingtalk": "DingTalk Bot", "feishu": "<PERSON><PERSON><PERSON>"}, "configurationSteps": "Configuration Steps:", "detailedDocumentation": "Detailed Documentation:", "qywxbotConfigurationGuide": "Enterprise WeChat Bot Configuration Guide", "qywxbotStep1": "In Enterprise WeChat group chat, click \"...\" in top right → \"Group Bot\"", "qywxbotStep2": "Click \"Add Bot\" → \"Custom Bot\"", "qywxbotStep3": "Set bot name and avatar, complete creation", "qywxbotStep4": "Copy the generated Webhook URL (format: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx)", "qywxbotStep5": "Fill the Webhook URL into the configuration above", "qywxbotDocumentationLink": "Enterprise WeChat Group Bot Configuration Guide", "wxpusherConfiguration": "WxPusher Configuration", "wxpusherConfigurationGuide": "WxPusher Configuration Guide", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "User UID", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Please enter WxPusher APP Token", "wxpusherUserUIDRequired": "Please enter WxPusher User UID", "wxpusherStep1": "Visit WxPusher Official Website to register an account", "wxpusherStep2": "Create application to get APP Token (format: AT_xxx)", "wxpusherStep3": "Scan QR code with WeChat to follow the application and get User UID (format: UID_xxx)", "wxpusherStep4": "Fill APP Token and User UID into the configuration above", "wxpusherOfficialWebsite": "WxPusher Official Website", "dingtalkConfigurationGuide": "DingTalk Bot Configuration Guide", "dingtalkStep1": "In DingTalk group chat, click \"...\" in top right → \"Group Assistant\" → \"Add Bot\"", "dingtalkStep2": "Select \"Custom\" bot", "dingtalkStep3": "Set bot name, choose security settings (recommend using signature)", "dingtalkStep4": "Copy the generated Webhook URL and signature key", "dingtalkStep5": "Fill Webhook URL and key into the configuration above", "dingtalkSecurityNote": "• Recommend enabling signature verification for better security", "dingtalkPrivacyNote": "• Please keep Webhook URL secure to avoid leakage", "dingtalkDocumentationLink": "DingTalk Custom Bot Integration", "feishuConfigurationGuide": "<PERSON><PERSON>u <PERSON> Configuration Guide", "feishuStep1": "In Feishu group chat, click \"Setting<PERSON>\" in top right → \"Group Bot\"", "feishuStep2": "Click \"Add Bot\" → \"Custom Bot\"", "feishuStep3": "Set bot name and description", "feishuStep4": "Choose security settings (recommend enabling signature verification)", "feishuStep5": "Copy the generated Webhook URL", "feishuStep6": "If signature verification is enabled, also copy the signature key", "feishuStep7": "Fill Webhook URL and signature key into the configuration above", "feishuSecurityNote": "• Recommend enabling signature verification for better security", "feishuPrivacyNote": "• Please keep Webhook URL secure to avoid leakage", "feishuDocumentationLink": "Feishu Custom Bot Integration", "telegramConfigurationGuide": "Telegram Bot Configuration Guide", "telegramStep1": "Search and add @BotFather bot in Telegram", "telegramStep2": "Send /newbot command to create a new bot", "telegramStep3": "Follow prompts to set bot name and username", "telegramStep4": "Copy the obtained Bot Token", "telegramStep5": "Send any message to your bot", "telegramStep6": "Visit https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Find your Chat ID from the returned results", "telegramStep8": "Fill Bot Token and Chat ID into the configuration above", "telegramSecurityNote": "• Please keep <PERSON><PERSON> secure to avoid leakage", "telegramDocumentationLink": "Telegram Bot API Official Documentation", "feishuStep3Detailed": "Set bot name, description and avatar", "feishuStep4Detailed": "Choose security settings (recommend using signature verification or keyword verification):", "feishuSignatureVerification": "• Signature verification: Provides higher security, requires key configuration", "feishuKeywordVerification": "• Keyword verification: Messages must contain specified keywords like \"notification\", \"alert\"", "feishuStep5Detailed": "After creation, copy the generated Webhook URL", "feishuStep6Detailed": "URL format: https://open.feishu.cn/open-apis/bot/v2/hook/xxx", "feishuStep7Detailed": "Fill Webhook URL into the configuration above", "feishuMessageFormatsTitle": "Message Format Support:", "feishuTextMessage": "• Text messages: Support plain text and @ mentions", "feishuRichTextMessage": "• Rich text messages: Support markdown format, links, images, etc.", "feishuCardMessage": "• Message cards: Support interactive card messages", "feishuImageMessage": "• Image messages: Support sending image content", "feishuNoticeTitle": "Notice:", "feishuRateLimit": "• Each bot can send up to 100 messages per minute", "feishuKeywordNote": "• If keyword verification is set, message content must contain the specified keywords", "feishuSecurityRecommendation": "• Recommend enabling signature verification for better security", "telegramStep6Detailed": "Find chat.id field value in returned JSON", "telegramStep7Detailed": "Configure Bot Token and Chat ID in your account settings", "telegramStep8Detailed": "Fill Bot Token and Chat ID into the configuration above", "telegramNoticeTitle": "Important Notes:", "telegramRateLimit": "• Each bot can send up to 30 messages per second", "telegramMessageFormats": "• Support various message formats including text, images, documents", "telegramMarkdownSupport": "• Support Markdown and HTML formatted rich text messages", "telegramInlineKeyboard": "• Support inline keyboards and custom keyboards", "telegramPrivacyNote": "• Keep Bot Token secure to avoid leakage", "telegramSecurityTip": "• Recommend regularly updating Bo<PERSON> for better security", "dingtalkStep3a": "• 推荐选择「自定义关键词」，添加关键词如「通知」、「预警」等", "dingtalkStep3b": "• 或选择「加签」方式（需要额外配置签名）", "dingtalkStep6": "将Webhook URL填入上方配置项", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "• 每个机器人每分钟最多发送20条消息", "dingtalkKeywordNote": "• 如果设置了关键词安全设置，消息内容必须包含设置的关键词", "telegramStep4Detailed": "After creation, BotFather will return <PERSON><PERSON> (format: *********0:ABCDEFGHIJKLMNOPQRSTUVWXYZ)", "telegramStep5Detailed": "Methods to get Chat ID:", "telegramPersonalChat": "• 个人聊天：向机器人发送消息，然后访问", "telegramGroupChat": "• 群组聊天：将机器人添加到群组，发送消息后同样访问上述链接", "telegramChannel": "• 频道：将机器人添加为管理员，Chat ID通常以-100开头", "telegramChatIdFormatsTitle": "Chat ID Format Description:", "telegramPersonalChatId": "• Personal chat: Positive integer, e.g. *********", "telegramGroupChatId": "• Group: Negative integer, e.g. -987654321", "telegramSuperGroupChatId": "• Supergroup/Channel: Starts with -100, e.g. -100*********0", "telegramUsernameFormat": "• Username: Can also use @username format (public groups/channels only)", "telegramInteractionRequired": "• Bot can only send messages to users who have interacted with it", "telegramGroupMembership": "• <PERSON><PERSON> must be added as member in groups", "telegramChannelPermission": "• Bot needs message sending permission in channels", "webhookCallUrl": "Webhook URL", "webhookConfigurationGuide": "Webhook Notification", "webhookDataFormatExample": "Webhook Request Structure", "webhookConfigurationInstructions": "Configuration Instructions", "webhookRequestMethod": "Only supports https, system will send notifications via POST method, please ensure the URL can receive POST requests", "webhookContentType": "The secret will be added to the request header as Bear<PERSON> token for webhook request verification", "webhookAuthMethod": "• Authentication: <PERSON><PERSON> (optional, will add Authorization: <PERSON><PERSON> {token} to request headers)", "webhookTimeout": "• Timeout: 30 seconds", "webhookRetryMechanism": "• Retry Mechanism: Retry 2 times on failure", "webhookTip": "When balance falls below this value, the system will send notifications through selected methods", "webhookUrlPlaceholder": "Please enter Webhook URL, e.g.: https://example.com/webhook", "webhookDescription": "Receive notifications via HTTP requests", "webhookExampleTitle": "Example:", "telegramStep3Detailed": "Follow prompts to set bot name and username (username must end with bot)", "telegramPersonalChatDetailed": "• Personal chat: Send message to bot, then visit", "telegramGroupChatDetailed": "• Group chat: Add bot to group, send message then visit the same link", "telegramChannelDetailed": "• Channel: Add bot as admin, Chat ID usually starts with -100", "telegramQuickChatIdTitle": "Quick Chat ID Example:", "telegramQuickStep1": "Replace BOT_TOKEN: https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "Visit the above link in browser", "telegramQuickStep3": "Look for in JSON response: \"chat\":{\"id\":*********}"}, "dailyUsage": {"total": "Total", "totalCost": "Total cost", "tooltipTitle": {"cost": "Cost situation", "usage": "Usage"}, "yAxisName": {"cost": "Cost (USD)", "usage": "Usage (USD)"}}, "dailyUsageByModel": {"total": "Total", "tooltipTotal": "Total: $ {{value}}", "switchTo": "Switch to", "cost": "Cost", "usage": "Usage量", "perspective": "Perspective", "granularity": {"hour": "By the hour", "day": "By day", "week": "Weekly", "month": "Monthly"}}, "checkinModal": {"title": "Please complete the verification.", "captchaPlaceholder": "Verification code", "confirm": "Confirm", "close": "Close"}, "balanceTransfer": {"title": "Transfer between accounts", "accountInfo": {"balance": "Account balance", "transferFee": "Transfer fee", "groupNote": "Transfers can only be made between the same user groups."}, "form": {"receiverId": "Recipient ID", "receiverUsername": "Recipient username", "remark": "Remarks information", "amount": "Transfer amount", "expectedFee": "Estimated deduction", "submit": "Initiate transfer"}, "result": {"success": "Transfer successful.", "continueTransfer": "Continue the transfer.", "viewRecord": "View records"}, "warning": {"disabled": "The administrator has not enabled the transfer function, and it is temporarily unavailable."}, "placeholder": {"autoCalculate": "Automatically calculate the transfer amount."}}, "channelsTable": {"title": "Channel Management", "columns": {"id": "ID", "name": "Name", "createdTime": "Created Time", "type": "Type", "key": "Key", "base": "Interface address", "models": "Model", "weight": "Weight", "priority": "Priority", "retryInterval": "Retry interval", "responseTime": "Response time", "rpm": "RPM", "status": "Status", "quota": "Balance", "expireTime": "Expiration Date", "group": "Group", "billingType": "Billing type", "actions": "Actions", "fusing": "Circuit breaker", "sort": "Priority", "disableReason": "Disable Reason"}, "status": {"all": "All", "normal": "Normal", "enabled": "Normal state", "manualDisabled": "Manual disablement", "waitingRetry": "Waiting to restart", "suspended": "Suspended use", "specified": "Designated status", "allDisabled": "Disabled", "specifiedDisabled": "Specify the disabled type.", "partiallyDisabled": "Partially Disabled"}, "placeholder": {"selectGroup": "Please select/search for a group.", "selectStatus": "Select channel status", "inputSelectModel": "Input/Select model name", "selectFusingStatus": "Select automatic circuit breaker status."}, "quota": {"usageAmount": "Consumption: {amount}", "remainingAmount": "Remaining: {amount}", "customTotalAmount": "Custom total: {amount}", "updateNotSupported": "Updating the balance is not currently supported, please use a custom balance.", "details": "Details", "sufficient": "sufficient"}, "actions": {"edit": "Editor", "copy": "Clone channel", "delete": "Delete channel", "enable": "Enable", "disable": "Disabled", "test": "Test", "advancedTest": "Advanced Testing", "viewLog": "Channel log", "viewAbility": "View ability", "cleanUsage": "Clear used", "updateBalance": "Update balance", "copyKey": "<PERSON>py the key."}, "confirm": {"deleteTitle": "Delete confirmation", "deleteContent": "Are you sure you want to delete the channel {{name}} (#{{id}})?", "cleanUsageTitle": "Confirm usage clearance", "cleanUsageContent": "Are you sure you want to clear the consumed amount for channel {{name}} (#{{id}})?", "testTitle": "Confirm test", "testContent": "Are you sure you want to test the channel of {{status}}?", "testNote": "Note: This feature needs to be used in conjunction with [Configuration] -> [Relay] -> [Monitoring Settings] -> [Disable channel on failure, enable channel on success]. If the relevant settings are not enabled, the channels will not be automatically disabled or enabled after the test is completed.", "deleteDisabledTitle": "Delete confirmation", "deleteDisabledContent": "Are you sure you want to delete all {{type}} channels?"}, "messages": {"operationSuccess": "Operation successful.", "operationSuccessWithSort": "Operation successful, the channel order may have changed, it is recommended to sort by ID!", "operationFailed": "Operation failed: {{message}}", "testRunning": "Channel {{name}} (#{{id}}) is running a test, please wait...", "testSuccess": "Channel \"{{name}}(#{{id}})\" {{model}} test successful, response time {{time}} seconds.", "testFailed": "Channel \"{{name}}(#{{id}})\" {{model}} test failed. Status code: {{code}}, reason: {{reason}}, click to view details.", "testStarted": "Start testing the {{status}} channel, please refresh later to view the results. The application of the test results depends on your monitoring settings.", "testOperationFailed": "Test failed.", "deleteSuccess": "Successfully deleted {{count}} channels.", "deleteFailed": "Deletion failed: {{message}}", "modelPrefix": "Model {{model}}", "channelInfo": "Channel information", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "The balance update for channel \"{{name}}\" was successful.", "updateBalanceFailed": "Channel \"{{name}}\" balance update failed: {{message}}", "updateAllBalanceStarted": "Start updating the balances of all channels in normal status.", "updateAllBalanceSuccess": "All channel balances updated successfully.", "fetchGroupError": "Error retrieving channel group data: {{response}}", "fetchChannelError": "Failed to retrieve channel data: {{message}}", "selectChannelFirst": "Please select the channel you want to delete first.", "deleteDisabledSuccess": "All {{type}} channels have been deleted, a total of {{count}}.", "deleteOperationFailed": "Deletion failed.", "copySuccess": "Copy successful.", "copyFailed": "Copy failed: {{message}}", "emptyKey": "The key is empty.", "testSuccessWithWarnings": "Test successful with warnings", "viewDetails": "View Details", "fetchChannelDetailError": "Failed to fetch channel details", "topupSuccess": "Top-up successful", "topupFailed": "Top-up failed"}, "popover": {"channelInfo": "Channel information"}, "menu": {"deleteManualDisabled": "Delete manually disabled channels.", "deleteWaitingRetry": "Delete the waiting restart channel.", "deleteSuspended": "Delete suspended channels.", "deleteDisabledAccount": "Delete disabled account channels", "deleteQuotaExceeded": "Delete quota exceeded channels", "deleteRateLimitExceeded": "Delete rate limit exceeded channels", "deleteInvalidKey": "Delete invalid key channels", "deleteConnectionError": "Delete connection error channels", "deleteInternalServerError": "Delete internal server error channels", "testAll": "Test all channels.", "testNormal": "Test normal channel.", "testManualDisabled": "Test manual channel disabling.", "testWaitingRetry": "Test waiting to restart the channel.", "testSuspended": "Test pause using channels."}, "tooltip": {"testNote": "It needs to be used in conjunction with [Configuration] -> [Relay] -> [Monitoring Settings] -> [Disable channel on failure, enable channel on success]. If it is not enabled, the channels will not be automatically disabled or enabled after the speed test is completed."}, "disableReasons": {"account_deactivated": "Account deactivated", "quota_exceeded": "<PERSON><PERSON><PERSON> exceeded", "rate_limit_exceeded": "Rate limit exceeded", "invalid_key": "Invalid key", "connection_error": "Connection error", "internal_server_error": "Internal server error"}, "topup": {"reminder1": "Please note: Top-up is only applicable to channels that support balance queries", "reminder2": "After successful top-up, the channel balance will be updated, but the system balance will not change"}}, "billingTypes": {"quota": "<PERSON><PERSON><PERSON>", "times": "Frequency"}, "serverLogViewer": {"title": "Server Log Viewer", "connecting": "Connecting to the server...", "downloadSelect": "Select log file to download.", "nginxConfig": "Nginx WebSocket Configuration Instructions", "directAccess": "If you access via a domain name and WebSocket support is not configured, the log viewer will not work. At this point, you can access it directly through the server IP and port (for example: http://your-ip:9527).", "domainAccess": "To access via domain name, you need to add the following configuration in the Nginx settings to support WebSocket:", "buttons": {"pause": "Pause", "resume": "Continue", "clear": "Clear."}, "errors": {"fetchFailed": "Failed to retrieve the log file list.", "downloadFailed": "Failed to download the log file.", "wsError": "WebSocket connection error"}}, "channelScore": {"score": "Score", "successRate": "Success rate", "avgResponseTime": "Average response time", "title": "Channel Score", "hourlyTitle": "Channel hourly score", "dailyTitle": "Channel daily score", "weeklyTitle": "Channel weekly score", "monthlyTitle": "Channel monthly score", "allTimeTitle": "Overall channel score", "infoTooltip": "The channel score is a comprehensive rating calculated based on success rate and response time.", "tableView": "Table view", "chartView": "Chart view", "refresh": "Refresh", "selectModel": "Select a model.", "allModels": "All models", "sortByScore": "Sort by score.", "sortBySuccessRate": "Sort by success rate.", "sortByResponseTime": "Sort by response time.", "noData": "No data available.", "totalItems": "A total of {{total}} items.", "fetchError": "Failed to obtain channel score data.", "aboutScoring": "About score calculation", "scoringExplanation": "The channel score is a comprehensive rating calculated based on success rate and response time, with a maximum score of 1 point.", "successRateWeight": "Success Rate Weight (70%)", "successRateExplanation": "The higher the success rate, the higher the score.", "responseTimeWeight": "Response Time Weight (30%)", "responseTimeExplanation": "A full score is awarded for a response time below 1000ms; otherwise, points will be deducted proportionally.", "columns": {"rank": "Ranking", "channelId": "Channel ID", "channelName": "Channel Name", "model": "Model", "totalRequests": "Total number of requests", "successRequests": "Number of successful requests", "failedRequests": "Number of failed requests", "successRate": "Success rate", "avgResponseTime": "Average response time", "score": "Overall score", "actions": "Actions"}, "actions": {"viewDetails": "View details", "test": "Test channel", "edit": "Editing channel"}, "tooltips": {"excellent": "Excellent", "good": "Good", "average": "Generally", "poor": "poor", "veryPoor": "Very bad."}, "scoringExplanation100": "The channel score is a comprehensive rating calculated based on success rate and response time, with a maximum score of 100 points."}, "menu": {"channelScores": "Channel Score"}, "relay": {"dispatchOptions": "Scheduling options", "preciseWeightCalculation": "Precise calculation of weights", "preciseWeightCalculationTip": "Enabling this will use a more precise algorithm to calculate channel weights, which may increase CPU overhead.", "channelMetricsEnabled": "Enable channel metric statistics", "channelMetricsEnabledTip": "When enabled, it will collect metrics such as channel success rate and response time for evaluating channel performance. When disabled, this data will not be collected, which can reduce system resource usage.", "channelScoreRoutingEnabled": "Enable score-based channel routing", "channelScoreRoutingEnabledTip": "When enabled, the system will automatically adjust request allocation priorities based on channel historical performance. Better performing channels will receive higher request allocation probabilities.", "globalIgnoreBillingTypeFilteringEnabled": "Globally ignore billing type filtering", "globalIgnoreBillingTypeFilteringEnabledTip": "When enabled, it ignores the distinction between per-token and per-request billing, reducing CPU and memory usage by not filtering channels by billing type.", "globalIgnoreFunctionCallFilteringEnabled": "Globally ignore function call filtering", "globalIgnoreFunctionCallFilteringEnabledTip": "When enabled, it ignores function call capability filtering, no longer specifically filtering channels that support function calls, reducing resource usage.", "globalIgnoreImageSupportFilteringEnabled": "Globally ignore image support filtering", "globalIgnoreImageSupportFilteringEnabledTip": "When enabled, it ignores image support capability filtering, no longer specifically filtering channels that support image input, reducing resource usage."}, "dynamicRouter": {"title": "Dynamic Route Management", "reloadRoutes": "Reload the route.", "exportConfig": "Export configuration", "clearConfig": "Clear configuration", "importantNotice": "Important Notice", "reloadLimitation": "1. Reloading the routes can only update the configuration of existing routes and cannot add or delete routes. To completely reload the routing structure, please restart the application.", "exportDescription": "2. Exporting the configuration will export the current database configuration to the router.json file, filtering out empty and zero values.", "clearDescription": "3. Clearing the configuration will delete all dynamic route configurations in the database, and after restarting the application, it will reload from the router.json file.", "routeGroups": "Route Groups", "upstreamConfig": "Upstream Configuration", "endpointConfig": "Endpoint Configuration", "editRouteGroup": "Edit Route Group", "editUpstream": "Edit Upstream Configuration", "editEndpoint": "Edit Endpoint Configuration", "editJSON": "Edit JSON", "confirmClear": "Confirm Clear Configuration", "confirmClearMessage": "This operation will clear all dynamic route configurations in the database, and they will be reloaded from the configuration file after the application restarts. Do you want to continue?", "configCleared": "The dynamic routing configuration has been cleared. Please restart the application to apply the changes.", "configExported": "The configuration has been successfully exported to the file.", "configReloaded": "The routing configuration has been successfully reloaded."}, "legal": {"privacyPolicy": {"title": "Privacy Policy", "lastUpdated": "Last updated: {{date}}", "sections": {"informationCollection": {"title": "Information Collection", "description": "We collect the following types of information:", "items": {"accountInfo": "Account Information: When you log in through Google, we collect your name, email address, and basic profile information", "usageData": "Usage Data: API call records, usage statistics, and system logs", "technicalInfo": "Technical Information: IP address, browser type, device information"}}, "informationUsage": {"title": "Information Usage", "description": "We use the collected information for:", "items": ["Providing and maintaining our services", "User authentication and account management", "Improving service quality and user experience", "Sending important service notifications", "Preventing fraud and abuse"]}, "informationSharing": {"title": "Information Sharing", "description": "We do not sell, trade, or transfer your personal information to third parties, except when:", "items": ["We have your explicit consent", "Required by law or court order", "To protect our rights, property, or safety"]}, "dataSecurity": {"title": "Data Security", "description": "We take appropriate security measures to protect your personal information:", "items": ["Data encryption in transmission and storage", "Access control and permission management", "Regular security audits and updates", "Employee privacy training"]}, "dataRetention": {"title": "Data Retention", "description": "We retain your personal information only for necessary periods:", "items": ["Account information: Duration of account existence", "Usage logs: 90 days", "System logs: 30 days"]}, "userRights": {"title": "Your Rights", "description": "You have the right to:", "items": ["Access and update your personal information", "Delete your account and related data", "Withdraw consent", "Data portability"]}, "cookieUsage": {"title": "<PERSON><PERSON>", "description": "We use cookies and similar technologies to:", "items": ["Maintain user sessions", "Remember user preferences", "Analyze website usage"]}, "thirdPartyServices": {"title": "Third-Party Services", "description": "Our service may contain third-party links or integrations:", "items": ["Google OAuth: For user authentication", "GitHub OAuth: For user authentication", "These services have their own privacy policies"]}, "childrenPrivacy": {"title": "Children's Privacy", "description": "Our service is not directed to children under 13. We do not knowingly collect personal information from children."}, "policyUpdates": {"title": "Policy Updates", "description": "We may update this privacy policy. Significant changes will be notified via email or website notification."}, "contactUs": {"title": "Contact Us", "description": "If you have any questions about this privacy policy, please contact us:", "email": "Email", "address": "Address"}}}, "termsOfService": {"title": "Terms of Service", "lastUpdated": "Last updated: {{date}}", "importantNotice": "By using our service, you agree to these terms. Please read carefully.", "sections": {"serviceDescription": {"title": "Service Description", "description": "Shell API Pro Max is an API management and proxy service platform that provides:", "items": ["API key management", "API call proxy and forwarding", "Usage statistics and monitoring", "User account management", "Related technical support services"]}, "userAccount": {"title": "User Account", "description": "Using our service requires:", "items": ["Authentication through Google or GitHub", "Providing accurate and complete registration information", "Protecting account security, not sharing with others", "Timely updating account information", "Being responsible for all activities under the account"]}, "usageRules": {"title": "Usage Rules", "description": "You agree to:", "items": ["Legal use: Only for lawful purposes", "No abuse: No malicious attacks or excessive requests", "No infringement: Not infringing on others' intellectual property", "No harmful content: Not spreading viruses or malware", "Follow restrictions: Comply with API call limits and quotas"]}, "prohibitedBehavior": {"title": "Prohibited Behavior", "description": "The following behaviors are strictly prohibited:", "items": ["Attempting unauthorized access to systems", "Interfering with or disrupting normal service operation", "Reverse engineering or decompiling services", "Creating fake accounts or identities", "Violating any applicable laws and regulations"]}, "serviceAvailability": {"title": "Service Availability", "description": "We strive to provide stable service, but:", "items": ["Do not guarantee 100% service availability", "May suspend service for maintenance or upgrades", "May interrupt service due to force majeure", "Will notify in advance of planned maintenance"]}, "feesAndPayment": {"title": "Fees and Payment", "description": "Regarding service fees:", "items": ["Basic services may be provided for free", "Advanced features may require payment", "Fee standards are published on the website", "Payments are non-refundable (except as required by law)"]}, "intellectualProperty": {"title": "Intellectual Property", "description": "Regarding intellectual property:", "items": ["Services and their content are protected by intellectual property law", "You receive a limited license to use", "May not copy, modify, or distribute our content", "You retain rights to your own data"]}, "privacyProtection": {"title": "Privacy Protection", "description": "We value your privacy:", "items": ["Process your information according to privacy policy", "Take reasonable measures to protect data security", "Will not share your information without consent"]}, "disclaimer": {"title": "Disclaimer", "description": "To the extent permitted by law:", "items": ["Service is provided \"as is\"", "Do not guarantee error-free or uninterrupted service", "Not liable for indirect losses", "Liability is limited to fees you have paid"]}, "serviceTermination": {"title": "Service Termination", "description": "Service may be terminated in the following situations:", "items": ["You violate these terms", "You request account deletion", "We discontinue the service", "Legal requirements"]}, "termsModification": {"title": "Terms Modification", "description": "We may modify these terms:", "items": ["Significant changes will be notified in advance", "Continued use of service indicates acceptance of new terms", "If you disagree, please stop using the service"]}, "disputeResolution": {"title": "Dispute Resolution", "description": "In case of disputes:", "items": ["First attempt friendly negotiation", "Governed by the laws of the People's Republic of China", "Jurisdiction of courts where service provider is located"]}, "contactUs": {"title": "Contact Us", "description": "If you have any questions about these terms, please contact us:", "email": "Email", "address": "Address", "serviceHours": "Service Hours: Weekdays 9:00-18:00"}}}, "common": {"copyright": "© {{year}} Shell API Pro Max. All rights reserved.", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "[Your Company Address]"}}, "tasks": {"title": "Async Tasks", "taskId": "Task ID", "platform": "Platform", "type": "Type", "status": "Status", "progress": "Progress", "submitTime": "Submit Time", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "result": "Result", "taskIdPlaceholder": "Enter task ID", "platformPlaceholder": "Select platform", "typePlaceholder": "Select task type", "statusPlaceholder": "Select status", "videoGeneration": "Video Generation", "imageGeneration": "Image Generation", "musicGeneration": "Music Generation", "textGeneration": "Text Generation", "unknown": "Unknown", "success": "Success", "failed": "Failed", "inProgress": "In Progress", "submitted": "Submitted", "queued": "Queued", "notStarted": "Not Started", "viewResult": "View Result", "viewError": "View Error", "taskDetails": "Task Details", "errorDetails": "<PERSON><PERSON><PERSON>", "loadError": "Failed to load task list", "refreshSuccess": "Task status refreshed successfully.", "refreshFailed": "Task status refresh failed.", "refreshError": "An error occurred while refreshing the task status.", "viewVideo": "View Video", "videoPreview": "Video Preview", "copyVideoUrl": "Copy Video URL", "copiedVideoUrl": "Video URL has been copied", "downloadVideo": "Download Video", "videoNotSupported": "Your browser does not support video playback", "videoUrl": "Video URL", "videoUrls": "Video URLs"}}