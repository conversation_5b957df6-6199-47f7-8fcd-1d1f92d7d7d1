{"message": {"copyModelSuccess": "Nome del modello copiato negli appunti!", "copyFailed": "Copia fallita, si prega di copiare manualmente.", "logoutSuccess": "Disconnessione avvenuta con successo", "loginSuccess": {"default": "<PERSON><PERSON> r<PERSON>", "welcomeBack": "<PERSON>"}, "removeLocalStorage": {"confirm": "Vuoi cancellare la cache locale?", "success": "Cancellazione della cache locale riuscita."}, "loadData": {"error": "Impossibile caricare i dati di {{name}}."}, "noNotice": "Nessun contenuto dell'annuncio disponibile.", "verification": {"turnstileChecking": "Turnstile sta controllando l'ambiente utente!", "pleaseWait": "Per favore riprova più tardi."}, "clipboard": {"inviteCodeDetected": "Codice di invito rilevato, compilato automaticamente!", "clickToCopy": "Clicca per copiare", "copySuccess": "Co<PERSON> riuscita"}}, "common": {"yes": "sì", "no": "No", "copyAll": "<PERSON><PERSON> tutto", "all": "<PERSON><PERSON>", "more": "di più", "unlimited": "senza limiti", "enabled": "<PERSON>i", "disabled": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "usd": "dollaro americano", "day": "{{count}} gior<PERSON>", "day_plural": "{{count}} gior<PERSON>", "days": "cielo", "seconds": "secondo", "times": "Time", "submit": "Invia", "bind": "legare", "unknown": "scon<PERSON><PERSON><PERSON>", "loading": "Caricamento in corso...", "copyFailed": "Copia fallita", "people": "persona", "ok": "Certamente", "close": "<PERSON><PERSON><PERSON>", "copied": "Copiato.", "expand": "Espandere", "collapse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "remark": "<PERSON>a", "selectPlaceholder": "Per favore scegli {{name}}.", "on": "apri", "off": "chiudi", "name": "Identificazione", "displayName": "Nome visualiz<PERSON>", "description": "Descrizione", "ratio": "moltiplicatore", "unnamed": "Canale non denominato", "groups": "Gruppo", "captchaPlaceholder": "Inserisci il codice di verifica.", "confirm": "Conferma", "permissions": "<PERSON><PERSON><PERSON>", "actions": "Azioni", "createdTime": "Ora di creazione", "expiredTime": "Ora di scadenza", "search": "Cerca", "reset": "R<PERSON><PERSON><PERSON>", "refresh": "Aggiorna", "pagination": {"total": "Totale {{total}} elementi"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "Clicca per aprire il link"}, "userRole": {"normal": "Utente normale", "agent": "agente", "admin": "amministratore", "superAdmin": "Super amministratore", "loading": "Caricamento in corso..."}, "channelStatus": {"enabled": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "Disabilitato", "waitingRestart": "Attendere il riavvio", "waiting": "Aspettare", "autoStoppedTitle": "Il tentativo automatico del canale ha superato il numero massimo di tentativi o ha attivato le condizioni di disattivazione automatica.", "stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partiallyDisabled": "Parzialmente disabilitato", "unknown": "scon<PERSON><PERSON><PERSON>", "reason": "Motivo"}, "channelBillingTypes": {"payAsYouGo": "Fatturazione basata sul consumo", "payPerRequest": "Pagamento a consumo", "unknown": "modo sconosciuto"}, "tokenStatus": {"normal": "normale", "disabled": "Disabilitare", "expired": "scaduto", "exhausted": "esau<PERSON>re", "unknown": "scon<PERSON><PERSON><PERSON>"}, "userStatus": {"normal": "normale", "banned": "bando", "unknown": "scon<PERSON><PERSON><PERSON>"}, "redemptionStatus": {"normal": "normale", "disabled": "Disabilitato", "redeemed": "G<PERSON>à riscattato", "expired": "scaduto", "unknown": "scon<PERSON><PERSON><PERSON>"}, "duration": {"request": "richiesta", "firstByte": "primo byte", "total": "Totale", "seconds": "secondo", "lessThanOneSecond": "<1 secondo"}, "streamType": {"stream": "flusso", "nonStream": "Non-streaming"}, "noSet": {"title": "L'amministratore non ha impostato {{name}}.", "name": {"about": "riguardo a", "chat": "Dialogo"}}, "buttonText": {"add": "Nuovo", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "delete": "Elimina", "edit": "Editore", "save": "<PERSON><PERSON>", "updateBalance": "Aggiornare il saldo", "test": "testare", "multiple": "Scelta multipla"}, "channelPage": {"title": "Gestione dei canali"}, "channelStatusCount": {"title": "Statistiche sullo stato dei canali", "summary": "Abilitato {{enabled}} | Disabilitato {{disabled}} | R<PERSON>rova in corso {{retry}} | Arrestato {{stopped}}", "statusEnabled": "<PERSON><PERSON><PERSON><PERSON>", "statusDisabled": "Già disabilitato", "statusRetry": "R<PERSON>rova.", "statusStopped": "Interrotto", "statusPartially": "Parzialmente disabilitato"}, "header": {"routes": {"status": "stato", "home": "Home", "chat": "Dialogo", "pptGen": "Generazione di PPT", "chart": "statistica", "agency": "agente", "channel": "canale", "ability": "Capacità del canale", "channelGroup": "Gruppo canali", "token": "token", "log": "registro", "logDetail": "<PERSON><PERSON><PERSON>", "midjourney": "disegno", "user": "utente", "config": "Configurazione", "packagePlanAdmin": "<PERSON><PERSON><PERSON>", "redemption": "Codice di riscatto", "group": "Gruppo", "query": "<PERSON><PERSON>", "about": "riguardo a", "setting": {"default": "Impostazioni", "operation": "Impostazioni operative", "system": "Impostazioni di sistema", "global": "Impostazioni globali", "advance": "Impostazioni delle caratteristiche", "sensitive": "Configurazione delle parole sensibili", "verification": "Configurazione del codice di verifica", "update": "Controlla aggiornamenti"}, "account": {"default": "account", "profile": "Centro personale", "cardTopup": "Riscatto del codice della carta", "onlineTopup": "Ricarica online", "recharge": "Ricarica del saldo", "balanceTransfer": "Trasferimento del saldo", "pricing": "Descrizione dei costi", "packagePlan": {"list": "Acquisto del pacchetto", "record": "Registro degli acquisti"}, "notificationSettings": "Impostazioni notifiche"}, "tools": {"default": "Strumenti", "fileUpload": "Caricamento file", "keyExtraction": "Estrazione della chiave", "multiplierCalculator": "Calcolatore di moltiplicazione", "shortLink": "Generazione di link brevi", "testConnection": "Test di accesso", "customPrompts": "Gestione delle parole chiave", "redis": "Visualizzazione di Redis", "ratioCompare": "Confronto dei rapporti di amplificazione", "serverLog": "Visualizzatore dei log del server"}, "onlineTopupRecord": "Registro di ricarica", "channelScores": "Punteggio del canale", "dynamicRouter": "Routing dinamico", "task": "Attività asincrone", "agencyJoin": "Partnership agenzia"}, "dropdownMenu": {"profile": "Centro personale", "recharge": "Ricarica del saldo", "agencyCenter": "Centro agenti", "checkin": "Registrazione", "darkMode": {"enable": "Modalità scura", "disable": "Modalità diurna"}, "fullScreen": {"default": "Attiva la modalità a schermo intero", "enable": "Modalità a schermo intero", "disable": "Esci dalla modalità a schermo intero"}, "logout": "<PERSON>nne<PERSON><PERSON>"}, "checkin": {"default": "Registrazione", "success": "Registrazione avvenuta con successo", "failed": "Registrazione fallita", "verification": "Si prega di completare la verifica."}, "avatarProps": {"login": "Accesso"}}, "settings": {"public": {"titles": {"default": "Impostazioni pubbliche"}, "SystemName": "Nome del sistema", "ServerAddress": "Indirizzo di servizio", "TopUpLink": "Link di ricarica", "ChatLink": "Link di dialogo", "Logo": "Logo del sistema", "HomePageContent": "Contenuto della home page", "About": "<PERSON><PERSON><PERSON><PERSON> al contenuto", "Notice": "Contenuto dell'annuncio", "Footer": "Contenuto del piè di pagina", "RegisterInfo": "Notifica di registrazione", "HeaderScript": "Intestazione personalizzata", "SiteDescription": "Descrizione del sito", "PrivacyPolicy": "Informativa sulla privacy", "ServiceAgreement": "Con<PERSON>tto di servizio", "FloatButton": {"FloatButtonEnabled": "<PERSON>i", "DocumentInfo": "Informazioni sul documento", "WechatInfo": "Messaggio WeChat", "QqInfo": "Informazioni QQ"}, "CustomThemeConfig": "<PERSON><PERSON>", "AppList": "Link di amicizia"}}, "home": {"default": {"title": "Benvenuto!", "subtitle": "Sviluppo secondario basato su One API, per offrire funzionalità più complete.", "start": "Inizia a usare", "description": {"title": "Nuove funzionalità:", "part1": "Interfaccia utente completamente nuova, comoda e veloce.", "part2": "Ottimizzare il meccanismo di programmazione, efficiente e stabile.", "part3": "Sviluppato per le imprese, sicuro e affidabile.", "part4": "Più funzionalità avanzate ti aspettano per essere scoperte."}}}, "dailyUsageChart": {"title": "<PERSON><PERSON><PERSON><PERSON>ior<PERSON> modello", "yAxisName": "<PERSON><PERSON><PERSON><PERSON> (USD)", "loadingTip": "<PERSON><PERSON><PERSON><PERSON> quoti<PERSON>o", "fetchError": "Errore durante il recupero dei dati di utilizzo giornal<PERSON>o:"}, "modelUsageChart": {"title": "U<PERSON><PERSON><PERSON> del modello", "hourlyTitle": "<PERSON><PERSON><PERSON><PERSON> del modello ogni ora", "dailyTitle": "<PERSON><PERSON><PERSON><PERSON> del modello quotidiano", "weeklyTitle": "<PERSON><PERSON><PERSON><PERSON> del modello settimanale", "monthlyTitle": "<PERSON><PERSON><PERSON><PERSON> del modello mensile"}, "granularity": {"hour": "ogni ora", "day": "<PERSON><PERSON> giorno", "week": "<PERSON><PERSON> set<PERSON>", "month": "ogni mese", "all": "<PERSON><PERSON>"}, "abilitiesTable": {"title": "Capacità del canale", "export": "Esportare", "group": "Gruppo", "model": "modello", "channelId": "Numero del canale", "enabled": "<PERSON><PERSON><PERSON><PERSON>", "weight": "peso", "priority": "Priorità", "billingType": "Tipo di fatturazione", "functionCallEnabled": "Attivazione della chiamata di funzione", "imageSupported": "Supporto per le immagini", "yes": "sì", "no": "No", "perToken": "Fatturazione per token", "perRequest": "Fatturazione su richiesta", "noDataToExport": "<PERSON><PERSON><PERSON> dato può essere esportato.", "exportConfirm": "Sei sicuro di voler esportare i dati della pagina corrente?", "exportSuccess": "Esportazione riuscita", "toggleSuccess": "Cambio avvenuto con successo.", "toggleError": "Cambio fallito", "selectOrInputGroup": "Seleziona o inserisci il gruppo di utenti"}, "logsTable": {"retry": "<PERSON><PERSON><PERSON><PERSON>", "retryChannelList": "Elenco dei canali di ripetizione", "retryDurations": "Dettagli sul tempo di ripetizione", "channel": "canale", "duration": "tempo impiegato", "startTime": "Orario di inizio", "endTime": "Tempo di conclusione", "retryCount": "Numero di tentativi", "retryDetails": "Dettagli del ripristino", "totalRetryTime": "Tempo totale di ripetizione", "seconds": "secondo", "tokenGroup": "Gruppo di token", "selectGroup": "Seleziona il gruppo", "dailyModelUsageStats": "Panoramica delle chiamate ai dati", "time": "Tempo", "moreInfo": "Ulteriori informazioni", "ip": "IP", "remoteIp": "IP remoto", "ipTooltip": "IP: {{ip}}  \nIP remoto: {{remoteIp}}", "requestId": "ID della richiesta", "username": "Nome utente", "userId": "ID utente", "tokenName": "Nome del token", "token": "token", "type": "Tipo", "typeUnknown": "scon<PERSON><PERSON><PERSON>", "type充值": "Ricarica", "type消费": "consumo", "type管理": "gestione", "type系统": "sistema", "type邀请": "Invito", "type提示": "Suggerimento", "type警告": "Avviso", "type错误": "errore", "type签到": "Registrazione", "type日志": "registro", "type退款": "<PERSON><PERSON><PERSON>", "type邀请奖励金划转": "Trasferimento del premio di invito", "type代理奖励": "Premio per il rappresentante", "type下游错误": "Errore a valle", "type测试渠道": "Canale di test", "typeRecharge": "Ricarica", "typeConsumption": "consumo", "typeManagement": "gestione", "typeSystem": "sistema", "typeInvitation": "Invito", "typePrompt": "Suggerimento", "typeWarning": "Avviso", "typeError": "errore", "typeCheckin": "Registrazione", "typeLog": "registro", "typeRefund": "<PERSON><PERSON><PERSON>", "typeInviteReward": "Trasferimento del premio di invito", "typeAgencyBonus": "Premio per il rappresentante", "typeDownstreamError": "Errore a valle", "typeChannelTest": "Canale di test", "channelId": "ID canale", "channelName": "Nome del canale", "model": "modello", "modelPlaceholder": "Inserisci/seleziona il nome del modello", "info": "Informazione", "isStream": "flusso", "isStreamPlaceholder": "Inserisci/seleziona se in streaming", "prompt": "Suggerimento", "completion": "completare", "consumption": "consumo", "consumptionRange": "Intervallo di spesa", "description": "Spiegazione", "action": "Operazione", "details": "<PERSON><PERSON><PERSON>", "tokenKey": "<PERSON>ave del <PERSON>", "requestDuration": "Tempo di richiesta", "firstByteDuration": "Tempo di attesa del primo byte", "totalDuration": "Tempo totale di utilizzo", "lessThanOneSecond": "<1 secondo", "modelInvocation": "Chiamata del modello", "modelUsage": "U<PERSON><PERSON><PERSON> del modello", "totalQuota": "Limite di spesa totale: {{quota}}", "totalRpm": "<PERSON><PERSON> al minuto: {{rpm}}", "totalTpm": "Token al minuto: {{tpm}}", "totalMpm": "Importo/minuto: {{mpm}}", "dailyEstimate": "Consu<PERSON> giornal<PERSON>o previsto: {{estimate}}", "currentStats": "RPM attuale: {{rpm}} TPM attuale: {{tpm}} MPM attuale: ${{mpm}} Stima consumo giornaliero: ${{dailyEstimate}}", "statsTooltip": "Solo statistiche sui log non archiviati, RPM: numero di richieste al minuto, TPM: numero di token al minuto, MPM: denaro speso al minuto, il consumo giornaliero stimato è dedotto dall'attuale MPM.", "showAll": "<PERSON><PERSON> tutto", "exportConfirm": "Esporta il registro di questa pagina?", "export": "Esportare", "statsData": "Dati statistici", "today": "quel giorno", "lastHour": "1 ora", "last3Hours": "3 ore", "lastDay": "1 giorno", "last3Days": "3 giorni", "last7Days": "7 giorni", "lastMonth": "1 mese", "last3Months": "3 mesi", "excludeModels": "<PERSON>lo di esclusione", "selectModelsToExclude": "Seleziona i modelli da escludere.", "excludeErrorCodes": "Escludere il codice di errore", "excludeErrorCodesPlaceholder": "Seleziona i codici di errore da escludere.", "errorCode": "Codice di errore", "errorCodePlaceholder": "Inserisci/seleziona il codice di errore", "timezoneTip": "Fuso orario attuale: {timezone}", "timezoneNote": "Suggerimento di fuso orario", "timezoneDescription": "I dati statistici sono raggruppati per data in base al tuo fuso orario attuale. Fusi orari diversi possono portare a periodi di raggruppamento dei dati differenti. Se desideri effettuare modifiche, visita il tuo centro personale per modificare le impostazioni del fuso orario.", "goToProfile": "Vai al centro personale", "realtimeQuota": "<PERSON><PERSON><PERSON> in tempo reale (1 minuto)", "viewTotalQuota": "Controlla il consumo totale", "viewTotalQuotaTip": "Visualizza l'importo totale della spesa storica (la ricerca potrebbe richiedere alcuni secondi).", "loadingTotalQuota": "Sto verificando l'importo totale dei consumi, per favore attendere...", "totalQuotaTitle": "Statistiche storiche sui consumi totali", "loadTotalQuotaError": "Impossibile ottenere l'importo totale dei consumi.", "requestLogs": "Registro delle richieste - {{requestId}}", "noRequestLogs": "Nessun registro di richiesta disponibile.", "metricsExplanation": "Solo statistiche sui log non archiviati, RPM: numero di richieste al minuto, TPM: numero di token al minuto, MPM: denaro speso al minuto, il consumo giornaliero stimato è dedotto dall'attuale MPM.", "autoRefresh": "Aggiornamento automatico", "autoRefreshTip": "Clicca per attivare/disattivare l'aggiornamento automatico; una volta attivato, i dati verranno aggiornati automaticamente ogni pochi secondi.", "autoRefreshOn": "Aggiornamento automatico attivato", "autoRefreshOff": "Aggiornamento automatico disattivato.", "refreshInterval": "Intervallo di aggiornamento", "stopRefresh": "Ferma il refresh", "secondsWithValue": "{{seconds}} secondi", "minutesWithValue": "{{minutes}} minuti"}, "mjLogs": {"logId": "ID del registro", "submitTime": "Data di invio", "type": "Tipo", "channelId": "ID canale", "userId": "ID utente", "taskId": "ID attività", "submit": "Invia", "status": "stato", "progress": "Progresso", "duration": "tempo impiegato", "result": "risultato", "prompt": "Invito", "promptEn": "<PERSON> dispiace, ma non posso fornire una traduzione senza un testo specifico da tradurre. <PERSON> favore, fornis<PERSON><PERSON> il testo in cinese che desideri tradurre in italiano.", "failReason": "Motivo del fallimento", "startTime": "Orario di inizio", "endTime": "Tempo di conclusione", "today": "quel giorno", "lastHour": "1 ora", "last3Hours": "3 ore", "lastDay": "1 giorno", "last3Days": "3 giorni", "last7Days": "7 giorni", "lastMonth": "1 mese", "last3Months": "3 mesi", "selectTaskType": "Seleziona il tipo di compito", "selectSubmitStatus": "Seleziona la situazione di invio", "submitSuccess": "<PERSON><PERSON>", "queueing": "In coda.", "duplicateSubmit": "Invio ripetuto", "selectTaskStatus": "Seleziona lo stato del compito", "success": "successo", "waiting": "Aspettare", "queued": "Fare la fila", "executing": "Esecuzione", "failed": "fallimento", "seconds": "secondo", "unknown": "scon<PERSON><PERSON><PERSON>", "viewImage": "<PERSON>lic<PERSON> per vedere", "markdownFormat": "Formato Markdown", "midjourneyTaskId": "ID del compito Midjourney", "copiedAsMarkdown": "Copiato nel formato Markdown", "copyFailed": "Copia fallita", "copiedMidjourneyTaskId": "ID del compito Midjourney copiato.", "drawingLogs": "Diario di disegno", "onlyUnarchived": "Statistiche solo sui log non archiviati.", "imagePreview": "Anteprima dell'immagine", "copiedImageUrl": "Indirizzo dell'immagine copiato.", "copy": "<PERSON><PERSON><PERSON>", "download": "Scarica", "resultImage": "Immagine dei risultati", "downloadError": "Download dell'immagine fallito.", "mode": "modello", "selectMode": "Seleziona modalità", "relax": "Modalità facile", "fast": "Modalità rapida", "turbo": "Modalità veloce", "actions": "Operazione", "refresh": "Aggiornare"}, "mjTaskType": {"IMAGINE": "<PERSON>rare immagini", "UPSCALE": "Ingrandire", "VARIATION": "cambiamento", "REROLL": "R<PERSON>ner<PERSON>", "DESCRIBE": "Immagine genera testo", "BLEND": "immagine mista", "OUTPAINT": "zoom", "DEFAULT": "scon<PERSON><PERSON><PERSON>"}, "mjCode": {"submitSuccess": "<PERSON><PERSON>", "queueing": "In coda.", "duplicateSubmit": "Invio ripetuto", "unknown": "scon<PERSON><PERSON><PERSON>"}, "mjStatus": {"success": "successo", "waiting": "Aspettare", "queued": "Fare la fila", "executing": "Esecuzione", "failed": "fallimento", "unknown": "scon<PERSON><PERSON><PERSON>"}, "tokensTable": {"title": "Gestione dei token", "table": {"title": "Gestione dei token", "toolBar": {"add": "Crea un nuovo token", "delete": "Elimina token", "deleteConfirm": "Stai eliminando in massa {{count}} token, questa operazione è irreversibile.", "export": "Esportare", "exportConfirm": "Esporta il token della pagina corrente?"}, "action": "Operazione"}, "modal": {"title": {"add": "Crea un nuovo token", "edit": "Token di modifica"}, "field": {"name": "Nome del token", "description": "Descrizione del token", "type": {"default": "Modalità di fatturazione", "type1": "Fatturazione basata sul consumo", "type2": "Pagamento a consumo", "type3": "Fatturazione mista", "type4": "Priorità in base alla quantità", "type5": "Secondo le priorità."}, "status": "stato", "statusEnabled": "normale", "statusDisabled": "Disabilitare", "statusExpired": "scaduto", "statusExhausted": "esau<PERSON>re", "models": "Modelli disponibili", "usedQuota": "Quota di consumo", "remainQuota": "<PERSON><PERSON>", "createdTime": "Data di creazione", "expiredTime": "data di scadenza", "all": "<PERSON><PERSON>", "more": "di più", "notEnabled": "Non attivato", "unlimited": "senza limiti", "daysLeft": "Scadenza tra {{days}} gior<PERSON>", "expired": "<PERSON><PERSON><PERSON> da {{days}} gior<PERSON>", "userId": "ID utente", "key": "Chiave API", "neverExpire": "mai scadere"}, "delete": {"title": "Elimina", "content": "Sei sicuro di voler eliminare la chiave API {{name}}?"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "update": "Aggiornamento"}, "bridge": {"title": "Integrazione rapida dei canali", "placeholder": "Inser<PERSON>ci il tuo indirizzo di servizio {{name}}."}, "copy": {"title": "Copia manuale"}}, "dropdown": {"onlineChat": "Conversazione online", "disableToken": "Disabilitare il token", "enableToken": "Abilita il token", "editToken": "Token di modifica", "requestExample": "Esempio di richiesta", "tokenLog": "Registro dei token", "shareToken": "Token di condivisione", "quickIntegration": "Collegamento con un clic"}, "error": {"fetchModelsFailed": "Impossibile ottenere il modello: {{message}}", "batchDeleteFailed": "Eliminazione in massa non riuscita: {{message}}", "deleteTokenFailed": "Eliminazione del token fallita: {{message}}", "refreshTokenFailed": "Errore nel rinnovo del token: {{message}}", "enableTokenFailed": "Attivazione del token fallita: {{message}}", "disableTokenFailed": "Disabilitazione del token non riuscita: {{message}}", "fetchDataFailed": "Impossibile ottenere i dati: {{message}}"}, "success": {"batchDelete": "Successo nella cancellazione di {{count}} token.", "shareTextCopied": "Il testo condiviso è stato copiato negli appunti.", "tokenCopied": "Il token è stato copiato negli appunti.", "deleteToken": "Token eliminato con successo", "refreshToken": "Token di aggiornamento riuscito", "enableToken": "Attivazione del token riuscita", "disableToken": "Disabilitazione del token riuscita", "export": "Esportazione del token della pagina corrente riuscita."}, "warning": {"copyFailed": "Copia fallita, si prega di copiare manualmente.", "invalidServerAddress": "Inserisci l'indirizzo del server corretto."}, "info": {"openingBridgePage": "Stiamo aprendo la pagina di integrazione, il token è stato copiato per te."}, "export": {"name": "Nome", "key": "chiave", "billingType": "Modalità di fatturazione", "status": "stato", "models": "Modelli disponibili", "usedQuota": "Quota di consumo", "remainQuota": "<PERSON><PERSON>", "createdTime": "Data di creazione", "expiredTime": "data di scadenza", "unlimited": "senza limiti", "neverExpire": "mai scadere"}, "billingType": {"1": "Fatturazione basata sul consumo", "2": "Pagamento a consumo", "3": "Fatturazione mista", "4": "Priorità in base alla quantità", "5": "Secondo le priorità."}, "bridge": {"quickIntegration": "Collegamento con un clic"}}, "editTokenModal": {"editTitle": "Token di modifica", "createTitle": "Creare un token", "defaultTokenName": "Il token di {{username}} {{date}}", "tokenName": "Nome del token", "unlimitedQuota": "limite illimitato", "remainingQuota": "<PERSON><PERSON>", "authorizedQuota": "Limite di autorizzazione", "quotaLimitNote": "L'importo massimo disponibile del token è limitato dal saldo dell'account.", "quickOptions": "Opzioni rapide", "neverExpire": "mai scadere", "expiryTime": "data di scadenza", "billingMode": "Modalità di fatturazione", "selectGroup": "Seleziona il gruppo", "switchGroup": "Seleziona il gruppo", "switchGroupTooltip": "Seleziona il gruppo a cui appartiene il token; gruppi diversi hanno prezzi e autorizzazioni funzionali diversi. Se non selezioni, verrà utilizzato il gruppo attuale dell'utente.", "switchGroupHint": "La scelta del gruppo influenzerà il moltiplicatore di addebito dei token e i modelli disponibili, si prega di scegliere in base alle esigenze reali.", "importantFeature": "Importante", "tokenRemark": "Nota del <PERSON>", "discordProxy": "Proxy di Discord", "enableAdvancedOptions": "Abilita opzioni avanzate", "generationAmount": "Quantità da generare", "availableModels": "Modelli disponibili", "selectModels": "Seleziona/Cerca/Aggiungi modelli disponibili, lascia vuoto per nessun limite.", "activateOnFirstUse": "Attivazione iniziale", "activateOnFirstUseTooltip": "Se questa opzione è attivata e si attiva tramite il primo utilizzo, sovrascriverà il periodo di validità del token configurato sopra.", "activationValidPeriod": "Periodo di validità dell'attivazione", "activationValidPeriodTooltip": "Periodo di validità del token attivato dopo il primo utilizzo (unità: giorni)", "ipWhitelist": "Elenco di indirizzi IP autorizzati", "ipWhitelistPlaceholder": "Indirizzi IP (intervallo), supporta IPV4 e IPV6, separati da virgole.", "rateLimiter": "limitatore di corrente", "rateLimitPeriod": "Periodo di limitazione del flusso", "rateLimitPeriodTooltip": "Periodo di limitazione del flusso (unità: secondi)", "rateLimitCount": "numero di limitazione del flusso", "rateLimitCountTooltip": "Numero di utilizzi disponibili durante il periodo di limitazione del flusso.", "promptMessage": "Messaggio di avviso", "promptMessageTooltip": "Messaggio di avviso quando il limite di traffico è superato.", "promotionPosition": "Posizione promozionale", "promotionPositionStart": "<PERSON><PERSON><PERSON>", "promotionPositionEnd": "fine", "promotionPositionRandom": "casuale", "promotionContent": "Contenuti promozionali", "currentGroup": "Gruppo attuale", "searchGroupPlaceholder": "Cerca nome del gruppo, descrizione o moltiplicatore...", "mjTranslateConfig": "Configurazione di traduzione MJ", "mjTranslateConfigTip": "Configurazione di traduzione valida solo per le parole chiave di Midjourney.", "mjTranslateBaseUrlPlaceholder": "Si prega di inserire l'URL di base del servizio di traduzione.", "mjTranslateApiKeyPlaceholder": "Si prega di inserire la chiave API per il servizio di traduzione.", "mjTranslateModelPlaceholder": "Si prega di inserire il nome del modello utilizzato per il servizio di traduzione.", "mjTranslateBaseUrlRequired": "È necessario fornire l'URL di base quando si attiva la traduzione.", "mjTranslateApiKeyRequired": "È necessario fornire una chiave API per attivare la traduzione.", "mjTranslateModelRequired": "È necessario fornire il nome del modello quando si attiva la traduzione."}, "addTokenQuotaModal": {"title": "Gestione del saldo del token {{username}}", "defaultReason": "Operazioni dell'amministratore", "enterRechargeAmount": "Inserisci l'importo del deposito.", "enterRemark": "Si prega di inserire un messaggio di nota.", "confirmOperation": "Conferma operazione", "confirmContent": "Confermi {{username}}{{action}}{{amount}} dollari {{updateExpiry}}?", "recharge": "Ricarica", "deduct": "detrazione", "andUpdateExpiry": "e aggiorna la validità del saldo a {{days}} gior<PERSON>.", "alertMessage": "L'inserimento di un numero negativo può ridurre il saldo dell'utente.", "rechargeAmount": "Importo di ricarica", "operationReason": "Motivo dell'operazione", "finalBalance": "Saldo finale"}, "billingType": {"1": "Fatturazione basata sul consumo", "2": "Pagamento a consumo", "3": "Fatturazione mista", "4": "Priorità in base alla quantità", "5": "Secondo le priorità.", "payAsYouGo": "Fatturazione basata sul consumo", "payPerRequest": "Pagamento a consumo", "hybrid": "Fatturazione mista", "payAsYouGoPriority": "Priorità in base alla quantità", "payPerRequestPriority": "Secondo le priorità.", "unknown": "modo sconosciuto"}, "packagePlanAdmin": {"title": "<PERSON><PERSON><PERSON>", "table": {"title": "Gestione dei pacchetti", "toolBar": {"add": "Nuovo pacchetto", "delete": "Elimina il pacchetto"}, "action": {"edit": "Editore", "delete": "Elimina", "detail": "<PERSON><PERSON><PERSON>", "recovery": "Mettere in vendita", "offline": "<PERSON><PERSON><PERSON><PERSON>"}}, "modal": {"title": {"add": "Nuovo pacchetto", "edit": "Pacchetto di editing"}, "field": {"name": "Nome del pacchetto", "type": {"default": "Tipo di pacchetto", "type1": "<PERSON>chet<PERSON> di credito", "type2": "Pacchetto a consumo", "type3": "Pacchetto di durata"}, "group": "Gruppi di pacchetti", "description": "Descrizione del pacchetto", "price": "Prezzo del pacchetto", "valid_period": "Data di scadenza", "first_buy_discount": "Sconto per il primo acquisto", "rate_limit_num": "limite di volte", "rate_limit_duration": "Periodo di limitazione", "inventory": "Inventario del pacchetto", "available_models": "Modelli disponibili", "quota": "Limite del pacchetto", "times": "Numero di pacchetti"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "update": "Aggiornamento"}}}, "login": {"title": "Accesso", "username": "Nome utente", "password": "password", "login": "Accesso", "otherLoginMethods": "Altri metodi di accesso", "register": "Registrare un account", "accountLogin": "Accesso all'account", "phoneLogin": "Accesso tramite numero di cellulare", "usernamePlaceholder": "Nome utente", "usernameRequired": "Per favore inserisci il nome utente!", "passwordPlaceholder": "password", "passwordRequired": "Inserisci la password!", "passwordMaxLength": "La lunghezza della password non può superare i 20 caratteri!", "phonePlaceholder": "Numero di telefono cellulare", "phoneRequired": "Si prega di inserire il numero di cellulare!", "phoneFormatError": "Formato del numero di cellulare errato!", "smsCodePlaceholder": "Codice di verifica SMS", "smsCodeCountdown": "Ritenta tra {{count}} secondi.", "getSmsCode": "Ottieni il codice di verifica", "agreementText": "Sono d'accordo.", "privacyPolicy": "Politica sulla privacy", "and": "e", "serviceAgreement": "\"<PERSON>tratto di servizio\"", "alreadyLoggedIn": "Hai effettuato l'accesso.", "weakPasswordWarning": "La tua password è troppo semplice, ti preghiamo di modificarla tempestivamente!", "welcomeMessage": "Benvenuto!", "captchaError": "Codice di verifica errato", "credentialsError": "Nome utente o password errati", "resetPassword": "Reimposta la password", "captchaExpired": "Il codice di verifica non esiste o è scaduto.", "loginFailed": "Accesso fallito: {{message}}", "captchaRequired": "Inserisci il codice di verifica!", "captchaPlaceholder": "Codice di verifica", "smsSent": "Il codice di verifica SMS è stato inviato con successo.", "smsSendFailed": "Invio del codice di verifica tramite SMS non riuscito.", "agreementWarning": "Si prega di accettare prima l'Informativa sulla privacy e il Contratto di servizio.", "turnstileWarning": "Per favore riprova più tardi, Turnstile sta controllando l'ambiente dell'utente!", "loginSuccess": "<PERSON><PERSON> r<PERSON>"}, "register": {"title": "Registrazione", "usernameRequired": "Per favore inserisci il nome utente!", "usernameNoAt": "Il nome utente non può contenere il simbolo @.", "usernameNoChinese": "Il nome utente non può contenere caratteri cinesi.", "usernameLength": "La lunghezza del nome utente deve essere compresa tra 4 e 12 caratteri.", "usernamePlaceholder": "Nome utente", "passwordRequired": "Inserisci la password!", "passwordLength": "La lunghezza della password deve essere compresa tra 8 e 20 caratteri.", "passwordPlaceholder": "password", "confirmPasswordRequired": "Si prega di confermare la password!", "passwordMismatch": "Le due password inserite non corrispondono!", "confirmPasswordPlaceholder": "Conferma password", "emailInvalid": "Per favore inserisci un indirizzo email valido!", "emailRequired": "Per favore inserisci l'email!", "emailPlaceholder": "indirizzo email", "emailCodeRequired": "Inserisci il codice di verifica dell'email!", "emailCodePlaceholder": "Codice di verifica dell'email", "enterCaptcha": "Inserisci il codice di verifica.", "resendEmailCode": "Ritenta tra {{seconds}} secondi.", "getEmailCode": "Ottieni il codice di verifica", "phoneRequired": "Si prega di inserire il numero di cellulare!", "phoneInvalid": "Il formato del numero di cellulare non è corretto!", "phonePlaceholder": "numero di cellulare", "smsCodeRequired": "Inserisci il codice di verifica SMS!", "smsCodePlaceholder": "Codice di verifica SMS", "resendSmsCode": "<PERSON><PERSON><PERSON> tra {{seconds}} secondi.", "getSmsCode": "Ottieni il codice di verifica", "captchaRequired": "Inserisci il codice di verifica!", "captchaPlaceholder": "Codice di verifica", "inviteCodePlaceholder": "Codice di invito (facoltativo)", "submit": "Registrazione", "successMessage": "Registrazione avvenuta con successo", "failMessage": "Registrazione fallita", "emailCodeSent": "Il codice di verifica dell'email è stato inviato.", "smsCodeSent": "Il codice di verifica SMS è stato inviato.", "confirm": "Conferma", "emailVerifyTitle": "Verifica dell'email", "smsVerifyTitle": "Verifica tramite SMS", "registerVerifyTitle": "Registrazione verifica"}, "profile": {"timezone": "fuso orario", "phoneNumber": "numero di cellulare", "emailAddress": "indirizzo email", "wechatAccount": "Account WeChat", "telegramAccount": "Account Telegram", "bindTelegram": "Collegare Telegram", "balanceValidPeriod": "Scadenza del saldo", "lastLoginIP": "Ultimo IP di accesso", "lastLoginTime": "Ultimo accesso", "inviteCode": "Codice di invito", "inviteLink": "Link di invito", "generate": "Generare", "pendingEarnings": "Entrate da utilizzare", "transfer": "trasferimento", "totalEarnings": "<PERSON>vi totali", "accountBalance": "Saldo del conto", "totalConsumption": "spesa totale", "callCount": "Numero di chiamate", "invitedUsers": "Invitare gli utenti", "promotionInfo": "Informazioni promozionali", "inviteDescription": "Un invito, un rimborso a vita; più inviti fai, più rimborso ricevi.", "userInfo": "Informazioni utente", "availableModels": "Modelli disponibili", "modelNameCopied": "Nome del modello copiato", "noAvailableModels": "Nessun modello disponibile.", "accountOptions": "Opzioni dell'account", "changePassword": "Cambia la password", "systemToken": "Token di sistema", "unsubscribe": "Cancellazione", "educationCertification": "Certificazione educativa", "timezoneUpdateSuccess": "Aggiornamento del fuso orario riuscito.", "inviteLinkCopied": "Il link di invito è stato copiato.", "inviteLinkCopyFailed": "Impossibile copiare il link di invito.", "inviteLinkGenerationFailed": "Generazione del link di invito fallita.", "allModelsCopied": "<PERSON>tti i modelli sono stati copiati negli appunti.", "copyAllModels": "Copia tutti i modelli.", "totalModels": "Numero di modelli disponibili", "expired": "Scaduto", "validPeriod": "Data di scadenza", "longTermValid": "a lungo termine efficace", "failedToLoadModels": "Impossibile caricare l'elenco dei modelli.", "accessTokens": "Token di accesso", "accessTokensManagement": "Gestione token di accesso", "accessTokenDescription": "I token di accesso consentono alle applicazioni esterne di accedere al tuo account", "tokenNameLabel": "Nome token", "tokenNamePlaceholder": "Inserisci il nome del token", "presetPermissions": "Permessi preimpostati", "detailPermissions": "<PERSON><PERSON><PERSON>", "validityPeriod": "Periodo di validità", "validityPeriodExtra": "Periodo di validità extra", "remarkLabel": "<PERSON>a", "remarkPlaceholder": "Inserisci...", "createNewToken": "Crea nuovo token", "tokenCreatedSuccess": "Token creato con successo", "tokenSavePrompt": "<PERSON>va il token in un luogo sicuro", "copyToken": "Copia token", "readPermission": "Permesso di lettura", "writePermission": "Permesso di scrittura", "deletePermission": "Permesso di eliminazione", "tokenManagement": "Gestione token", "channelManagement": "Gestione canali", "logView": "Visualizzazione log", "statisticsView": "Visualizzazione statistiche", "userManagement": "Gestione utenti", "quotaManagement": "Gestione quote", "readOnlyPermission": "Permesso di sola lettura", "writeOnlyPermission": "Permesso di sola scrittura", "readWritePermission": "Permesso di lettura e scrittura", "standardPermission": "Permesso standard", "fullPermission": "<PERSON><PERSON><PERSON> completo", "selectPermission": "Seleziona permesso", "tokenStatus": "Stato token", "tokenEnabled": "Abilitato", "tokenDisabled": "Disabilitato", "enableToken": "Abilita token", "disableToken": "Disabilita token", "deleteToken": "Elimina token", "deleteTokenConfirm": "Conferma eliminazione token", "disableTokenConfirm": "Conferma disabilitazione token", "enableTokenConfirm": "Conferma abilitazione token", "tokenExpiryNever": "Non scade mai", "accessTokensInfo": "Informazioni token di accesso", "accessTokensInfoDetail1": "I token di accesso consentono alle applicazioni di accedere al tuo account", "accessTokensInfoDetail2": "Conserva i token di accesso in un luogo sicuro", "accessTokensInfoDetail3": "Puoi revocare i token di accesso in qualsiasi momento", "accessTokensInfoDetail4": "I token di accesso hanno permessi limitati", "accessTokensInfoDetail5": "<PERSON><PERSON>di attentamente i permessi prima della creazione", "noPermission": "<PERSON><PERSON><PERSON> permesso per eseguire questa operazione"}, "topup": {"onlineRecharge": "Ricarica online", "cardRedemption": "Codice di riscatto", "accountBalance": "Saldo del conto", "rechargeReminder": "Promemoria di ricarica", "reminder1": "1. Il saldo può essere utilizzato per chiamate di modelli, acquisto di p<PERSON>chetti, ecc.", "reminder2": "2. Se l'importo non è accreditato dopo il pagamento, si prega di contattare il servizio clienti per assistenza.", "reminder3": "3. Il saldo non supporta il prelievo, ma può essere trasferito all'interno dello stesso gruppo di utenti.", "reminder4WithTransfer": "4. <PERSON><PERSON> il successo della ricarica, la validità del saldo dell'account sarà ripristinata a", "reminder4WithoutTransfer": "3. <PERSON><PERSON> il successo della ricarica, la validità del saldo dell'account sarà ripristinata a", "days": "cielo", "paymentSuccess": "Pagamento r<PERSON>", "paymentError": "Erro di pagamento", "paymentAmount": "Importo da pagare:", "purchaseAmount": "Importo di acquisto: $", "yuan": "yuan", "or": "o", "usd": "dollaro americano", "cny": "yuan", "enterAmount": "Inserisci l'importo del deposito!", "amountPlaceholder": "Inserisci l'importo del deposito, a partire da {{min}} dollari.", "amountUpdateError": "Si è verificato un errore durante l'aggiornamento dell'importo.", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "Formato del codice di riscatto errato.", "redeemSuccess": "{{amount}} scambiato con successo!", "redeemError": "Si è verificato un errore durante il riscatto, riprova più tardi.", "enterCardKey": "Inserisci il codice di riscatto.", "cardKeyPlaceholder": "Inserisci il codice di riscatto.", "buyCardKey": "Acquista codice di riscatto.", "redeem": "Liquidazione immediata", "record": {"title": "Registro di ricarica", "amount": "Importo di ricarica", "payment": "Importo da pagare", "paymentMethod": "Modalità di pagamento", "orderNo": "Numero d'ordine", "status": "stato", "createTime": "Data di creazione", "statusSuccess": "successo", "statusPending": "In elaborazione", "statusFailed": "fallimento"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "amministratore", "paymentMethodRedeem": "Codice di riscatto", "alipayF2F": "Alipay faccia a faccia"}, "pricing": {"fetchErrorMessage": "Si è verificato un errore nel recupero delle informazioni sui prezzi, contattare l'amministratore.", "availableModelErrorMessage": "Si è verificato un errore nel recupero dei modelli disponibili, contattare l'amministratore.", "modelName": "Nome del modello", "billingType": "Tipo di fatturazione", "price": "Prezzo", "ratio": "moltiplicatore", "promptPriceSame": "Prezzo suggerito: uguale alla tariffa originale", "completionPriceSame": "Prezzo di completamento: uguale alla tariffa originale.", "promptPrice": "Prezzo suggerito: $ {{price}} / 1M token", "completionPrice": "Prezzo completo: $ {{price}} / 1M token", "promptRatioSame": "Rapporto di suggerimento: uguale al rapporto originale.", "completionRatioSame": "Fattore di completamento: uguale al fattore originale.", "promptRatio": "Rapporto di suggerimento: {{ratio}}", "completionRatio": "Rapporto di completamento: {{ratio}}", "payAsYouGo": "Pagamento basato sul consumo - Chat", "fixedPrice": "$ {{price}} / volta", "payPerRequest": "Pagamento per utilizzo - Chat", "dynamicPrice": "$ {{price}} / volta", "payPerRequestAPI": "Pagamento per utilizzo - API", "loadingTip": "Sto ottenendo informazioni sui prezzi...", "userGroupRatio": "Il tuo rapporto di gruppo utenti è: {{ratio}}", "readFailed": "Lettura fallita", "billingFormula": "Costo di fatturazione per consumo = Tasso di conversione × Moltiplicatore di gruppo × Moltiplicatore di modello × (Numero di token di suggerimento + Numero di token di completamento × Moltiplicatore di completamento) / 500000 (unità: dollari)", "billingFormula1": "Tasso di conversione = (nuovo tasso di ricarica / tasso di ricarica originale) × (nuovo tasso di gruppo / tasso di gruppo originale)", "generatedBy": "Questa pagina è stata generata automaticamente da {{systemName}}.", "modalTitle": "Dettagli sui prezzi", "perMillionTokens": "/1M token", "close": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Cerca nome del modello", "viewGroups": "Visualizza gruppi", "copiedToClipboard": "Copiato negli appunti.", "copyFailed": "Copia fallita", "groupName": "Nome del gruppo", "availableGroups": "Gruppi disponibili per il modello {{model}}", "noGroupsAvailable": "Nessun gruppo disponibile", "modelGroupsErrorMessage": "Impossibile ottenere i dati del gruppo modello.", "currentGroup": "Gruppo attuale", "copyModelName": "Copia il nome del modello", "groupRatio": "Rapporto di suddivisione", "closeModal": "<PERSON><PERSON><PERSON>", "groupsForModel": "Modelli disponibili per gruppi", "actions": "Operazione", "filterByGroup": "Filtraggio per gruppo", "groupSwitched": "<PERSON>ato al gruppo: {{group}}", "showAdjustedPrice": "Mostra il prezzo dopo l'aggiustamento del gruppo (rapporto attuale: {{ratio}})"}, "guestQuery": {"usageTime": "Tempo di utilizzo", "modelName": "Nome del modello", "promptTooltip": "Input consumare token", "completionTooltip": "Output consumo di token", "quotaConsumed": "Quota di consumo", "pasteConfirm": "È stato rilevato un token valido negli appunti, vuoi incollarlo?", "queryFailed": "<PERSON><PERSON> fallita", "tokenExpired": "Il token è scaduto.", "tokenExhausted": "Il limite del token è stato raggiunto.", "invalidToken": "Inser<PERSON>ci il token corretto.", "focusRequired": "Si prega di assicurarsi che la pagina sia in stato di messa a fuoco.", "queryFirst": "Per favore controlla prima.", "tokenInfoText": "Totale token: {{totalQuota}}  \nToken consumati: {{usedQuota}}  \nSaldo token: {{remainQuota}}  \nNumero di chiamate: {{callCount}}  \nScadenza: {{validUntil}}", "unlimited": "senza limiti", "neverExpire": "mai scadere", "infoCopied": "Le informazioni sul token sono state copiate negli appunti.", "copyFailed": "Copia fallita", "noDataToExport": "<PERSON><PERSON><PERSON> dato può essere esportato.", "prompt": "Suggerimento", "completion": "completare", "disabled": "La ricerca dei visitatori non è attivata.", "tokenQuery": "<PERSON><PERSON> di <PERSON>", "tokenPlaceholder": "Please enter...", "tokenInfo": "Informazioni sul token", "copyInfo": "Copia informazioni", "totalQuota": "Importo totale dei token", "usedQuota": "Consumo di token", "remainQuota": "Saldo del <PERSON>", "callCount": "Numero di chiamate", "validUntil": "Data di scadenza fino a", "currentRPM": "RPM attuale", "currentTPM": "TPM attuale", "callLogs": "Registro delle chiamate", "exportLogs": "Esporta il registro"}, "agencyProfile": {"fetchError": "Impossibile ottenere le informazioni sull'agente.", "fetchCommissionError": "Impossibile ottenere l'elenco delle commissioni.", "systemPreset": "Impostazioni di sistema", "lowerRatioWarning": "La tariffa è inferiore a quella preimpostata dal sistema.", "lowerRatioMessage": "Le seguenti tariffe sono inferiori ai valori preimpostati dal sistema, si prega di modificarle tempestivamente:", "cancelRatioEdit": "Annulla la modifica della tariffa", "updateSuccess": "Aggiornamento riuscito", "updateError": "Aggiornamento delle informazioni sull'agente non riuscito:", "updateFailed": "Aggiornamento fallito:", "customPriceUpdateSuccess": "Aggiornamento del prezzo personalizzato r<PERSON>.", "customPriceUpdateError": "Aggiornamento del prezzo personalizzato non riuscito:", "time": "Tempo", "type": "Tipo", "agencyCommission": "Commissione per gli agenti.", "unknownType": "<PERSON><PERSON><PERSON>", "amount": "importo", "balance": "saldo", "description": "Descrizione", "group": "Gruppo", "customRate": "<PERSON>riff<PERSON>", "systemDefaultRate": "Tariff predefinito del sistema", "action": "Operazione", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Editore", "agencyConsole": "Pannello di controllo dell'agente", "agencyInfo": "Informazioni sull'agente", "editInfo": "Modifica informazioni", "agencyName": "Nome dell'agente", "agencyLevel": "Livello dell'agente", "level1": "Livello 1", "subordinateUsers": "Utente di livello inferiore", "totalSales": "Vendite totali", "commissionIncome": "Entrate da commissioni", "cumulativeEarnings": "<PERSON><PERSON><PERSON><PERSON> accumulati", "agencyFunctions": "Funzione di delega", "hideSubordinateUsers": "Nascondi gli utenti di livello inferiore", "viewSubordinateUsers": "Visualizza utenti subordinati", "hideCommissionDetails": "Nascondi i dettagli delle commissioni", "viewCommissionDetails": "Visualizza i dettagli delle commissioni", "hideCustomPrice": "Nascondi prezzo personalizzato", "setCustomPrice": "Imposta un prezzo personalizzato", "subordinateUsersList": "Elenco degli utenti subordinati", "commissionRecords": "Registro delle commissioni", "customPriceSettings": "Impostazione del prezzo personalizzato", "saveChanges": "Salva le modifiche", "editAgencyInfo": "Modifica le informazioni dell'agente.", "logo": "Logo", "setAgencyLogo": "Imposta il logo dell'agente.", "customHomepage": "Homepage personalizzata", "aboutContent": "<PERSON><PERSON><PERSON><PERSON> al contenuto", "newHomepageConfig": "Nuova configurazione della homepage", "customAnnouncement": "<PERSON><PERSON><PERSON>", "customRechargeGroupRateJson": "JSON delle tariffe di gruppo di ricarica personalizzate", "customRechargeRate": "Tariff di ricarica personalizzato", "viewSystemDefaultRate": "Controlla la tariffa predefinita del sistema.", "rateComparison": "Confronto delle tariffe", "comparisonResult": "Risultati di confronto", "higherThanSystem": "Superiore al sistema", "lowerThanSystem": "Sotto il sistema", "equalToSystem": "uguale al sistema", "unknown": "scon<PERSON><PERSON><PERSON>", "notAnAgentYet": "Non sei ancora un agente.", "becomeAnAgent": "Diventare un agente.", "startYourOnlineBusiness": "🌟 Avvia facilmente la tua attività online", "becomeOurAgent": "Diventa il nostro agente e goditi un'esperienza imprenditoriale senza stress:", "noInventory": "💼 Nessuna necessità di magazzino, zero pressione sul capitale circolante.", "instantCommission": "💰 Vendita con divisione immediata, ottieni un ricco ritorno proporzionale.", "easyManagement": "🖥️ Nessuna competenza nella creazione di siti web necessaria, gestisci facilmente il tuo negozio online.", "flexibleDomainChoice": "🌐 Scelta flessibile del nome di dominio", "youCan": "Puoi:", "useOwnDomain": "🏠 Utilizzare il proprio dominio", "orUseOurSubdomain": "🎁 Oppure possiamo fornirti un sottodominio esclusivo.", "convenientStart": "🔥 Che tu sia un esperto o un principiante, ti offriamo un modo semplice per iniziare.", "actNow": "🚀 Agisci subito!", "contactAdmin": "Contatta l'amministratore del sito per iniziare il tuo viaggio da agente! 📞", "applyNow": "Candidati subito", "contactCooperation": "Collaborazione di consulenza", "understandPolicy": "Comprendere le politiche degli agenti e i dettagli della collaborazione.", "provideDomain": "Fornire un nome di dominio", "configDomain": "Fornisci il tuo dominio, ti aiuteremo a configurarlo.", "promoteAndEarn": "Promozione e profitto", "startPromoting": "Inizia a promuovere il tuo sito di agenzia e guadagna commissioni.", "noDeploymentWorries": "Non c'è bisogno di preoccuparsi per la complessità del deployment dei servizi cloud, dei canali di pagamento e dei problemi di approvvigionamento.", "easySetup": "Basta fornire il nome di dominio e configurarlo secondo il tutorial per avviare facilmente un'attività di rivenditore API di livello aziendale.", "customizeContent": "Puoi personaliz<PERSON><PERSON>, informazioni sul sito, SEO, logo e altro.", "commissionBenefits": "In qualità di agente, riceverai una percentuale sulle ricariche degli utenti, il sistema dedurrà automaticamente i costi e l'importo rimanente potrà essere prelevato in qualsiasi momento.", "joinNowBenefit": "Unisciti a noi ora e goditi i benefici dell'era dell'AI!", "groups": {"student": "studente universitario", "studentDesc": "Con un tempo sufficiente, spero di aumentare facilmente le entrate attraverso attività promozionali, per coprire parte delle spese di vita e di svago.", "partTime": "Lavoro part-time o attività secondaria", "partTimeDesc": "Non è necessario un grande investimento di tempo, basta promuovere semplicemente nel tempo libero lavorativo per guadagnare facilmente un reddito extra.", "mediaWorker": "Professionista dei media autonomi", "mediaWorkerDesc": "Avere una certa base di fan, basta aggiungere un link alla fine dell'articolo o del post per ottenere facilmente un reddito extra.", "freelancer": "libero professionista", "freelancerDesc": "Avere molto tempo flessibile e poter facilmente aumentare il reddito extra semplicemente partecipando ad attività di vendita."}, "stories": {"story1": {"name": "Signor <PERSON>", "role": "studente universitario"}, "story2": {"name": "<PERSON><PERSON>", "role": "insegnante di scuola media"}, "story3": {"name": "Signor <PERSON>", "role": "e-commerce"}, "story4": {"name": "Signor <PERSON>", "role": "media autonomo"}, "story5": {"name": "Signor <PERSON>", "role": "Ricercatore scientifico"}, "story6": {"name": "<PERSON><PERSON>", "role": "Blogger <PERSON>"}, "story7": {"name": "<PERSON><PERSON>", "role": "media autonomo"}, "story8": {"name": "Signor <PERSON>", "role": "Settore IT"}}, "earnedAmount": "Hai guadagnato {{amount}}", "applyForAgentNow": "Richiedi subito di diventare un agente.", "businessLinesConnected": "Oltre 40 linee di business sono già integrate.", "agencyJoin": "Text", "becomeExclusiveAgent": "Diventa il nostro agente esclusivo", "startBusinessJourney": "Inizia facilmente il tuo viaggio commerciale~", "welcomeToAgencyPage": "Benvenuti nella nostra pagina di agenzia!", "earningsTitle": "Oltre 100 persone hanno guadagnato più di 3000 yuan.", "becomeAgentSteps": "Diventare un agente: i passaggi.", "agencyRules": "Regole di agenzia", "suitableGroups": "Gruppo target", "agencyImages": {"becomeAgent": "Diventare un agente.", "agencyBusiness": "Attività di agenzia"}, "rules": {"howToEstablishRelation": "Come possono gli utenti stabilire una relazione di delega con me?", "howToEstablishRelationAnswer": "Registrati sul tuo sito di agenzia, diventerai il tuo utente.", "canSetPrice": "Posso impostare il prezzo di vendita?", "canSetPriceAnswer": "Va bene! Ma il tuo prezzo di vendita deve essere superiore del 10% al prezzo di acquisto.", "commissionShare": "Quante commissioni posso ottenere?", "commissionShareAnswer": {"assumption": "Ipotesi: il tuo prezzo di acquisto è $1=1 yuan, il tuo prezzo di vendita è $1=2 yuan, e la tua percentuale di commissione è del 90%.", "example": "L'utente acquista $10 sul tuo sito, spende 20 yuan.", "calculation": "<PERSON><PERSON><PERSON> o<PERSON>ere: (2-1)*10*0.9 = 9 yuan", "explanation": "Interpretazione: (prezzo di vendita - prezzo di acquisto) * volume di transazione * percentuale di commissione"}}}, "error": {"title": "errore", "content": "Si è verificato un errore."}, "loading": {"title": "Caricamento in corso", "content": "Caricamento in corso..."}, "notfound": {"title": "404", "content": "Pagina non trovata"}, "servererror": {"title": "cinquecento", "content": "Errore del server"}, "unauthorized": {"title": "401", "content": "Non autorizzato"}, "forbidden": {"title": "403", "content": "Accesso vietato"}, "networkerror": {"title": "Errore di rete", "content": "Errore di rete"}, "timeout": {"title": "Scadenza", "content": "Richie<PERSON> scaduta"}, "noresult": {"title": "<PERSON><PERSON><PERSON> r<PERSON>", "content": "<PERSON><PERSON><PERSON> r<PERSON>"}, "nopermission": {"title": "Accesso negato", "content": "<PERSON><PERSON><PERSON> permesso"}, "channelBridge": {"title": "Integrazione rapida dei canali", "channelPlatform": "Piattaforma di canale", "billingMethod": "Modalità di fatturazione", "channelName": "Nome del canale", "remark": "<PERSON>a", "availableGroups": "Gruppi disponibili", "availableModels": "Modelli disponibili", "channelKey": "Chiave del canale", "proxyAddress": "Indirizzo di interfaccia", "cancel": "<PERSON><PERSON><PERSON>", "submit": "Invia", "gpt35Models": "Modello GPT-3.5", "gpt4Models": "Modello GPT-4", "clear": "<PERSON><PERSON><PERSON><PERSON>", "customModelName": "Nome del modello personalizzato", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moreConfigReminder": "Per ulteriori configurazioni, si prega di salvare il canale e poi modificare.", "quickIntegration": "Collegamento con un clic", "selectBillingMethod": "Si prega di scegliere il metodo di fatturazione.", "enterChannelName": "Inserisci il nome del canale", "enterChannelRemark": "Inserisci il commento del canale", "selectAvailableGroups": "Seleziona il gruppo che può utilizzare questo canale.", "selectAvailableModels": "Seleziona/cerca i modelli disponibili per questo canale.", "enterChannelKey": "Inserisci la chiave del canale", "proxyAddressPlaceholder": "Questa opzione è facoltativa e serve per effettuare chiamate API tramite un server proxy. Si prega di inserire l'indirizzo del server proxy.", "includes16kModels": "Include modello 16k", "excludes32kModels": "Non include il modello 32k.", "cleared": "<PERSON><PERSON><PERSON> s<PERSON>", "addCustomModel": "Aggiungi modello <PERSON>", "clipboardTokenDetected": "È stato rilevato un token valido negli appunti, vuoi incollarlo?", "channelIntegrationSuccess": "Integrazione del canale riuscita!", "channelIntegrationFailed": "Integrazione del canale fallita:"}, "about": {"loading": "Ottieni contenuti aggiornati...", "noContent": "L'amministratore non ha impostato il contenuto della pagina informativa.", "loadFailed": "Impossibile caricare il contenuto..."}, "onlineTopupRecord": {"title": "Registro di ricarica", "columns": {"id": "ID", "username": "utente", "amount": "Importo di ricarica", "money": "Importo da pagare", "paymentMethod": "Modalità di pagamento", "tradeNo": "Numero d'ordine", "status": "stato", "createTime": "Data di creazione"}, "status": {"success": "successo", "pending": "In elaborazione", "failed": "fallimento"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Informazioni descrittive", "downstreamError": "Errore a valle", "originalError": "Errore originale", "requestParams": "Parametri di richiesta", "copy": "<PERSON><PERSON><PERSON>"}, "viewMode": {"switchTo": "Passa alla prospettiva {{mode}}.", "cost": "costo", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "agenciesTable": {"title": "Gestione degli agenti", "addAgency": "Nuovo agente", "columns": {"id": "ID", "userId": "ID utente", "name": "Nome", "domain": "nome di dominio", "commissionRate": "Commissione proporzionale", "salesVolume": "fatturato", "userCount": "Numero di utenti", "commissionIncome": "Entrate da commissioni", "historicalCommission": "<PERSON><PERSON><PERSON><PERSON> accumulati", "actions": "Operazione"}, "confirm": {"deleteTitle": "Sei sicuro di voler eliminare questo agente?", "updateName": "Aggiornamento del nome dell'agente...", "updateSuccess": "Aggiornamento riuscito", "updateFailed": "Aggiornamento fallito", "deleteSuccess": "Cancellazione riuscita!"}, "messages": {"getListFailed": "Impossibile ottenere l'elenco degli agenti: {{message}}", "deleteSuccess": "Cancellazione riuscita!", "loadingData": "Caricamento in corso..."}}, "units": {"times": "Time", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} volte ({{percent}}%)"}, "dailyUsage": {"total": "Totale", "totalCost": "costo totale", "tooltipTitle": {"cost": "Situazione dei costi", "usage": "Situazione d'uso"}, "yAxisName": {"cost": "Costo (USD)", "usage": "<PERSON><PERSON><PERSON><PERSON> (USD)"}}, "dailyUsageByModel": {"total": "Totale", "tooltipTotal": "Totale: $ {{value}}", "switchTo": "Passa a", "cost": "costo", "usage": "<PERSON><PERSON><PERSON><PERSON>", "perspective": "<PERSON><PERSON><PERSON><PERSON>", "granularity": {"hour": "a ore", "day": "<PERSON>ior<PERSON><PERSON><PERSON>", "week": "a settimana", "month": "mensile"}}, "checkinModal": {"title": "Si prega di completare la verifica.", "captchaPlaceholder": "Codice di verifica", "confirm": "Certamente", "close": "<PERSON><PERSON><PERSON>"}, "balanceTransfer": {"title": "Trasferimento tra conti", "accountInfo": {"balance": "Saldo del conto", "transferFee": "Commissione di trasferimento", "groupNote": "Le trasferimenti possono avvenire solo tra gruppi di utenti identici."}, "form": {"receiverId": "ID del destinatario", "receiverUsername": "Nome utente del destinatario", "remark": "Informazioni di annotazione", "amount": "Importo del trasferimento", "expectedFee": "Addebito previsto", "submit": "Avvia trasfer<PERSON>o"}, "result": {"success": "Trasferimento r<PERSON>", "continueTransfer": "Continua il trasferimento.", "viewRecord": "Visualizza registrazioni"}, "warning": {"disabled": "L'amministratore non ha attivato la funzione di trasferimento, al momento non è utilizzabile."}, "placeholder": {"autoCalculate": "Compilare l'importo del trasferimento per calcolo automatico."}}, "channelsTable": {"title": "Gestione dei canali", "columns": {"id": "ID", "name": "Nome", "type": "Tipo", "key": "chiave", "base": "Indirizzo dell'interfaccia", "models": "modello", "weight": "peso", "priority": "Priorità", "retryInterval": "Intervallo di ripetizione", "responseTime": "Tempo di risposta", "rpm": "RPM", "status": "Stato", "quota": "saldo", "expireTime": "data di scadenza", "group": "Gruppo", "billingType": "Tipo di fatturazione", "actions": "Operazione", "fusing": "interruzione automatica", "sort": "Priorità", "createdTime": "Time", "disableReason": "禁用原因"}, "status": {"all": "tutti", "normal": "normale", "enabled": "Stato normale", "manualDisabled": "Disabilitazione manuale", "waitingRetry": "Attendere il riavvio", "suspended": "So<PERSON><PERSON>e l'uso", "specified": "Stato specificato", "allDisabled": "Disabilitare", "specifiedDisabled": "Tipo di disabilitazione specificato", "partiallyDisabled": "Status"}, "placeholder": {"selectGroup": "Seleziona/Cerca gruppo", "selectStatus": "Seleziona lo stato del canale", "inputSelectModel": "Inserisci/seleziona il nome del modello", "selectFusingStatus": "Seleziona lo stato di interruzione automatica."}, "quota": {"usageAmount": "Consumo: {amount}", "remainingAmount": "Rimanente: {amount}", "customTotalAmount": "Importo personaliz<PERSON>to: {amount}", "updateNotSupported": "Attualmente non è supportato l'aggiornamento del saldo, si prega di utilizzare un saldo personalizzato.", "details": "<PERSON><PERSON><PERSON>", "sufficient": "sufficiente"}, "actions": {"edit": "Editore", "copy": "Canale di clonazione", "delete": "Elimina canale", "enable": "<PERSON><PERSON><PERSON><PERSON>", "disable": "Disabilitare", "test": "testare", "advancedTest": "Test avanzato", "viewLog": "Registro dei canali", "viewAbility": "Controllo delle capacità", "cleanUsage": "Svuota utilizzato", "updateBalance": "Aggiornare il saldo", "copyKey": "Copia la chiave"}, "confirm": {"deleteTitle": "Conferma eliminazione", "deleteContent": "Sei sicuro di voler eliminare il canale {{name}} (#{{id}})?", "cleanUsageTitle": "Conferma di azzeramento dell'utilizzo", "cleanUsageContent": "Sei sicuro di voler azzerare l'importo consumato del canale {{name}} (#{{id}})?", "testTitle": "Conferma del test", "testContent": "Sei sicuro di voler testare il canale {{status}}?", "testNote": "Attenzione: questa funzione deve essere utilizzata insieme a [Configurazione] -> [<PERSON><PERSON>] -> [Impostazioni di monitoraggio] -> [Disabilita canali in caso di errore, abilita canali in caso di successo]. Se le impostazioni correlate non sono attivate, i canali non verranno disabilitati o abilitati automaticamente al termine del test.", "deleteDisabledTitle": "Conferma di eliminazione", "deleteDisabledContent": "Sei sicuro di voler eliminare tutti i canali {{type}}?"}, "messages": {"operationSuccess": "Operazione riuscita", "operationSuccessWithSort": "Operazione riuscita, l'ordinamento dei canali potrebbe essere cambiato, si consiglia di ordinare per ID!", "operationFailed": "Operazione fallita: {{message}}", "testRunning": "Il test del canale {{name}}(#{{id}}) è in esecuzione, attendere prego...", "testSuccess": "Il canale \"{{name}}(#{{id}})\" {{model}} ha superato il test con successo, tempo di risposta {{time}} secondi.", "testFailed": "Il test del canale \"{{name}}(#{{id}})\" {{model}} è fallito. Codice di stato: {{code}}, motivo: {{reason}}, clicca per vedere i dettagli.", "testStarted": "Inizia a testare il canale {{status}}, per favore aggiorna più tardi per vedere i risultati. L'applicazione dei risultati del test dipende dalle tue impostazioni di monitoraggio.", "testOperationFailed": "Test fallito", "deleteSuccess": "Cancellazione riuscita di {{count}} canali.", "deleteFailed": "Eliminazione fallita: {{message}}", "modelPrefix": "<PERSON><PERSON> {{model}}", "channelInfo": "Informazioni sui canali", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "Il saldo del canale \"{{name}}\" è stato aggiornato con successo.", "updateBalanceFailed": "Aggiornamento del saldo del canale \"{{name}}\" non riuscito: {{message}}", "updateAllBalanceStarted": "Inizia ad aggiornare il saldo di tutti i canali in stato normale.", "updateAllBalanceSuccess": "Aggiornamento del saldo di tutti i canali riuscito.", "fetchGroupError": "Errore nel recupero dei dati del gruppo di canali: {{response}}", "fetchChannelError": "Impossibile ottenere i dati del canale: {{message}}", "selectChannelFirst": "Si prega di selezionare il canale da eliminare.", "deleteDisabledSuccess": "Tutti i canali {{type}} sono stati eliminati, per un totale di {{count}}.", "deleteOperationFailed": "Eliminazione fallita", "copySuccess": "Co<PERSON> riuscita", "copyFailed": "Copia fallita: {{message}}", "emptyKey": "La chiave è vuota.", "testSuccessWithWarnings": "Test riuscito con avvisi", "viewDetails": "Visualizza <PERSON>", "fetchChannelDetailError": "Errore nel recupero dei dettagli del canale", "topupSuccess": "Ricarica riuscita", "topupFailed": "Ricarica fallita"}, "popover": {"channelInfo": "Informazioni sul canale"}, "menu": {"deleteManualDisabled": "Elimina i canali disabilitati manualmente.", "deleteWaitingRetry": "Elimina il canale di attesa per il riavvio.", "deleteSuspended": "Eliminare i canali sospesi.", "testAll": "Testare tutti i canali", "testNormal": "Testare il canale normale", "testManualDisabled": "Testare la disabilitazione manuale del canale.", "testWaitingRetry": "Testare il canale di attesa per il riavvio.", "testSuspended": "Testare la sospensione dell'uso dei canali", "deleteDisabledAccount": "Elimina canali account disabilitato", "deleteQuotaExceeded": "Elimina canali quota superata", "deleteRateLimitExceeded": "Elimina canali limite di frequenza", "deleteInvalidKey": "Elimina canali chiave non valida", "deleteConnectionError": "Elimina canali errore di connessione"}, "tooltip": {"testNote": "È necessario configurare [Configurazione] -> [<PERSON><PERSON>] -> [Impostazioni di monitoraggio] -> [Disabilita canale in caso di errore, abilita canale in caso di successo] per utilizzarlo. Se non è attivato, non verrà disabilitato o abilitato automaticamente al termine del test di velocità."}, "disableReasons": {"account_deactivated": "Account disattivato", "quota_exceeded": "Quota superata", "rate_limit_exceeded": "Limite di frequenza superato", "invalid_key": "Chiave non valida", "connection_error": "Errore di connessione"}, "topup": {"reminder1": "Assicurati che le informazioni di pagamento siano corrette prima di procedere", "reminder2": "Se riscontri problemi, contatta il servizio clienti"}}, "billingTypes": {"quota": "limite", "times": "numero di volte"}, "serverLogViewer": {"title": "Visualizzatore dei log del server", "connecting": "Connessione al server in corso...", "downloadSelect": "Seleziona il file di registro da scaricare", "nginxConfig": "Descrizione della configurazione di Nginx WebSocket", "directAccess": "Se si accede utilizzando un nome di dominio e non è stata configurata la supporto per WebSocket, il visualizzatore di log non funzionerà. In questo caso, è possibile accedere direttamente tramite l'IP del server e la porta (ad esempio: http://your-ip:9527).", "domainAccess": "Per accedere tramite nome di dominio, è necessario aggiungere la seguente configurazione nel file di configurazione di Nginx per supportare WebSocket:", "buttons": {"pause": "pausa", "resume": "Continua", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"fetchFailed": "Impossibile ottenere l'elenco dei file di log.", "downloadFailed": "Download del file di log non riuscito.", "wsError": "Errore di connessione WebSocket"}}, "channelScore": {"score": "<PERSON><PERSON><PERSON><PERSON>", "successRate": "tasso di successo", "avgResponseTime": "Tempo medio di risposta", "title": "Punteggio del canale", "hourlyTitle": "Punteggio orario del canale", "dailyTitle": "Punteggio giornaliero del canale", "weeklyTitle": "<PERSON>unt<PERSON>gio settimanale del canale", "monthlyTitle": "Punteggio mensile del canale", "allTimeTitle": "Punteggio complessivo del canale", "infoTooltip": "Il punteggio del canale è un punteggio complessivo calcolato in base al tasso di successo e al tempo di risposta.", "tableView": "Vista tabellare", "chartView": "Visualizzazione grafica", "refresh": "Aggiornare", "selectModel": "Seleziona il modello", "allModels": "<PERSON><PERSON> i <PERSON>", "sortByScore": "Ordinato per punteggio", "sortBySuccessRate": "Ordinare in base al tasso di successo", "sortByResponseTime": "Ordinare per tempo di risposta", "noData": "<PERSON><PERSON><PERSON> dato disponibile.", "totalItems": "Totale {{total}} elementi", "fetchError": "Impossibile ottenere i dati del punteggio del canale.", "aboutScoring": "<PERSON><PERSON><PERSON><PERSON> al calcolo del punteggio", "scoringExplanation": "Il punteggio del canale è un punteggio complessivo calcolato in base al tasso di successo e al tempo di risposta, con un punteggio massimo di 1.", "successRateWeight": "Peso del tasso di successo (70%)", "successRateExplanation": "Maggiore è il tasso di successo, maggiore è il punteggio.", "responseTimeWeight": "Peso del tempo di risposta (30%)", "responseTimeExplanation": "Un punteggio massimo viene assegnato se il tempo di risposta è inferiore a 1000 ms; altrimenti, i punti vengono detratti proporzionalmente.", "columns": {"rank": "Classifica", "channelId": "ID canale", "channelName": "Nome del canale", "model": "modello", "totalRequests": "Numero totale di richieste", "successRequests": "Numero di richieste riuscite", "failedRequests": "Numero di richieste fallite", "successRate": "tasso di successo", "avgResponseTime": "Tempo medio di risposta", "score": "Punteggio complessivo", "actions": "Operazione"}, "actions": {"viewDetails": "Visualizza <PERSON>", "test": "Canale di test", "edit": "Canale di editing"}, "tooltips": {"excellent": "eccellente", "good": "<PERSON><PERSON><PERSON>", "average": "generale", "poor": "scadente", "veryPoor": "<PERSON><PERSON><PERSON>"}, "scoringExplanation100": "Il punteggio del canale è un punteggio complessivo calcolato in base al tasso di successo e al tempo di risposta, con un punteggio massimo di 100 punti."}, "menu": {"channelScores": "Punteggio del canale"}, "relay": {"dispatchOptions": "Opzioni di pianificazione", "preciseWeightCalculation": "Calcolo preciso dei pesi", "preciseWeightCalculationTip": "Una volta attivato, verrà utilizzato un algoritmo più preciso per calcolare il peso dei canali, il che potrebbe aumentare il carico della CPU.", "channelMetricsEnabled": "Abilita la statistica degli indicatori del canale.", "channelMetricsEnabledTip": "Una volta attivato, raccoglierà indicatori come il tasso di successo e il tempo di risposta del canale, utilizzati per valutare le prestazioni del canale. Se disattivato, non raccoglierà questi dati, riducendo così l'uso delle risorse di sistema.", "channelScoreRoutingEnabled": "Abilitare il routing basato sul punteggio del canale", "channelScoreRoutingEnabledTip": "Una volta attivato, il sistema regolerà automaticamente la priorità di assegnazione delle richieste in base alle prestazioni storiche dei canali, con i canali più performanti che riceveranno una probabilità di assegnazione delle richieste più alta.", "globalIgnoreBillingTypeFilteringEnabled": "Ignora filtro tipo fatturazione globalmente", "globalIgnoreBillingTypeFilteringEnabledTip": "Quando abilitato, il filtro del tipo di fatturazione verrà ignorato per tutti i canali", "globalIgnoreFunctionCallFilteringEnabled": "Ignora filtro chiamate funzione globalmente", "globalIgnoreFunctionCallFilteringEnabledTip": "Quando abilitato, il filtro delle chiamate di funzione verrà ignorato per tutti i canali", "globalIgnoreImageSupportFilteringEnabled": "Ignora filtro supporto immagini globalmente", "globalIgnoreImageSupportFilteringEnabledTip": "<PERSON>uando abilitato, il filtro del supporto immagini verrà ignorato per tutti i canali"}, "dynamicRouter": {"title": "Gestione delle route dinamiche", "reloadRoutes": "Ricarica la rotta", "exportConfig": "Esporta configurazione", "clearConfig": "Rip<PERSON><PERSON> le impostazioni predefinite", "importantNotice": "Avviso importante", "reloadLimitation": "1. Il ricaricamento della route può solo aggiornare la configurazione delle route esistenti, senza la possibilità di aggiungere o rimuovere route. Per ricaricare completamente la struttura delle route, riavviare l'applicazione.", "exportDescription": "2. L'esportazione della configurazione salverà la configurazione attuale del database nel file router.json, filtrando i valori vuoti e zero.", "clearDescription": "3. Svuotare la configurazione eliminerà tutte le configurazioni di routing dinamico nel database, e dopo il riavvio dell'applicazione verranno ricaricate dal file router.json.", "routeGroups": "Gruppo di routing", "upstreamConfig": "Configurazione a monte", "endpointConfig": "Configurazione del punto finale", "editRouteGroup": "Modifica il gruppo di routing", "editUpstream": "Modifica la configurazione upstream", "editEndpoint": "Configurazione del punto di fine dell'editor", "editJSON": "Modifica JSON", "confirmClear": "Conferma svuotamento configurazione", "confirmClearMessage": "Questa operazione cancellerà tutte le configurazioni delle route dinamiche nel database; al prossimo riavvio dell'applicazione, verranno ricaricate dal file di configurazione. Sei sicuro di voler continuare?", "configCleared": "La configurazione del routing dinamico è stata svuotata, riavvia l'applicazione per applicare le modifiche.", "configExported": "La configurazione è stata esportata con successo nel file.", "configReloaded": "La configurazione del router è stata ricaricata con successo."}, "notification": {"title": "Impostazioni notifiche", "subscriptionEvents": "Eventi sottoscritti", "notificationMethods": "Metodi di notifica", "alertSettings": "Impostazioni avvisi", "emailConfig": "Configurazione email", "customEmails": "Email <PERSON>", "addEmail": "Aggiungi email", "removeEmail": "Rimuovi email", "emailPlaceholder": "Inserisci indirizzo email", "emailTooltip": "Inserisci un indirizzo email valido", "emailDescription": "Le notifiche verranno inviate a questo indirizzo email", "balanceThreshold": "Soglia saldo", "balanceThresholdTooltip": "<PERSON><PERSON><PERSON>à inviato un avviso quando il saldo scende sotto questa soglia", "balanceThresholdDescription": "Inserisci la soglia del saldo per l'avviso", "alertExplanationTitle": "Spiegazione avvisi", "alertExplanation": "Verranno inviati avvisi quando si verificano gli eventi selezionati", "selectEvents": "Seleziona eventi", "eventsDescription": "Scegli gli eventi per cui vuoi ricevere notifiche", "selectMethods": "Seleziona i metodi per ricevere le notifiche", "methodsDescription": "<PERSON><PERSON><PERSON> come ricevere le notifiche", "description": "Descrizione", "recommended": "Consigliato", "important": "Importante", "testRecommendation": "Si consiglia di testare prima di salvare", "testNotification": "Test notifica", "testMessage": "Questo è un messaggio di test", "testSuccess": "Notifica di test inviata con successo", "testFailed": "Invio notifica di test fallito", "saveSuccess": "Impostazioni notifica salvate con successo", "saveFailed": "Salvataggio impostazioni notifica fallito", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Inserisci il Token del Bot Telegram"}, "qywxbotConfig": "Configurazione bot WeChat Enterprise", "qywxbotGuide": "Message", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkConfig": "Configurazione bot DingTalk", "dingtalkGuide": "Message", "feishuConfig": "Configurazione bot <PERSON><PERSON>u", "feishuGuide": "Message", "webhookConfig": "Webhook配置", "webhookGuide": "Message", "webhookUrl": "调用地址", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "Configurazione bot Telegram", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "额度即将过期", "security_alert": "Message", "system_announcement": "系统公告", "promotional_activity": "Message", "model_pricing_update": "模型价格更新", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Message", "detailedDocumentation": "详细文档：", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "企业微信群机器人配置说明", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Visita il sito ufficiale WxPusher per registrare un account", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "设置机器人名称和描述", "feishuStep4": "Message", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "复制获得的Bot Token", "telegramStep5": "Message", "telegramStep6": "访问 https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "消息格式支持：", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "注意事项：", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Chat ID格式说明：", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "• 群组中需要先将机器人添加为成员", "telegramChannelPermission": "Message", "webhookCallUrl": "Indirizzo di chiamata", "webhookConfigurationGuide": "Guida alla configurazione Webhook", "webhookDataFormatExample": "Esempio di formato dati：", "webhookConfigurationInstructions": "Istruzioni di configurazione：", "webhookRequestMethod": "• Metodo di richiesta：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• Metodo di autenticazione：<PERSON><PERSON>（opzionale, dopo l'inserimento aggiungerà Authorization: Bearer {token} nell'header della richiesta）", "webhookTimeout": "• Tempo di timeout：30 secondi", "webhookRetryMechanism": "• Meccanismo di ripetizione：Riprova 2 volte dopo il fallimento", "webhookTip": "💡 Suggerimento：Assicurati che il tuo endpoint Webhook possa ricevere richieste POST e restituire codici di stato 2xx", "telegramStep3Detailed": "Message", "telegramPersonalChatDetailed": "Message", "telegramGroupChatDetailed": "Message", "telegramChannelDetailed": "Message", "telegramQuickChatIdTitle": "Message", "telegramQuickStep1": "Message", "telegramQuickStep2": "Message", "telegramQuickStep3": "Message"}, "legal": {"privacyPolicy": {"title": "Informativa sulla privacy", "lastUpdated": "Ultimo aggiornamento", "sections": {"informationCollection": {"title": "Raccolta informazioni", "description": "Raccogliamo le seguenti informazioni", "items": {"accountInfo": "Informazioni account", "usageData": "<PERSON>ti di utilizzo", "technicalInfo": "Informazioni tecniche"}}, "informationUsage": {"title": "Utilizzo delle informazioni", "description": "Utilizziamo le informazioni per i seguenti scopi", "items": ["提供和维护我们的服务", "Description", "改进服务质量和用户体验", "Description", "防止欺诈和滥用"]}, "informationSharing": {"title": "Title", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "数据安全", "description": "Description", "items": ["数据加密传输和存储", "Text", "定期安全审计和更新", "Text"]}, "dataRetention": {"title": "数据保留", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "您的权利", "description": "Description", "items": ["User", "删除您的账户和相关数据", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["维持用户会话", "Text", "Text"]}, "thirdPartyServices": {"title": "第三方服务", "description": "Description", "items": ["Text", "GitHub OAuth：用于用户身份验证", "Text"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "服务描述", "description": "Description", "items": ["API 密钥管理", "Description", "使用统计和监控", "Description", "Description"]}, "userAccount": {"title": "用户账户", "description": "Description", "items": ["Text", "User", "User", "及时更新账户信息", "User"]}, "usageRules": {"title": "使用规则", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "服务可用性", "description": "Description", "items": ["Text", "Text", "Text", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "高级功能可能需要付费", "Text", "Text"]}, "intellectualProperty": {"title": "知识产权", "description": "Description", "items": ["Text", "您获得有限的使用许可", "Text", "Text"]}, "privacyProtection": {"title": "隐私保护", "description": "Description", "items": ["Text", "采取合理措施保护数据安全", "Text"]}, "disclaimer": {"title": "免责声明", "description": "Description", "items": ["Text", "Text", "不对间接损失承担责任", "Text"]}, "serviceTermination": {"title": "服务终止", "description": "Description", "items": ["您违反这些条款", "Text", "Text", "法律要求"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["重大变更会提前通知", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "© {{year}} Shell API Pro Max. Tutti i diritti riservati.", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "[Indirizzo della vostra azienda]"}}, "tasks": {"title": "Elenco attività", "taskId": "ID attività", "platform": "Piattaforma", "type": "Tipo", "status": "Stato", "progress": "Progresso", "submitTime": "Ora di invio", "startTime": "Ora di inizio", "endTime": "<PERSON>a di fine", "duration": "<PERSON><PERSON>", "result": "Risultato", "taskIdPlaceholder": "Inserisci ID attività", "platformPlaceholder": "Seleziona piattaforma", "typePlaceholder": "Seleziona tipo", "statusPlaceholder": "Seleziona stato", "videoGeneration": "Generazione video", "imageGeneration": "Generazione immagine", "musicGeneration": "Generazione musicale", "textGeneration": "Generazione di testo", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "Successo", "failed": "Fallito", "inProgress": "In corso", "submitted": "Inviato", "queued": "In coda", "notStarted": "Non iniziato", "viewResult": "Visualizza risultato", "viewError": "Visualizza errore", "taskDetails": "Det<PERSON><PERSON> attivi<PERSON>", "errorDetails": "<PERSON><PERSON><PERSON> errore", "loadError": "Errore di caricamento", "refreshSuccess": "Stato del compito aggiornato con successo.", "refreshFailed": "Aggiornamento dello stato del compito non riuscito.", "refreshError": "Si è verificato un errore durante l'aggiornamento dello stato del compito.", "viewVideo": "Visualizza video", "videoPreview": "Anteprima video", "copyVideoUrl": "Copia URL video", "copiedVideoUrl": "URL video copiato", "downloadVideo": "Scarica video", "videoNotSupported": "Video non supportato", "videoUrl": "URL video", "videoUrls": "URL video"}}