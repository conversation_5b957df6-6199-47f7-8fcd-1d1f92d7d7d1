import React from 'react';
import { Card, Typography, Divider, Space, Alert } from 'antd';
import { FileTextOutlined, MailOutlined, HomeOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Paragraph, Text } = Typography;

const TermsOfService: React.FC = () => {
    const { t } = useTranslation();

    const renderSection = (sectionKey: string, index: number) => {
        const section = t(`legal.termsOfService.sections.${sectionKey}`, { returnObjects: true }) as any;

        return (
            <div key={sectionKey}>
                <Title level={2}>{index}. {section.title}</Title>
                <Paragraph>{section.description}</Paragraph>
                {section.items && (
                    <ul>
                        {section.items.map((item: string, itemIndex: number) => (
                            <li key={itemIndex}>{item}</li>
                        ))}
                    </ul>
                )}
            </div>
        );
    };

    const sections = [
        'serviceDescription',
        'userAccount',
        'usageRules',
        'prohibitedBehavior',
        'serviceAvailability',
        'feesAndPayment',
        'intellectualProperty',
        'privacyProtection',
        'disclaimer',
        'serviceTermination',
        'termsModification',
        'disputeResolution'
    ];

    return (
        <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
            <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                    <div style={{ textAlign: 'center' }}>
                        <FileTextOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
                        <Title level={1}>{t('legal.termsOfService.title')}</Title>
                        <Text type="secondary">{t('legal.termsOfService.lastUpdated', { date: '2025-07-21' })}</Text>
                    </div>

                    <Alert
                        message={t('legal.termsOfService.importantNotice')}
                        description={t('legal.termsOfService.importantNotice')}
                        type="warning"
                        showIcon
                    />

                    <Divider />

                    {sections.map((section, index) => renderSection(section, index + 1))}

                    <div>
                        <Title level={2}>{sections.length + 1}. {t('legal.termsOfService.sections.contactUs.title')}</Title>
                        <Card style={{ backgroundColor: '#f6f8fa' }}>
                            <Paragraph>
                                {t('legal.termsOfService.sections.contactUs.description')}
                            </Paragraph>
                            <Space direction="vertical">
                                <div>
                                    <MailOutlined style={{ marginRight: '8px' }} />
                                    <Text strong>{t('legal.termsOfService.sections.contactUs.email')}</Text>：{t('legal.common.supportEmail')}
                                </div>
                                <div>
                                    <HomeOutlined style={{ marginRight: '8px' }} />
                                    <Text strong>{t('legal.termsOfService.sections.contactUs.address')}</Text>：{t('legal.common.companyAddress')}
                                </div>
                                <div>
                                    <ClockCircleOutlined style={{ marginRight: '8px' }} />
                                    <Text strong>{t('legal.termsOfService.sections.contactUs.serviceHours')}</Text>
                                </div>
                            </Space>
                        </Card>
                    </div>

                    <Divider />

                    <div style={{ textAlign: 'center' }}>
                        <Text type="secondary">
                            {t('legal.common.copyright', { year: new Date().getFullYear() })}
                        </Text>
                    </div>
                </Space>
            </Card>
        </div>
    );
};

export default TermsOfService;
