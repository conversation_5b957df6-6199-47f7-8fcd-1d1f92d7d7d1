import React from 'react';
import { Card, Typography, Divider, Space } from 'antd';
import { SafetyCertificateOutlined, MailOutlined, HomeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Paragraph, Text } = Typography;

const PrivacyPolicy: React.FC = () => {
    const { t } = useTranslation();

    const renderSection = (sectionKey: string, index: number) => {
        const section = t(`legal.privacyPolicy.sections.${sectionKey}`, { returnObjects: true }) as any;

        return (
            <div key={sectionKey}>
                <Title level={2}>{index}. {section.title}</Title>
                <Paragraph>{section.description}</Paragraph>
                {section.items && (
                    <ul>
                        {Array.isArray(section.items)
                            ? section.items.map((item: string, itemIndex: number) => (
                                <li key={itemIndex}>{item}</li>
                            ))
                            : Object.entries(section.items).map(([key, value]) => (
                                <li key={key}><Text strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</Text>：{value as string}</li>
                            ))
                        }
                    </ul>
                )}
            </div>
        );
    };

    const sections = [
        'informationCollection',
        'informationUsage',
        'informationSharing',
        'dataSecurity',
        'dataRetention',
        'userRights',
        'cookieUsage',
        'thirdPartyServices',
        'childrenPrivacy',
        'policyUpdates'
    ];

    return (
        <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
            <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                    <div style={{ textAlign: 'center' }}>
                        <SafetyCertificateOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
                        <Title level={1}>{t('legal.privacyPolicy.title')}</Title>
                        <Text type="secondary">{t('legal.privacyPolicy.lastUpdated', { date: '2025-07-21' })}</Text>
                    </div>

                    <Divider />

                    {sections.map((section, index) => renderSection(section, index + 1))}

                    <div>
                        <Title level={2}>{sections.length + 1}. {t('legal.privacyPolicy.sections.contactUs.title')}</Title>
                        <Card style={{ backgroundColor: '#f6f8fa' }}>
                            <Paragraph>
                                {t('legal.privacyPolicy.sections.contactUs.description')}
                            </Paragraph>
                            <Space direction="vertical">
                                <div>
                                    <MailOutlined style={{ marginRight: '8px' }} />
                                    <Text strong>{t('legal.privacyPolicy.sections.contactUs.email')}</Text>：{t('legal.common.contactEmail')}
                                </div>
                                <div>
                                    <HomeOutlined style={{ marginRight: '8px' }} />
                                    <Text strong>{t('legal.privacyPolicy.sections.contactUs.address')}</Text>：{t('legal.common.companyAddress')}
                                </div>
                            </Space>
                        </Card>
                    </div>

                    <Divider />

                    <div style={{ textAlign: 'center' }}>
                        <Text type="secondary">
                            {t('legal.common.copyright', { year: new Date().getFullYear() })}
                        </Text>
                    </div>
                </Space>
            </Card>
        </div>
    );
};

export default PrivacyPolicy;
