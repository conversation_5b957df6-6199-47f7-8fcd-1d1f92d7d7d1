import React, { useContext, useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { API } from '../helpers';
import { UserContext } from '../context/User';
import { App } from 'antd';

const GoogleOAuth: React.FC = () => {
    const [searchParams] = useSearchParams();
    const [prompt, setPrompt] = useState('处理中...');
    const [userState, userDispatch] = useContext(UserContext);
    const { message: AntdMessage } = App.useApp();
    const navigate = useNavigate();

    const sendCode = async (code: string | null, state: string | null, count: number) => {
        try {
            console.log(`发送 Google OAuth 请求: code=${code}, state=${state}`);
            setPrompt(`正在处理 Google 登录请求...`);

            const res = await API.get(`/api/oauth/google?code=${code}&state=${state}`);
            console.log('Google OAuth 响应:', res);

            const {success, message, data, token} = res.data;
            if (success) {
                if (message === 'bind') {
                    AntdMessage.success('绑定成功！');
                    userDispatch({type: 'update', payload: data});
                } else {
                    userDispatch({type: 'login', payload: data});
                    token && localStorage.setItem('X-S-Token', token);
                    AntdMessage.success('欢迎使用', 0.8);
                    navigate('/token');
                }
            } else {
                console.error('Google OAuth 失败:', message);
                AntdMessage.error(message);
                if (count === 0) {
                    setPrompt(`操作失败，重定向至登录界面中...`);
                    navigate('/login'); // in case this is failed to bind Google
                    return;
                }
                count++;
                setPrompt(`出现错误，第 ${count} 次重试中...`);
                await new Promise((resolve) => setTimeout(resolve, count * 2000));
                await sendCode(code, state, count);
            }
        } catch (error) {
            console.error('Google OAuth 请求异常:', error);
            AntdMessage.error('Google 登录请求失败，请稍后重试');
            setPrompt(`请求异常，重定向至登录界面中...`);
            setTimeout(() => navigate('/login'), 2000);
        }
    };

    useEffect(() => {
        let code = searchParams.get('code');
        let state = searchParams.get('state');
        sendCode(code, state, 0).then();
    }, []);

    return (
        <div style={{minHeight: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Spin size='large' tip={prompt}/>
        </div>
    );
};

export default GoogleOAuth;
