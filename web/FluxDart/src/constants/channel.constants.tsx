import { SYSTEM_NAME } from './common.constant';

export const CHANNEL_TYPE_OPTIONS = [
  { key: 1, text: 'OpenAI', value: 1, color: '#000000' },
  { key: 7007, text: `Shell API`, value: 7007, color: '#ff8484', icon: 'icon-run_shell' },
  { key: 3, text: 'Microsoft Azure', value: 3, color: '#4BA4EA', icon: 'icon-microsoft-fill' },
  { key: 14, text: 'Anthropic Claude', value: 14, color: '#CD9B78', icon: 'icon-<PERSON>' },
    {key: 33, text: 'AWS Claude', value: 33, color: '#4e4540', icon: 'icon-<PERSON>'},
    { key: 42, text: 'VertexAI', value: 42, color: 'blue',icon: 'icon-Claude' },
  { key: 11, text: 'Google PaLM2', value: 11, color: '#648FF7', icon: 'icon-google' },
  { key: 24, text: 'Google Gemini', value: 24, color: '#58A65C', icon: 'icon-google' },
  { key: 48, text: 'xA<PERSON> Grok', value: 48, color: '#000000', icon: 'icon-robot' },
  // {key: 7006, text: 'MJ-OpenAI', value: 7006, color: '#161652', icon: 'icon-a-666'},
    {key: 7011, text: 'Midjourney', value: 7011, color: '#161652', icon: 'icon-a-666'},
    {key: 7013, text: 'Fish Audio', value: 7013, color: '#1E88E5', icon: 'icon-a-666'},
    {key: 40, text: '字节跳动豆包', value: 40, color: '#54B4F4'},
  { key: 19, text: '360智脑', value: 19, color: '#54B4F4', icon: 'icon-360logo' },
  { key: 23, text: '腾讯混元', value: 23, color: '#2151D1', icon: 'icon-qq' },
  { key: 15, text: '百度文心千帆', value: 15, color: '#3F6FED', icon: 'icon-baiduchatgpt' },
  { key: 17, text: '阿里通义千问', value: 17, color: '#5C57DD', icon: 'icon-tongyiqianwen' },
  { key: 18, text: '讯飞星火认知', value: 18, color: '#64CBF9', icon: 'icon-xunfeichatgpt' },
  { key: 16, text: '智谱AI', value: 16, color: '#3C6EE7', icon: 'icon-zhipuAI' },
  {key: 7005, text: 'Proxy API', value: 7005, color: '#C77BDC', icon: 'icon-phone-iphone'},
  // {key: 7001, text: 'Google Search', value: 7001, color: '#ffab3c', icon: 'icon-google'},
  // {key: 7008, text: 'Lobe-OpenAI', value: 7008, color: '#53479C', icon: 'icon-robot'},
  { key: 22, text: 'FastGPT', value: 22, color: '#5C7FF7', icon: 'icon-wenjianshu' },
  { key: 8, text: '自定义渠道', value: 8, color: '#9d9292', icon: 'icon-zidingyi' }
  // { key: 21, text: '知识库：AI Proxy', value: 21, color: 'purple',icon:"icon-wenjianshu" },
];

export const CHANNEL_BILLING_TYPE_OPTIONS = [
  { key: 1, text: '按量计费', value: 1, color: 'green' },
  { key: 2, text: '按次计费', value: 2, color: 'orange' },
  { key: 3, text: '混合模式', value: 3, color: 'orange' },
  { key: 4, text: '按量优先', value: 4, color: 'orange' },
  { key: 5, text: '按次优先', value: 5, color: 'orange' }
];

export const CHANNEL_BILLING_TYPE_OPTIONS_FOR_CHANNEL = [
  { key: 1, text: '按量计费', value: 1, color: 'green' },
  { key: 2, text: '按次计费', value: 2, color: 'orange' }
];