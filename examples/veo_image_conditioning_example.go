package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/songquanpeng/one-api/model"
)

// VeoImageConditioningExample 展示如何使用增强的垫图功能
func main() {
	fmt.Println("=== VEO 垫图功能示例 ===")

	// 示例1: 图片生成视频 (基础垫图)
	fmt.Println("\n1. 图片生成视频示例:")
	imageToVideoExample()

	// 示例2: 视频垫图 - 最后一帧
	fmt.Println("\n2. 视频垫图 - 最后一帧示例:")
	lastFrameExample()

	// 示例3: 视频延长
	fmt.Println("\n3. 视频延长示例:")
	videoExtensionExample()

	// 示例4: 组合垫图功能
	fmt.Println("\n4. 组合垫图功能示例:")
	combinedExample()
}

// imageToVideoExample 图片生成视频示例
func imageToVideoExample() {
	request := &model.VideoGenerationRequest{
		Model:           "veo-3.0-generate-preview",
		Prompt:          "让图片中的场景动起来，添加一些自然的动态效果，微风轻抚",
		DurationSeconds: 8,
		AspectRatio:     "16:9",
		SampleCount:     1,
		// 垫图功能 - 图片生成视频
		Image:         "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...", // base64编码的图片
		ImageMimeType: "image/jpeg",
		// 其他参数
		EnhancePrompt:    true,
		GenerateAudio:    true,
		AddWatermark:     true,
		IncludeRaiReason: true,
	}

	fmt.Printf("请求参数: %+v\n", request)

	// 创建任务
	task := &model.Task{
		TaskID:    fmt.Sprintf("img2video_%d", time.Now().Unix()),
		ChannelId: 1, // 示例渠道ID
		Status:    model.TaskStatusNotStart,
	}
	task.SetData(request)

	fmt.Printf("创建任务: %s\n", task.TaskID)
}

// lastFrameExample 视频垫图 - 最后一帧示例
func lastFrameExample() {
	request := &model.VideoGenerationRequest{
		Model:           "veo-3.0-generate-preview",
		Prompt:          "基于最后一帧图片，生成后续的视频内容，保持场景连贯性",
		DurationSeconds: 5,
		AspectRatio:     "16:9",
		SampleCount:     1,
		// 垫图功能 - 视频垫图最后一帧
		LastFrame:         "gs://my-bucket/last-frame.jpg", // GCS URI
		LastFrameMimeType: "image/jpeg",
		// 其他参数
		EnhancePrompt:    true,
		GenerateAudio:    false,
		AddWatermark:     true,
		IncludeRaiReason: true,
	}

	fmt.Printf("请求参数: %+v\n", request)

	// 创建任务
	task := &model.Task{
		TaskID:    fmt.Sprintf("lastframe_%d", time.Now().Unix()),
		ChannelId: 1,
		Status:    model.TaskStatusNotStart,
	}
	task.SetData(request)

	fmt.Printf("创建任务: %s\n", task.TaskID)
}

// videoExtensionExample 视频延长示例
func videoExtensionExample() {
	request := &model.VideoGenerationRequest{
		Model:           "veo-3.0-generate-preview",
		Prompt:          "延长现有视频，保持动作和场景的连贯性",
		DurationSeconds: 10,
		AspectRatio:     "16:9",
		SampleCount:     1,
		// 垫图功能 - 视频延长
		Video:         "data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28y...", // base64编码的视频
		VideoMimeType: "video/mp4",
		// 其他参数
		EnhancePrompt:    true,
		GenerateAudio:    true,
		AddWatermark:     true,
		IncludeRaiReason: true,
	}

	fmt.Printf("请求参数: %+v\n", request)

	// 创建任务
	task := &model.Task{
		TaskID:    fmt.Sprintf("videoext_%d", time.Now().Unix()),
		ChannelId: 1,
		Status:    model.TaskStatusNotStart,
	}
	task.SetData(request)

	fmt.Printf("创建任务: %s\n", task.TaskID)
}

// combinedExample 组合垫图功能示例
func combinedExample() {
	request := &model.VideoGenerationRequest{
		Model:           "veo-3.0-generate-preview",
		Prompt:          "结合图片和视频内容，创建一个连贯的视频序列",
		DurationSeconds: 12,
		AspectRatio:     "16:9",
		SampleCount:     1,
		// 组合垫图功能 - 同时使用图片和最后一帧
		Image:             "gs://my-bucket/reference-image.jpg",
		ImageMimeType:     "image/jpeg",
		LastFrame:         "gs://my-bucket/last-frame.png",
		LastFrameMimeType: "image/png",
		// 高级参数
		NegativePrompt:   "模糊，低质量，不连贯",
		Seed:             intPtr(12345),
		EnhancePrompt:    true,
		GenerateAudio:    true,
		AddWatermark:     false,
		IncludeRaiReason: true,
		PersonGeneration: "allow_adult",
	}

	fmt.Printf("请求参数: %+v\n", request)

	// 演示异步任务处理
	demonstrateAsyncProcessing(request)
}

// demonstrateAsyncProcessing 演示异步任务处理流程
func demonstrateAsyncProcessing(request *model.VideoGenerationRequest) {
	fmt.Println("\n=== 异步任务处理流程演示 ===")

	// 1. 创建任务
	task := &model.Task{
		TaskID:    fmt.Sprintf("combined_%d", time.Now().Unix()),
		ChannelId: 1,
		Status:    model.TaskStatusNotStart,
		StartTime: time.Now().Unix(),
	}
	task.SetData(request)

	fmt.Printf("1. 创建任务: %s\n", task.TaskID)

	// 2. 模拟任务执行过程
	fmt.Println("2. 开始任务执行...")

	// 模拟不同阶段的进度更新
	stages := []struct {
		status   model.TaskStatus
		progress string
		message  string
	}{
		{model.TaskStatusSubmitted, "10%", "任务已提交到VEO API"},
		{model.TaskStatusInProgress, "25%", "开始处理垫图内容"},
		{model.TaskStatusInProgress, "45%", "分析图片和视频内容"},
		{model.TaskStatusInProgress, "65%", "生成视频帧"},
		{model.TaskStatusInProgress, "85%", "后处理和优化"},
		{model.TaskStatusInProgress, "95%", "准备输出结果"},
		{model.TaskStatusSuccess, "100%", "任务完成"},
	}

	for i, stage := range stages {
		time.Sleep(500 * time.Millisecond) // 模拟处理时间

		task.Status = stage.status
		task.Progress = stage.progress

		fmt.Printf("   阶段 %d: %s - %s (%s)\n", i+1, string(stage.status), stage.progress, stage.message)

		if stage.status == model.TaskStatusSuccess {
			// 模拟成功结果
			result := map[string]interface{}{
				"id":      task.TaskID,
				"object":  "video.generation",
				"created": time.Now().Unix(),
				"model":   "veo-3.0-generate-preview",
				"status":  "completed",
				"data": []map[string]interface{}{
					{
						"url":                         "https://storage.googleapis.com/veo-results/video123.mp4",
						"mime_type":                   "video/mp4",
						"supports_image_conditioning": true,
					},
				},
				"video_count":                 1,
				"supports_image_conditioning": true,
			}

			resultBytes, _ := json.MarshalIndent(result, "", "  ")
			task.SetData(result)
			task.FinishTime = time.Now().Unix()

			fmt.Printf("3. 任务结果:\n%s\n", string(resultBytes))
		}
	}

	fmt.Println("=== 任务处理完成 ===")
}

// intPtr 返回int指针
func intPtr(i int) *int {
	return &i
}

// 使用说明和最佳实践
func printUsageGuide() {
	fmt.Println(`
=== VEO 垫图功能使用指南 ===

1. 图片生成视频 (Image-to-Video):
   - 使用 Image 字段提供参考图片
   - 支持 base64 编码或 GCS URI
   - 适用于静态图片动画化

2. 视频垫图 - 最后一帧 (Last Frame):
   - 使用 LastFrame 字段提供最后一帧图片
   - 用于视频间的平滑过渡
   - 保持场景连贯性

3. 视频延长 (Video Extension):
   - 使用 Video 字段提供现有视频
   - 延长视频长度，保持动作连贯
   - 支持 base64 编码或 GCS URI

4. 最佳实践:
   - 合理设置 DurationSeconds (5-15秒)
   - 使用 EnhancePrompt 提升效果
   - 设置合适的 AspectRatio
   - 使用 NegativePrompt 避免不良内容
   - 考虑使用 Seed 确保可重现性

5. 异步处理优势:
   - 支持长时间视频生成
   - 智能进度跟踪
   - 错误重试机制
   - 渠道切换支持
`)
}
