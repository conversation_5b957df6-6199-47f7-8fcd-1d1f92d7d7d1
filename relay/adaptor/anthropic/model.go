package anthropic

import (
	"encoding/json"

	"github.com/songquanpeng/one-api/relay/model"
)

// https://docs.anthropic.com/claude/reference/messages_post

type Metadata struct {
	UserId string `json:"user_id"`
}

type ImageSource struct {
	Type      string `json:"type"`
	MediaType string `json:"media_type"`
	Data      string `json:"data"`
}

type Content struct {
	Type      string       `json:"type"`
	Text      string       `json:"text,omitempty"`
	Thinking  string       `json:"thinking,omitempty"`
	Signature string       `json:"signature,omitempty"`
	Source    *ImageSource `json:"source,omitempty"`
	// tool_calls
	Id        string `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	Input     any    `json:"input,omitempty"`
	Content   string `json:"content,omitempty"`
	ToolUseId string `json:"tool_use_id,omitempty"`
}

type Message struct {
	Role    string    `json:"role"`
	Content []Content `json:"content"`
}

type Tool struct {
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	InputSchema InputSchema `json:"input_schema"`
}

type InputSchema struct {
	Type       string `json:"type"`
	Properties any    `json:"properties,omitempty"`
	Required   any    `json:"required,omitempty"`
}

type Request struct {
	Model         string    `json:"model"`
	Messages      []Message `json:"messages"`
	System        string    `json:"system,omitempty"`
	MaxTokens     int       `json:"max_tokens,omitempty"`
	StopSequences []string  `json:"stop_sequences,omitempty"`
	Stream        bool      `json:"stream,omitempty"`
	Temperature   *float64  `json:"temperature,omitempty"`
	TopP          *float64  `json:"top_p,omitempty"`
	TopK          int       `json:"top_k,omitempty"`
	Tools         []Tool    `json:"tools,omitempty"`
	ToolChoice    any       `json:"tool_choice,omitempty"`
	Thinking      *Thinking `json:"thinking,omitempty"`
	//Metadata    `json:"metadata,omitempty"`
}

type FlexibleMessageRequest struct {
	Model         string            `json:"model"`
	Messages      []FlexibleMessage `json:"messages"`
	System        string            `json:"system,omitempty"`
	MaxTokens     int               `json:"max_tokens,omitempty"`
	StopSequences []string          `json:"stop_sequences,omitempty"`
	Stream        bool              `json:"stream,omitempty"`
	Temperature   float64           `json:"temperature,omitempty"`
	TopP          float64           `json:"top_p,omitempty"`
	TopK          int               `json:"top_k,omitempty"`
	Tools         []Tool            `json:"tools,omitempty"`
	ToolChoice    any               `json:"tool_choice,omitempty"`
	//Metadata    `json:"metadata,omitempty"`
}

type Usage struct {
	InputTokens              int    `json:"input_tokens"`
	OutputTokens             int    `json:"output_tokens"`
	CacheCreationInputTokens int    `json:"cache_creation_input_tokens,omitempty"`
	CacheReadInputTokens     int    `json:"cache_read_input_tokens,omitempty"`
	ServiceTier              string `json:"service_tier,omitempty"`
}

type Error struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

type Response struct {
	Id           string    `json:"id"`
	Type         string    `json:"type"`
	Role         string    `json:"role"`
	Content      []Content `json:"content"`
	Model        string    `json:"model"`
	StopReason   *string   `json:"stop_reason"`
	StopSequence *string   `json:"stop_sequence"`
	Usage        Usage     `json:"usage"`
	Error        *Error    `json:"error,omitempty"`
}

type Delta struct {
	Type         string  `json:"type"`
	Text         string  `json:"text,omitempty"`
	Thinking     string  `json:"thinking,omitempty"`
	Signature    string  `json:"signature,omitempty"`
	PartialJson  string  `json:"partial_json,omitempty"`
	StopReason   *string `json:"stop_reason,omitempty"`
	StopSequence *string `json:"stop_sequence,omitempty"`
}

type StreamResponse struct {
	Type         string    `json:"type"` // message_start, content_block_start, content_block_delta, content_block_stop, message_delta, message_stop, ping, error
	Message      *Response `json:"message,omitempty"`
	Index        int       `json:"index,omitempty"`
	ContentBlock *Content  `json:"content_block,omitempty"`
	Delta        *Delta    `json:"delta,omitempty"`
	Usage        *Usage    `json:"usage,omitempty"`
	Error        *Error    `json:"error,omitempty"`
}

type ContentBlock struct {
	Type     string `json:"type"`
	Thinking string `json:"thinking,omitempty"`
}

type StreamDelta struct {
	Type     string `json:"type"`
	Text     string `json:"text,omitempty"`
	Thinking string `json:"thinking,omitempty"`
}

type FlexibleContent struct {
	Type      string       `json:"type,omitempty"`
	Text      string       `json:"text,omitempty"`
	Source    *ImageSource `json:"source,omitempty"`
	Id        string       `json:"id,omitempty"`
	Name      string       `json:"name,omitempty"`
	Input     interface{}  `json:"input,omitempty"`
	Content   string       `json:"content,omitempty"`
	ToolUseId string       `json:"tool_use_id,omitempty"`
}

type FlexibleMessage struct {
	Role    string          `json:"role"`
	Content json.RawMessage `json:"content"`
}

// 添加 UnmarshalContent 方法
func (m *FlexibleMessage) UnmarshalContent() (interface{}, error) {
	var stringContent string
	err := json.Unmarshal(m.Content, &stringContent)
	if err == nil {
		return stringContent, nil
	}

	var arrayContent []FlexibleContent
	err = json.Unmarshal(m.Content, &arrayContent)
	if err == nil {
		return arrayContent, nil
	}

	return nil, err
}

type FlexibleClaudeRequest struct {
	Model         string            `json:"model"`
	Messages      []FlexibleMessage `json:"messages"`
	System        string            `json:"system,omitempty"`
	MaxTokens     int               `json:"max_tokens,omitempty"`
	StopSequences []string          `json:"stop_sequences,omitempty"`
	Stream        bool              `json:"stream,omitempty"`
	Temperature   float64           `json:"temperature,omitempty"`
	TopP          float64           `json:"top_p,omitempty"`
	TopK          int               `json:"top_k,omitempty"`
	Tools         []Tool            `json:"tools,omitempty"`
	ToolChoice    interface{}       `json:"tool_choice,omitempty"`
	Thinking      *Thinking         `json:"thinking,omitempty"`
}

// 添加一个新的方法来转换 Tools
func (fcr *FlexibleClaudeRequest) ConvertTools() []model.Tool {
	tools := make([]model.Tool, len(fcr.Tools))
	for i, tool := range fcr.Tools {
		tools[i] = model.Tool{
			Type: "function",
			Function: model.Function{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters: map[string]interface{}{
					"type":       tool.InputSchema.Type,
					"properties": tool.InputSchema.Properties,
					"required":   tool.InputSchema.Required,
				},
			},
		}
	}
	return tools
}

// 添加Thinking结构体用于Claude 3.7
type Thinking struct {
	Type         string `json:"type,omitempty"`
	BudgetTokens int    `json:"budget_tokens,omitempty"`
}
