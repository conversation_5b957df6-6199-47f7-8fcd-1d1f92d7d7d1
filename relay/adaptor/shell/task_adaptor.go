package shell

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/dto"
	"github.com/songquanpeng/one-api/relay/channel"
	relaycommon "github.com/songquanpeng/one-api/relay/common"
	"github.com/songquanpeng/one-api/service"
)

// TaskAdaptor ShellAPI任务适配器 - 用于链式调用场景
type TaskAdaptor struct {
	baseURL string
	apiKey  string
}

// Init 初始化适配器
func (a *TaskAdaptor) Init(info *relaycommon.TaskRelayInfo) {
	a.baseURL = info.BaseUrl
	a.apiKey = info.ApiKey
}

// ValidateRequestAndSetAction 验证请求并设置动作
func (a *TaskAdaptor) ValidateRequestAndSetAction(c *gin.Context, info *relaycommon.TaskRelayInfo) *dto.TaskError {
	// 对于ShellAPI，我们直接转发请求，不需要特殊验证
	// 设置默认动作
	info.Action = "generate"

	// 添加调试日志
	fmt.Printf("ShellAPI TaskAdaptor: ValidateRequestAndSetAction called, channelType=%d\n", info.ChannelType)

	return nil
}

// BuildRequestURL 构建请求URL
func (a *TaskAdaptor) BuildRequestURL(info *relaycommon.TaskRelayInfo) (string, error) {
	// 构建上游API的URL
	baseURL := strings.TrimSuffix(a.baseURL, "/")
	return fmt.Sprintf("%s/v1/video/generations", baseURL), nil
}

// BuildRequestHeader 构建请求头
func (a *TaskAdaptor) BuildRequestHeader(c *gin.Context, req *http.Request, info *relaycommon.TaskRelayInfo) error {
	// 设置基本请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+a.apiKey)
	req.Header.Set("User-Agent", "shell-api-client/1.0")

	// 传递用户相关的头部信息（用于链式调用中的用户识别）
	if userID := c.GetHeader("User-Id"); userID != "" {
		req.Header.Set("User-Id", userID)
	}
	if apiUser := c.GetHeader("Api-User"); apiUser != "" {
		req.Header.Set("Api-User", apiUser)
	}
	if rixUser := c.GetHeader("Rix-Api-User"); rixUser != "" {
		req.Header.Set("Rix-Api-User", rixUser)
	}

	return nil
}

// BuildRequestBody 构建请求体
func (a *TaskAdaptor) BuildRequestBody(c *gin.Context, info *relaycommon.TaskRelayInfo) (io.Reader, error) {
	// 直接转发原始请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, fmt.Errorf("读取请求体失败: %w", err)
	}

	// 重置请求体以供后续使用
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	return bytes.NewReader(body), nil
}

// DoRequest 执行请求
func (a *TaskAdaptor) DoRequest(c *gin.Context, info *relaycommon.TaskRelayInfo, requestBody io.Reader) (*http.Response, error) {
	return channel.DoTaskApiRequest(a, c, info, requestBody)
}

// DoResponse 处理响应
func (a *TaskAdaptor) DoResponse(c *gin.Context, resp *http.Response, info *relaycommon.TaskRelayInfo) (taskID string, taskData []byte, taskErr *dto.TaskError) {
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		taskErr = service.TaskErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError)
		return
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		taskErr = service.TaskErrorWrapper(fmt.Errorf("upstream error: %s", string(responseBody)), "upstream_error", resp.StatusCode)
		return
	}

	// 添加调试日志
	fmt.Printf("Shell TaskAdaptor DoResponse: responseBody length=%d, content=%s\n", len(responseBody), string(responseBody))

	// 处理空响应体的情况
	if len(responseBody) == 0 {
		taskErr = service.TaskErrorWrapper(fmt.Errorf("empty response body from upstream"), "empty_response_body", http.StatusInternalServerError)
		return
	}

	// 解析响应以获取任务ID
	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		taskErr = service.TaskErrorWrapper(fmt.Errorf("failed to unmarshal response: %w, body: %s", err, string(responseBody)), "unmarshal_response_body_failed", http.StatusInternalServerError)
		return
	}

	// 尝试从不同的字段中获取任务ID
	if id, ok := response["id"].(string); ok {
		taskID = id
	} else if id, ok := response["task_id"].(string); ok {
		taskID = id
	} else if id, ok := response["taskId"].(string); ok {
		taskID = id
	} else {
		taskErr = service.TaskErrorWrapper(fmt.Errorf("task ID not found in response, available fields: %v", getMapKeys(response)), "task_id_not_found", http.StatusInternalServerError)
		return
	}

	// 添加调试日志
	fmt.Printf("Shell TaskAdaptor: extracted taskID=%s\n", taskID)

	// 返回任务ID给客户端（参考vertex.TaskAdaptor的实现）
	c.JSON(http.StatusOK, gin.H{
		"task_id": taskID,
	})

	taskData = responseBody
	return
}

// getMapKeys 获取map的所有键
func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// FetchTask 获取任务状态
func (a *TaskAdaptor) FetchTask(baseUrl, key string, body map[string]any) (*http.Response, error) {
	taskID, ok := body["task_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid task_id")
	}

	// 构建获取任务状态的URL
	baseUrl = strings.TrimSuffix(baseUrl, "/")
	url := fmt.Sprintf("%s/v1/tasks/%s", baseUrl, taskID)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+key)
	req.Header.Set("User-Agent", "shell-api-client/1.0")

	return http.DefaultClient.Do(req)
}

// ParseTaskResult 解析任务结果
func (a *TaskAdaptor) ParseTaskResult(respBody []byte) (*relaycommon.TaskInfo, error) {
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	taskInfo := &relaycommon.TaskInfo{}

	// 解析任务ID
	if id, ok := response["id"].(string); ok {
		taskInfo.TaskID = id
	} else if id, ok := response["task_id"].(string); ok {
		taskInfo.TaskID = id
	}

	// 解析任务状态 - 使用与model.TaskStatus一致的大写格式
	if status, ok := response["status"].(string); ok {
		switch strings.ToLower(status) {
		case "pending", "queued", "submitted":
			taskInfo.Status = "SUBMITTED"
		case "processing", "running", "in_progress":
			taskInfo.Status = "IN_PROGRESS"
		case "completed", "success", "succeeded":
			taskInfo.Status = "SUCCESS"
		case "failed", "error":
			taskInfo.Status = "FAILURE"
		default:
			// 对于未知状态，尝试转换为大写
			taskInfo.Status = strings.ToUpper(status)
		}
	}

	// 解析错误信息
	if errorMsg, ok := response["error"].(string); ok {
		taskInfo.Reason = errorMsg
	} else if errorObj, ok := response["error"].(map[string]interface{}); ok {
		if msg, ok := errorObj["message"].(string); ok {
			taskInfo.Reason = msg
		}
	}

	// 解析结果URL
	if url, ok := response["url"].(string); ok {
		taskInfo.Url = url
	} else if url, ok := response["result_url"].(string); ok {
		taskInfo.Url = url
	}

	return taskInfo, nil
}

// GetModelList 获取模型列表
func (a *TaskAdaptor) GetModelList() []string {
	return []string{
		"veo-3.0-generate-preview",
		"veo-2.0-generate",
		"video-generation",
	}
}

// GetChannelName 获取渠道名称
func (a *TaskAdaptor) GetChannelName() string {
	return "shell-api"
}
