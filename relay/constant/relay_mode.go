package constant

import (
	"strings"

	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/model"

	"github.com/gin-gonic/gin"
)

const (
	RelayModeUnknown = iota
	RelayModeChatCompletions
	RelayModeCompletions
	RelayModeEmbeddings
	RelayModeModerations
	RelayModeImagesGenerations
	RelayModeImagesEdits
	RelayModeEdits
	RelayModeAudioSpeech
	RelayModeAudioTranscription
	RelayModeAudioTranslation
	RelayModeMidjourneyImagine
	RelayModeMidjourneyChange
	RelayModeMidjourneyNotify
	RelayModeMidjourneyTaskFetch
	RelayModeMidjourneyDescribe
	RelayModeMidjourneyBlend
	RelayModeMidjourneyAction
	RelayModeMidjourneyModal
	RelayModeMidjourneyShorten
	RelayModeMidjourneyInsightFaceSwap
	RelayModeMidjourneyChangeSimple
	RelayModeMidjourneyTaskImageSeed
	RelayModeMidjourneyTaskFetchByCondition
	RelayModeUploadDiscordImages
	RelayModeMidjourneyVideo
	RelayModeMidjourneyEdits
	RelayModeResponses
	RelayModeGetResponse
	RelayModeVideoGenerations
	RelayModeVertexVideoGenerations
	RelayModeVertexVideoFetchByID
)

const (
	RelayModeSearchSerper = iota + 7001
	RelayModeBingXMY
	RelayModeVisionPreviewChatCompletions
	_
	RelayModeGPTGodChatCompletions
	RelayModeOpenaiMJChatCompletions
	RelayModeOpenaiDalleChatCompletions
	RelayModeOpenaiLobeChatCompletions
	RelayModeOpenaiSoraChatCompletions
	RelayModeOpenaiNetChatCompletions
	_
	RelayModeOpenaiSunoChatCompletions
	RelayModeOpenaiSerperSearchCompletions
	RelayModeOpenaiImageChatCompletions // For gpt-image-1 model
)

func Path2RelayMode(path string, c *gin.Context) int {
	relayMode := RelayModeUnknown
	if strings.HasPrefix(path, "/v1/chat/completions") {
		relayMode = RelayModeChatCompletions
		// midjourney-fast,midjourney-relax,midjourney-turbo,mj-fast,mj-turbo,mj-relax
		if c.GetString(ctxkey.RequestModel) == "mj" || c.GetString(ctxkey.RequestModel) == "midjourney" ||
			c.GetString(ctxkey.RequestModel) == "midjourney-fast" || c.GetString(ctxkey.RequestModel) == "midjourney-relax" ||
			c.GetString(ctxkey.RequestModel) == "midjourney-turbo" || c.GetString(ctxkey.RequestModel) == "mj-fast" ||
			c.GetString(ctxkey.RequestModel) == "mj-turbo" || c.GetString(ctxkey.RequestModel) == "mj-relax" {
			relayMode = RelayModeOpenaiMJChatCompletions
		} else if c.GetString(ctxkey.RequestModel) == "dall-e-3" || c.GetString(ctxkey.RequestModel) == "dall-e-2" {
			relayMode = RelayModeOpenaiDalleChatCompletions
		} else if isImageChatConversionModel(c, c.GetString(ctxkey.RequestModel)) {
			relayMode = RelayModeOpenaiImageChatCompletions
		} else if isSunoChatConversionModel(c, c.GetString(ctxkey.RequestModel)) {
			relayMode = RelayModeOpenaiSunoChatCompletions
		} else if c.GetString(ctxkey.RequestModel) == "sora-1.0-turbo" {
			relayMode = RelayModeOpenaiSoraChatCompletions
		} else if strings.HasPrefix(c.GetString(ctxkey.RequestModel), "net-") {
			// net- 前缀的模型名 需要联网
			relayMode = RelayModeOpenaiNetChatCompletions
		} else if strings.HasPrefix(c.GetString(ctxkey.RequestModel), "suno") && c.GetInt("channel") == RelayModeOpenaiSunoChatCompletions {
			// suno需要区分channel类型是suno chat 才转换这种,否则会被认为是openai chat
			relayMode = RelayModeOpenaiSunoChatCompletions
		} else if strings.HasPrefix(c.GetString(ctxkey.RequestModel), "search-serper") {
			relayMode = RelayModeOpenaiSerperSearchCompletions
		}
	} else if strings.HasPrefix(path, "/v1/messages") {
		relayMode = RelayModeChatCompletions
	} else if strings.HasPrefix(path, "/v1/completions") {
		relayMode = RelayModeCompletions
	} else if strings.HasPrefix(path, "/v1/embeddings") {
		relayMode = RelayModeEmbeddings
	} else if strings.HasSuffix(path, "embeddings") {
		relayMode = RelayModeEmbeddings
	} else if strings.HasPrefix(path, "/v1/moderations") {
		relayMode = RelayModeModerations
	} else if strings.HasPrefix(path, "/v1/images/generations") {
		relayMode = RelayModeImagesGenerations
	} else if strings.HasPrefix(path, "/v1/images/edits") {
		relayMode = RelayModeImagesEdits
	} else if strings.HasPrefix(path, "/v1/videos/generations") {
		relayMode = RelayModeVideoGenerations

	} else if strings.HasPrefix(path, "/v1/edits") {
		relayMode = RelayModeEdits
	} else if strings.HasPrefix(path, "/v1/audio/speech") {
		relayMode = RelayModeAudioSpeech
	} else if strings.HasPrefix(path, "/v1/audio/transcriptions") {
		relayMode = RelayModeAudioTranscription
	} else if strings.HasPrefix(path, "/v1/audio/translations") {
		relayMode = RelayModeAudioTranslation
	} else if strings.HasPrefix(path, "/search/serper") {
		relayMode = RelayModeSearchSerper
	} else if strings.HasPrefix(path, "/v1/responses") {
		// 新增对 /v1/responses 路径的处理
		if c.Request.Method == "POST" {
			relayMode = RelayModeResponses
		} else if c.Request.Method == "GET" {
			relayMode = RelayModeGetResponse
		}
	}
	if c.GetInt("channel") == RelayModeGPTGodChatCompletions {
		relayMode = RelayModeGPTGodChatCompletions
	}
	if c.GetInt("channel") == RelayModeOpenaiLobeChatCompletions {
		relayMode = RelayModeOpenaiLobeChatCompletions
	}
	return relayMode
}

// isImageChatConversionModel 检查指定模型是否支持图片聊天转换
func isImageChatConversionModel(c *gin.Context, modelName string) bool {
	// 获取渠道ID
	channelId := c.GetInt(ctxkey.ChannelId)
	if channelId == 0 {
		return false
	}

	// 获取渠道扩展配置
	channelEx, err := model.CacheGetChannelExByChannelId(channelId)
	if err != nil {
		return false
	}

	// 检查是否启用了图片聊天转换
	if channelEx == nil || !channelEx.ImageChatConversionEnabled {
		return false
	}

	// 如果模型列表为空，则表示支持所有模型
	if channelEx.ImageChatConversionModels == "" {
		return true
	}

	// 检查模型是否在支持列表中
	supportedModels := strings.Split(channelEx.ImageChatConversionModels, ",")
	for _, supportedModel := range supportedModels {
		if strings.TrimSpace(supportedModel) == modelName {
			return true
		}
	}

	return false
}

// isSunoChatConversionModel 检查指定模型是否支持Suno聊天转换
func isSunoChatConversionModel(c *gin.Context, modelName string) bool {
	// 获取渠道ID
	channelId := c.GetInt(ctxkey.ChannelId)
	if channelId == 0 {
		return false
	}

	// 获取渠道扩展配置
	channelEx, err := model.CacheGetChannelExByChannelId(channelId)
	if err != nil {
		return false
	}

	// 检查是否启用了Suno聊天转换
	if channelEx == nil || !channelEx.SunoChatConversionEnabled {
		return false
	}

	// 如果模型列表为空，则表示支持所有模型
	if channelEx.SunoChatConversionModels == "" {
		return true
	}

	// 检查模型是否在支持列表中
	supportedModels := strings.Split(channelEx.SunoChatConversionModels, ",")
	for _, supportedModel := range supportedModels {
		if strings.TrimSpace(supportedModel) == modelName {
			return true
		}
	}

	return false
}
