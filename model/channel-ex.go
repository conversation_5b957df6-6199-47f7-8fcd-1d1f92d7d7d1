package model

import (
	"encoding/json"
	"fmt"

	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

type ChannelExtend struct {
	Id                              int     `json:"id" bson:"id" gorm:"primaryKey"`
	ChannelId                       int     `json:"channel_id" bson:"channel_id" gorm:"column:channel_id;index"`
	FilterStreamAd                  bool    `json:"filter_stream_ad" bson:"filter_stream_ad" gorm:"default:0;column:filter_stream_ad"`                                                          // 是否过滤流式广告
	FilterStreamAdMinSize           int     `json:"filter_stream_ad_min_size" bson:"filter_stream_ad_min_size" gorm:"type:int;default:10;column:filter_stream_ad_min_size"`                     // 过滤流式广告的最小数据块大小(默认10)
	FilterNonStreamAd               bool    `json:"filter_non_stream_ad" bson:"filter_non_stream_ad" gorm:"default:0;column:filter_non_stream_ad"`                                              // 是否过滤非流式广告
	FilterNonStreamAdRegex          string  `json:"filter_non_stream_ad_regex" bson:"filter_non_stream_ad_regex" gorm:"type:varchar(255);column:filter_non_stream_ad_regex"`                    // 过滤非流式广告的正则表达式
	FilterSystemPrompt              bool    `json:"filter_system_prompt" bson:"filter_system_prompt" gorm:"default:0;column:filter_system_prompt"`                                              // 是否屏蔽系统预设提示词
	CustomSystemPrompt              string  `json:"custom_system_prompt" bson:"custom_system_prompt" gorm:"type:text;column:custom_system_prompt"`                                              // 自定义系统提示词
	PlatformAccessToken             string  `json:"platform_access_token" bson:"platform_access_token" gorm:"type:varchar(255);column:platform_access_token"`                                   // ShellAPI渠道系统访问令牌
	UpstreamUserId                  string  `json:"upstream_user_id" bson:"upstream_user_id" gorm:"type:varchar(255);column:upstream_user_id"`                                                  // 上游用户ID
	ExtraHeaders                    *string `json:"extra_headers" bson:"extra_headers" gorm:"type:text;column:extra_headers"`                                                                   // 额外请求头
	ParseUrlToContent               bool    `json:"parse_url_to_content" bson:"parse_url_to_content" gorm:"default:0;column:parse_url_to_content"`                                              // 是否强制自动解析url中文件的文本内容带入到请求参数中
	ParseUrlPrefixEnabled           bool    `json:"parse_url_prefix_enabled" bson:"parse_url_prefix_enabled" gorm:"default:0;column:parse_url_prefix_enabled"`                                  // 是否启用自定义url前缀解析器,开启后,默认content开头以parse_url则解析
	ParseUrlPrefix                  string  `json:"parse_url_prefix" bson:"parse_url_prefix" gorm:"type:varchar(255);column:parse_url_prefix"`                                                  // content开头以xxx则解析带入到上下文
	CustomFullUrlEnabled            bool    `json:"custom_full_url_enabled" bson:"custom_full_url_enabled" gorm:"default:0;column:custom_full_url_enabled"`                                     // 是否完全自定义url,默认false,如果为true后面不拼接/v1/chat/completions
	ArrangeMessages                 bool    `json:"arrange_messages" bson:"arrange_messages" gorm:"default:0;column:arrange_messages"`                                                          // 是否整理消息
	OriginalModelPricing            bool    `json:"original_model_pricing" bson:"original_model_pricing" gorm:"default:0;column:original_model_pricing"`                                        // 是否使用映射之前的原始模型计价
	NegativeOptimizationEnabled     bool    `json:"negative_optimization_enabled" bson:"negative_optimization_enabled" gorm:"default:0;column:negative_optimization_enabled"`                   // 是否开启渠道负优化,默认不开启
	NegativeOptimizationTime        int64   `json:"negative_optimization_time" bson:"negative_optimization_time" gorm:"type:int;default:0;column:negative_optimization_time"`                   // 负优化时差(单位毫秒)
	NegativeRandomOffset            int64   `json:"negative_random_offset" bson:"negative_random_offset" gorm:"type:int;default:0;column:negative_random_offset"`                               // 负优化时随机偏移(单位毫秒)
	OriginalModelFakeRespEnabled    bool    `json:"original_model_fake_resp_enabled" bson:"original_model_fake_resp_enabled" gorm:"default:0;column:original_model_fake_resp_enabled"`          // 是否根据映射前的模型伪造响应
	FakeCompletionIdEnabled         bool    `json:"fake_completion_id_enabled" bson:"fake_completion_id_enabled" gorm:"default:0;column:fake_completion_id_enabled"`                            // 是否伪造completionId
	ExcludeCustomPromptCostEnabled  bool    `json:"exclude_custom_prompt_cost_enabled" bson:"exclude_custom_prompt_cost_enabled" gorm:"default:0;column:exclude_custom_prompt_cost_enabled"`    // 排除自定义提示词的计费
	ForceChatUrlEnabled             bool    `json:"force_chat_url_enabled" bson:"force_chat_url_enabled" gorm:"default:0;column:force_chat_url_enabled"`                                        // 请求时强制将/v1/messages替换为/v1/chat/completions
	IgnoreFcTcEnabled               bool    `json:"ignore_fc_tc_enabled" bson:"ignore_fc_tc_enabled" gorm:"default:0;column:ignore_fc_tc_enabled"`                                              // 忽略function/tools
	ChannelTimeoutBreakerTime       int64   `json:"channel_timeout_breaker_time" bson:"channel_timeout_breaker_time" gorm:"type:int;default:0;column:channel_timeout_breaker_time"`             // 渠道请求超时熔断时间单位秒,0或者负数为不启用此功能
	UsageRecalculationEnabled       bool    `json:"usage_recalculation_enabled" bson:"usage_recalculation_enabled" gorm:"default:0;column:usage_recalculation_enabled"`                         // usage重新计算,不信任上游返回的usage,自己重新计算,会消耗更多系统资源
	EmptyResponseErrorEnabled       bool    `json:"empty_response_error_enabled" bson:"empty_response_error_enabled" gorm:"default:0;column:empty_response_error_enabled"`                      // 空返回值报错
	RemoveImageDownloadErrorEnabled bool    `json:"remove_image_download_error_enabled" bson:"remove_image_download_error_enabled" gorm:"default:0;column:remove_image_download_error_enabled"` // 图片下载报错则从请求中移除
	Base64ImagePrefixMapping        *string `json:"base64_image_prefix_mapping" bson:"base64_image_prefix_mapping" gorm:"type:text;column:base64_image_prefix_mapping"`                         // base64图片前缀映射
	RequestTokenLimitEnabled        bool    `json:"request_token_limit_enabled" bson:"request_token_limit_enabled" gorm:"default:0;column:request_token_limit_enabled"`                         // 是否开启渠道请求Token限制
	MinRequestTokenCount            int64   `json:"min_request_token_count" bson:"min_request_token_count" gorm:"type:int;default:0;column:min_request_token_count"`                            // 最小请求Token数
	MaxRequestTokenCount            int64   `json:"max_request_token_count" bson:"max_request_token_count" gorm:"type:int;default:0;column:max_request_token_count"`                            // 最大请求Token数
	ClaudeStreamEnabled             bool    `json:"claude_stream_enabled" bson:"claude_stream_enabled" gorm:"default:0;column:claude_stream_enabled"`                                           // 是否开启claude流式
	KeywordErrorEnabled             bool    `json:"keyword_error_enabled" bson:"keyword_error_enabled" gorm:"default:0;column:keyword_error_enabled"`                                           // 关键词报错
	KeywordError                    string  `json:"keyword_error" bson:"keyword_error" gorm:"type:text;column:keyword_error"`
	TransparentProxyEnabled         bool    `json:"transparent_proxy_enabled" bson:"transparent_proxy_enabled" gorm:"default:0;column:transparent_proxy_enabled"`             // 透明代理模式开启,暂时仅对anthropic类型渠道有效,后期考虑是否开放
	ForceO1StreamEnabled            bool    `json:"force_o1_stream_enabled" bson:"force_o1_stream_enabled" gorm:"default:0;column:force_o1_stream_enabled"`                   // 是否强制O1流式输出
	CostPerUnit                     float64 `json:"cost_per_unit" bson:"cost_per_unit" gorm:"type:decimal(10,6);default:0;column:cost_per_unit"`                              // 每单位成本(每1美元的成本)
	ThinkTagProcessingEnabled       bool    `json:"think_tag_processing_enabled" bson:"think_tag_processing_enabled" gorm:"default:0;column:think_tag_processing_enabled"`    // 是否启用<think>标签处理
	ImageChatConversionEnabled      bool    `json:"image_chat_conversion_enabled" bson:"image_chat_conversion_enabled" gorm:"default:0;column:image_chat_conversion_enabled"` // 是否支持chat格式到image格式的转换
	ImageChatConversionModels       string  `json:"image_chat_conversion_models" bson:"image_chat_conversion_models" gorm:"type:text;column:image_chat_conversion_models"`    // 支持图片转换的模型列表，逗号分隔
	SunoChatConversionEnabled       bool    `json:"suno_chat_conversion_enabled" bson:"suno_chat_conversion_enabled" gorm:"default:0;column:suno_chat_conversion_enabled"`    // 是否支持chat格式到suno异步接口的转换
	SunoChatConversionModels        string  `json:"suno_chat_conversion_models" bson:"suno_chat_conversion_models" gorm:"type:text;column:suno_chat_conversion_models"`       // 支持Suno转换的模型列表，逗号分隔
}

func (channelExtend *ChannelExtend) Insert(tx *gorm.DB) error {
	return channelExtend.InsertWithSync(tx, false)
}

func (channelExtend *ChannelExtend) InsertWithSync(tx *gorm.DB, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.InsertChannelExtend(channelExtend)
		if nosqlErr != nil {
			logger.SysError("failed to insert channel extend in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	var err error
	err = tx.Create(channelExtend).Error
	if err != nil {
		return err
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channelExtend *ChannelExtend) Update(tx *gorm.DB) error {
	return channelExtend.UpdateWithSync(tx, false)
}

func (channelExtend *ChannelExtend) UpdateWithSync(tx *gorm.DB, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		// MongoDB也采用先删除再插入的逻辑，与SQL保持一致
		deleteErr := db.DeleteChannelExtendByChannelId(channelExtend.ChannelId)
		if deleteErr != nil {
			logger.SysError("failed to delete channel extend in NoSQL during update: " + deleteErr.Error())
			nosqlErr = deleteErr
		} else {
			// 删除成功后，插入新数据
			insertErr := db.InsertChannelExtend(channelExtend)
			if insertErr != nil {
				logger.SysError("failed to insert channel extend in NoSQL during update: " + insertErr.Error())
				nosqlErr = insertErr
			}
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	// 先删除关联的渠道扩展,再插入新的渠道扩展
	err := channelExtend.DeleteChannelExtendByChannelId(tx, channelExtend.ChannelId)
	if err != nil {
		return err
	}
	err = tx.Save(channelExtend).Error
	if err != nil {
		return err
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channelExtend *ChannelExtend) Delete(tx *gorm.DB) error {
	return channelExtend.DeleteWithSync(tx, false)
}

func (channelExtend *ChannelExtend) DeleteWithSync(tx *gorm.DB, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.DeleteChannelExtend(channelExtend)
		if nosqlErr != nil {
			logger.SysError("failed to delete channel extend in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	var err error
	err = tx.Delete(channelExtend).Error
	if err != nil {
		return err
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

// DeleteChannelExtendByChannelId 根据渠道ID删除渠道扩展
func (channelExtend *ChannelExtend) DeleteChannelExtendByChannelId(tx *gorm.DB, channelId int) error {
	return channelExtend.DeleteChannelExtendByChannelIdWithSync(tx, channelId, false)
}

func (channelExtend *ChannelExtend) DeleteChannelExtendByChannelIdWithSync(tx *gorm.DB, channelId int, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.DeleteChannelExtendByChannelId(channelId)
		if nosqlErr != nil {
			logger.SysError("failed to delete channel extend by channel id in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	var err error
	err = tx.Where("channel_id = ?", channelId).Delete(channelExtend).Error
	if err != nil {
		return err
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channelExtend *ChannelExtend) Get() error {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		result, err := db.GetChannelExtendByChannelId(channelExtend.ChannelId)
		if err != nil {
			logger.SysError("failed to get channel extend from NoSQL: " + err.Error())
			// 如果NoSQL失败，回退到SQL
		} else {
			*channelExtend = *result
			return nil
		}
	}

	// SQL数据库操作（回退）
	err := DB.First(channelExtend).Error
	return err
}

// GetChannelExtendByChannelId 根据渠道ID获取渠道扩展
func GetChannelExtendByChannelId(channelId int) (*ChannelExtend, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		result, err := db.GetChannelExtendByChannelId(channelId)
		if err != nil {
			logger.SysError("failed to get channel extend by channel id from NoSQL: " + err.Error())
			// 如果NoSQL失败，回退到SQL
		} else {
			return result, nil
		}
	}

	// SQL数据库操作（回退）
	var channelExtend *ChannelExtend
	err := DB.Where("channel_id = ?", channelId).First(&channelExtend).Error
	if err != nil {
		return nil, err
	}
	return channelExtend, nil
}

// BatchInsertChannelExtendsByTx 批量插入渠道扩展
func BatchInsertChannelExtendsByTx(tx *gorm.DB, channelExtends []ChannelExtend, sqlOnly bool) error {
	return BatchInsertChannelExtendsByTxWithSync(tx, channelExtends, false, sqlOnly)
}

func BatchInsertChannelExtendsByTxWithSync(tx *gorm.DB, channelExtends []ChannelExtend, syncBothDB bool, sqlOnly bool) error {
	var nosqlErr error

	if !sqlOnly {
		// 使用渠道数据库接口
		db := getChannelDB()
		if db != nil {
			nosqlErr = db.BatchInsertChannelExtends(channelExtends, sqlOnly)
			if nosqlErr != nil {
				logger.SysError("failed to batch insert channel extends in NoSQL: " + nosqlErr.Error())
			}
			// 如果不需要同步两个数据库，直接返回NoSQL的结果
			if !syncBothDB {
				return nosqlErr
			}
			// 如果需要同步，继续执行SQL操作
		}
	}

	// SQL数据库操作
	var err error
	err = tx.Create(channelExtends).Error
	if err != nil {
		return err
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channelExtend *ChannelExtend) GetExtraHeaders() map[string]string {
	// 参考上面的注释
	if channelExtend.ExtraHeaders == nil || *channelExtend.ExtraHeaders == "" || *channelExtend.ExtraHeaders == "{}" {
		return nil
	}
	extraHeaders := make(map[string]string)
	err := json.Unmarshal([]byte(*channelExtend.ExtraHeaders), &extraHeaders)
	if err != nil {
		logger.SysError(fmt.Sprintf("failed to unmarshal extra headers for channel %d, error: %s", channelExtend.ChannelId, err.Error()))
		return nil
	}
	return extraHeaders
}

func DeleteChannelExtendByChannelIdByTx(tx *gorm.DB, channelId int) error {
	return DeleteChannelExtendByChannelIdByTxWithSync(tx, channelId, false)
}

func DeleteChannelExtendByChannelIdByTxWithSync(tx *gorm.DB, channelId int, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.DeleteChannelExtendByChannelId(channelId)
		if nosqlErr != nil {
			logger.SysError("failed to delete channel extend by channel id in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	var err error
	err = tx.Where("channel_id = ?", channelId).Delete(&ChannelExtend{}).Error
	if err != nil {
		return err
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channelExtend *ChannelExtend) GetBase64ImagePrefixMapping() map[string]string {
	if channelExtend.Base64ImagePrefixMapping == nil || *channelExtend.Base64ImagePrefixMapping == "" || *channelExtend.Base64ImagePrefixMapping == "{}" {
		return nil
	}
	base64ImagePrefixMapping := make(map[string]string)
	err := json.Unmarshal([]byte(*channelExtend.Base64ImagePrefixMapping), &base64ImagePrefixMapping)
	if err != nil {
		logger.SysError(fmt.Sprintf("failed to unmarshal base64 image prefix mapping for channel %d, error: %s", channelExtend.ChannelId, err.Error()))
		return nil
	}
	return base64ImagePrefixMapping
}
