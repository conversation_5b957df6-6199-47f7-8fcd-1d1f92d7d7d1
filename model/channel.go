package model

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/songquanpeng/one-api/common"

	"strings"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

const (
	ChannelStatusUnknown          = 0
	ChannelStatusEnabled          = 1 // don't use 0, 0 is the default value!
	ChannelStatusManuallyDisabled = 2 // also don't use 0
	ChannelStatusAutoDisabled     = 3
)

type Channel struct {
	Id                       int     `json:"id" bson:"id"`
	ChannelGroupId           int     `json:"channel_group_id" bson:"channel_group_id" gorm:"default:0;index"` // 所属渠道组id
	Type                     int     `json:"type" bson:"type" gorm:"default:0"`
	Key                      string  `json:"key" bson:"key" gorm:"type:text"`
	OpenAIOrganization       *string `json:"openai_organization" bson:"openai_organization"`
	Status                   int     `json:"status" bson:"status" gorm:"default:1"`
	Name                     string  `json:"name" bson:"name" gorm:"index"`
	Remark                   *string `json:"remark" bson:"remark" gorm:"index"`
	Weight                   *uint   `json:"weight" bson:"weight" gorm:"type:smallint;default:0"`
	CreatedTime              int64   `json:"created_time" bson:"created_time" gorm:"bigint"`
	TestTime                 int64   `json:"test_time" bson:"test_time" gorm:"bigint"`
	ResponseTime             int     `json:"response_time" bson:"response_time"` // in milliseconds
	BaseURL                  *string `json:"base_url" bson:"base_url" gorm:"column:base_url;default:''"`
	Other                    *string `json:"other" bson:"other"`     // DEPRECATED: please save config to field Config
	Balance                  float64 `json:"balance" bson:"balance"` // in USD
	BalanceUpdatedTime       int64   `json:"balance_updated_time" bson:"balance_updated_time" gorm:"bigint"`
	CustomBalanceLimit       float64 `json:"custom_balance_limit" bson:"custom_balance_limit"` // in USD 用户自定义余额上限
	Models                   string  `json:"models" bson:"models"`
	Group                    string  `json:"group" bson:"group" gorm:"type:varchar(1024);default:'default';index:idx_channel_group"`
	UsedQuota                int64   `json:"used_quota" bson:"used_quota" gorm:"bigint;default:0"`
	ModelMapping             *string `json:"model_mapping" bson:"model_mapping" gorm:"type:text;"`
	ExcludedFields           *string `json:"excluded_fields" bson:"excluded_fields" gorm:"type:text;"`
	ExcludedResponseFields   *string `json:"excluded_response_fields" bson:"excluded_response_fields" gorm:"type:text;"`
	ExtraFields              *string `json:"extra_fields" bson:"extra_fields" gorm:"type:text;"`
	Sort                     *int    `json:"sort" bson:"sort" gorm:"default:0"`
	OverFrequencyAutoDisable *bool   `json:"overFrequencyAutoDisable" bson:"overFrequencyAutoDisable" gorm:"default:0"`
	RetryInterval            *int    `json:"retryInterval" bson:"retryInterval" gorm:"default:300"`
	UndeadModeEnabled        *bool   `json:"undeadModeEnabled" bson:"undeadModeEnabled" gorm:"default:0"` // 开启不死模式,和之前的-1并不冲突,主要用于在自定义熔断之后不占用复活时间这个配置项
	Priority                 *int64  `json:"priority" bson:"priority" gorm:"bigint;default:0"`
	NonStrictTestMode        *bool   `json:"nonStrictTestMode" bson:"nonStrictTestMode" gorm:"default:0"`                // 非严格测试模式(默认false即严格模式)
	TestRequestBody          *string `json:"testRequestBody" bson:"testRequestBody" gorm:"type:text;"`                   // 测速请求参数
	DisableReason            *string `json:"disableReason" bson:"disableReason" gorm:"column:disable_reason;type:text;"` // 禁用原因
	BillingType              int     `json:"billing_type" bson:"billing_type" gorm:"default:1"`                          // 计费类型: 1 - 按量计费, 2 - 按次计费
	FunctionCallEnabled      *bool   `json:"function_call_enabled" bson:"function_call_enabled" gorm:"default:1"`        // 是否有能力使用function call 默认支持
	ImageSupported           *bool   `json:"image_supported" bson:"image_supported" gorm:"default:1"`                    // 是否支持图片,默认支持
	ImageInMarkdown          *bool   `json:"image_in_markdown" bson:"image_in_markdown" gorm:"default:0"`                // 返回图片链接以markdown包裹
	Config                   string  `json:"config" bson:"config"`
	SystemPrompt             *string `json:"system_prompt" bson:"system_prompt" gorm:"type:text"`
}

type ChannelConfig struct {
	Region                       string `json:"region,omitempty"`
	SK                           string `json:"sk,omitempty"`
	AK                           string `json:"ak,omitempty"`
	UserID                       string `json:"user_id,omitempty"`
	APIVersion                   string `json:"api_version,omitempty"`
	LibraryID                    string `json:"library_id,omitempty"`
	Plugin                       string `json:"plugin,omitempty"`
	VertexAIProjectID            string `json:"vertex_ai_project_id,omitempty"`
	VertexAIADC                  string `json:"vertex_ai_adc,omitempty"`
	MJTranslateEnabled           bool   `json:"mj_translate_enabled,omitempty"`
	MJTranslateModel             string `json:"mj_translate_model,omitempty"`
	MJConcatModeURLPrefixEnabled bool   `json:"mj_concat_mode_url_prefix_enabled,omitempty"`
	MJConcatModeToContent        bool   `json:"mj_concat_mode_to_content,omitempty"`
	MJAccountFilterModeEnabled   bool   `json:"mj_account_filter_mode_enabled,omitempty"`
	MJBase64ToLocalEnabled       bool   `json:"mj_base64_to_local_enabled,omitempty"`
	Eise                         int    `json:"eise,omitempty"`
	EiseUrl                      string `json:"eise_url,omitempty"`
	EiseKey                      string `json:"eise_key,omitempty"`
	TrustUpstreamStreamUsage     int    `json:"trust_upstream_stream_usage,omitempty"`
	ForceStreamOption            int    `json:"force_stream_option,omitempty"`
	ForceDownstreamStreamUsage   int    `json:"force_downstream_stream_usage,omitempty"`
	ProxyEnabled                 bool   `json:"proxy_enabled,omitempty"`             // 是否启用代理
	ProxyURL                     string `json:"proxy_url,omitempty"`                 // 代理服务器URL
	ProxyAuth                    string `json:"proxy_auth,omitempty"`                // 代理认证信息
	AudioMinBillingSeconds       int    `json:"audio_min_billing_seconds,omitempty"` // 音频模型最小计费时长(秒)，默认0表示按实际时长计费
	// 新增：响应中markdown base64图片转换开关
	ConvertBase64ToUrlEnabled bool `json:"convert_base64_to_url_enabled,omitempty"` // 是否启用响应中base64图片转换为URL
	// 新增：图片服务器地址配置
	ImageServerUrl string `json:"image_server_url,omitempty"` // 图片服务器地址(http://开头，不配置则使用系统默认)
}

// 针对ChannelConfig写一个getEise方法
func (cfg ChannelConfig) GetEiseEnabled() bool {
	// 0表示系统默认配置,1表示用户自定义配置开启,2表示用户自定义配置关闭
	if cfg.Eise == 0 {
		if config.EiseEnabled && cfg.GetEiseUrl() != "" && cfg.GetEiseKey() != "" {
			return true
		} else {
			return false
		}
	} else if cfg.Eise == 1 && cfg.GetEiseUrl() != "" && cfg.GetEiseKey() != "" {
		return true
	}
	return false
}

func (cfg ChannelConfig) GetEiseUrl() string {
	if cfg.Eise == 0 {
		if cfg.EiseUrl == "" {
			return config.EiseUrl
		}
		return cfg.EiseUrl
	}
	return cfg.EiseUrl
}

func (cfg ChannelConfig) GetEiseKey() string {
	if cfg.Eise == 0 {
		if cfg.EiseKey == "" {
			return config.EiseKey
		}
		return cfg.EiseKey
	}
	return cfg.EiseKey
}

// GetTrustUpstreamStreamUsageEnabled 获取是否信任上游流式用量统计
func (cfg ChannelConfig) GetTrustUpstreamStreamUsageEnabled() bool {
	// 0表示系统默认配置,1表示用户自定义配置开启,2表示用户自定义配置关闭
	if cfg.TrustUpstreamStreamUsage == 0 {
		return config.TrustUpstreamStreamUsageEnabled
	} else if cfg.TrustUpstreamStreamUsage == 1 {
		return true
	}
	return false
}

// GetForceStreamOptionEnabled 获取是否强制要求上游返回用量
func (cfg ChannelConfig) GetForceStreamOptionEnabled() bool {
	// 0表示系统默认配置,1表示用户自定义配置开启,2表示用户自定义配置关闭
	if cfg.ForceStreamOption == 0 {
		return config.ForceStreamOptionEnabled
	} else if cfg.ForceStreamOption == 1 {
		return true
	}
	return false
}

// GetForceDownstreamStreamUsageEnabled 获取是否强制返回下游流式用量
func (cfg ChannelConfig) GetForceDownstreamStreamUsageEnabled() bool {
	// 0表示系统默认配置,1表示用户自定义配置开启,2表示用户自定义配置关闭
	if cfg.ForceDownstreamStreamUsage == 0 {
		return config.ForceDownstreamStreamUsageEnabled
	} else if cfg.ForceDownstreamStreamUsage == 1 {
		return true
	}
	return false
}

// 添加代理相关的方法
func (cfg ChannelConfig) GetProxyEnabled() bool {
	return cfg.ProxyEnabled
}

func (cfg ChannelConfig) GetProxyURL() string {
	return cfg.ProxyURL
}

func (cfg ChannelConfig) GetProxyAuth() string {
	return cfg.ProxyAuth
}

// GetAudioMinBillingSeconds 获取音频模型最小计费时长(秒)
func (cfg ChannelConfig) GetAudioMinBillingSeconds() int {
	if cfg.AudioMinBillingSeconds <= 0 {
		return 0 // 默认按实际时长计费
	}
	return cfg.AudioMinBillingSeconds
}

// GetConvertBase64ToUrlEnabled 获取是否启用响应中base64图片转换为URL
func (cfg ChannelConfig) GetConvertBase64ToUrlEnabled() bool {
	return cfg.ConvertBase64ToUrlEnabled
}

// GetImageServerUrl 获取图片服务器地址
func (cfg ChannelConfig) GetImageServerUrl() string {
	return cfg.ImageServerUrl
}

// getChannelDB 获取渠道数据库实现（根据配置选择SQL或NoSQL）
func getChannelDB() DatabaseInterface {
	if DBAdapter != nil && DBAdapter.dbManager != nil && DBAdapter.dbManager.IsUsingNoSQL() {
		return DBAdapter.dbManager.GetDB()
	}
	// 默认使用SQL实现
	return nil
}

func GetAllChannels(startIdx int, num int, orderByStr string, selectAll bool, id int, name string, key string,
	group string, models string, channelGroupId int, status int, billingType int, baseUrl string,
	overFrequencyAutoDisable string, scope string, disableReason string) ([]*Channel, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAllChannels(startIdx, num, orderByStr, selectAll, id, name, key, group, models, channelGroupId, status, billingType, baseUrl, overFrequencyAutoDisable, scope, disableReason)
	}

	// 回退到原来的SQL实现
	var channels []*Channel
	// 解析orderByStr格式为[{"aa":"asc"},{"bb":"desc"}]解析成为JSON数组
	var orderByMap []map[string]string
	if orderByStr != "" {
		err := json.Unmarshal([]byte(orderByStr), &orderByMap)
		if err != nil {
			return nil, err
		}
	}
	keyCol := "`key`"
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
		groupCol = `"group"`
	}
	var err error
	tx := DB
	// 先按照sort,再按照id排序
	if selectAll {

	} else {
		tx = tx.Omit("key")
	}
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ? OR remark LIKE ?", "%"+name+"%", "%"+name+"%")
	}
	if key != "" {
		tx = tx.Where(keyCol+" = ?", key)
	}
	if group != "" {
		// 按照逗号拆分
		groups_ := strings.Split(group, ",")
		// FIND_IN_SET
		for _, model := range groups_ {
			if common.UsingPostgreSQL {
				tx = tx.Where("position(? in "+groupCol+") > 0", model)
			} else {
				tx = tx.Where("FIND_IN_SET(?, "+groupCol+")", model)
			}
		}
	}
	if models != "" {
		// 按照逗号拆分
		models_ := strings.Split(models, ",")
		// FIND_IN_SET
		for _, model := range models_ {
			if common.UsingPostgreSQL {
				tx = tx.Where("position(? in models) > 0", model)
			} else {
				tx = tx.Where("FIND_IN_SET(?, models)", model)
			}
		}
	}
	if channelGroupId != 0 {
		if channelGroupId == -1 {
			// 特殊处理传-1默认查询没有加挂渠道组的渠道
			tx = tx.Where("channel_group_id = 0 or channel_group_id = -1 or channel_group_id is null")
		} else {
			tx = tx.Where("channel_group_id = ?", channelGroupId)
		}
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if billingType != 0 {
		tx = tx.Where("billing_type = ?", billingType)
	}
	if baseUrl != "" {
		tx = tx.Where("base_url = ?", baseUrl)
	}
	if overFrequencyAutoDisable != "" {
		tx = tx.Where("over_frequency_auto_disable = ?", overFrequencyAutoDisable)
	}
	if disableReason != "" {
		// 根据不同的错误类型进行匹配
		switch disableReason {
		case "account_deactivated":
			tx = tx.Where("disable_reason LIKE ?", "%account%deactivated%")
		case "quota_exceeded":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%quota%exceeded%", "%insufficient%quota%")
		case "rate_limit_exceeded":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%rate%limit%", "%too%many%requests%")
		case "invalid_key":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%invalid%key%", "%authentication%failed%")
		case "connection_error":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?",
				"%connection%failed%", "%timeout%", "%refused%")
		default:
			tx = tx.Where("disable_reason LIKE ?", "%"+disableReason+"%")
		}
	}
	if len(orderByMap) > 0 {
		for _, orderBy := range orderByMap {
			for k, v := range orderBy {
				if v == "asc" {
					tx = tx.Order(k + " asc")
				} else if v == "desc" {
					tx = tx.Order(k + " desc")
				} else {
					tx = tx.Order(k + " " + v)
				}
			}
		}
	} else {
		tx = tx.Order("sort desc").Order("weight desc").Order("id desc").Order("created_time desc")
	}
	if num != 0 {
		tx = tx.Limit(num).Offset(startIdx)
	}
	err = tx.Find(&channels).Error
	return channels, err
}

func SearchChannels(startIdx int, num int, keyword string, channelGroupId int) (channels []*Channel, err error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.SearchChannels(startIdx, num, keyword, channelGroupId)
	}

	// 回退到原来的SQL实现
	keyCol := "`key`"
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
		groupCol = `"group"`
	}
	tx := DB
	tx = tx.Omit("key")
	if channelGroupId != 0 {
		if channelGroupId == -1 {
			// 特殊处理传-1默认查询没有加挂渠道组的渠道
			tx = tx.Where("channel_group_id = 0 or channel_group_id = -1 or channel_group_id is null")
		} else {
			tx = tx.Where("channel_group_id = ?", channelGroupId)
		}
	}
	tx = tx.Where("id = ? or name LIKE ? or "+keyCol+" = ? or "+groupCol+" like ? ", helper.String2Int(keyword), "%"+keyword+"%", keyword, "%"+keyword+"%").Order("sort desc").Limit(num).Offset(startIdx)
	err = tx.Find(&channels).Error
	return channels, err
}

func CountChannels(keyword string, id int, name string, key string, group string, models string, channelGroupId int, status int, billingType int, baseUrl string, overFrequencyAutoDisable string, disableReason string) (count int64, err error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.CountChannels(keyword, id, name, key, group, models, channelGroupId, status, billingType, baseUrl, overFrequencyAutoDisable, disableReason)
	}

	// 回退到原来的SQL实现
	keyCol := "`key`"
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
		groupCol = `"group"`
	}
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ? OR remark LIKE ?", "%"+name+"%", "%"+name+"%")
	}
	if key != "" {
		tx = tx.Where(keyCol+" = ?", key)
	}
	if group != "" {
		// 按照逗号拆分
		groups_ := strings.Split(group, ",")
		// FIND_IN_SET
		for _, model := range groups_ {
			if common.UsingPostgreSQL {
				tx = tx.Where("position(? in "+groupCol+") > 0", model)
			} else {
				tx = tx.Where("FIND_IN_SET(?, "+groupCol+")", model)
			}
		}
	}
	if models != "" {
		// 按照逗号拆分
		models_ := strings.Split(models, ",")
		// FIND_IN_SET
		for _, model := range models_ {
			if common.UsingPostgreSQL {
				tx = tx.Where("position(? in models) > 0", model)
			} else {
				tx = tx.Where("FIND_IN_SET(?, models)", model)
			}
		}
	}
	if channelGroupId != 0 {
		if channelGroupId == -1 {
			// 特殊处理传-1默认查询没有加挂渠道组的渠道
			tx = tx.Where("channel_group_id = 0 or channel_group_id = -1 or channel_group_id is null")
		} else {
			tx = tx.Where("channel_group_id = ?", channelGroupId)
		}
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if billingType != 0 {
		tx = tx.Where("billing_type = ?", billingType)
	}
	if baseUrl != "" {
		tx = tx.Where("base_url = ?", baseUrl)
	}
	if overFrequencyAutoDisable != "" {
		tx = tx.Where("over_frequency_auto_disable = ?", overFrequencyAutoDisable)
	}
	if disableReason != "" {
		// 根据不同的错误类型进行匹配
		switch disableReason {
		case "account_deactivated":
			tx = tx.Where("disable_reason LIKE ?", "%account%deactivated%")
		case "quota_exceeded":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%quota%exceeded%", "%insufficient%quota%")
		case "rate_limit_exceeded":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%rate%limit%", "%too%many%requests%")
		case "invalid_key":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%invalid%key%", "%authentication%failed%")
		case "connection_error":
			tx = tx.Where("disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?",
				"%connection%failed%", "%timeout%", "%refused%")
		default:
			tx = tx.Where("disable_reason LIKE ?", "%"+disableReason+"%")
		}
	}
	if keyword != "" {
		tx = tx.Where("id = ? or name LIKE ? or "+keyCol+" = ? or "+groupCol+" like ? ", helper.String2Int(keyword), "%"+keyword+"%", keyword, "%"+keyword+"%")
	}

	err = tx.Model(&Channel{}).Count(&count).Error
	return count, err
}

func BatchUpdateChannelsToChannelGroup(channelIds []int, channelGroupId int) (err error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.BatchUpdateChannelsToChannelGroup(channelIds, channelGroupId)
	}

	// 回退到原来的SQL实现
	err = DB.Transaction(func(tx *gorm.DB) error {
		return tx.Model(&Channel{}).Where("id IN ?", channelIds).Updates(Channel{ChannelGroupId: channelGroupId}).Error
	})
	return err
}

func GetChannelById(id int, selectAll bool) (*Channel, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetChannelById(id, selectAll)
	}

	// 回退到原来的SQL实现
	channel := Channel{Id: id}
	var err error = nil
	if selectAll {
		err = DB.First(&channel, "id = ?", id).Error
	} else {
		err = DB.Omit("key").First(&channel, "id = ?", id).Error
	}
	return &channel, err
}

func BatchInsertChannels(channels []Channel) error {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		err := db.BatchInsertChannels(channels)
		if err != nil {
			return err
		}
		return nil
	}

	// 回退到原来的SQL实现
	var err error
	err = DB.Create(&channels).Error
	if err != nil {
		return err
	}
	for _, channel_ := range channels {
		err = channel_.AddAbilities()
		if err != nil {
			return err
		}
	}
	return nil
}

func BatchInsertChannelsByTx(tx *gorm.DB, channels []Channel) error {
	var err error
	err = tx.Create(&channels).Error
	if err != nil {
		return err
	}
	for _, channel_ := range channels {
		err = channel_.AddAbilitiesByTx(tx)
		if err != nil {
			return err
		}
	}
	return nil
}

func (channel *Channel) GetWeight() uint {
	if channel.Weight == nil {
		return 0
	}
	return *channel.Weight
}
func (channel *Channel) GetWeightForCalculation() uint {
	// 真正计算权重时,权重为0或者空的默认为1
	if channel.Weight == nil {
		return 1
	}
	if *channel.Weight == 0 {
		return 1
	}
	return *channel.Weight
}
func (channel *Channel) GetSort() int {
	if channel.Sort == nil {
		return 0
	}
	return *channel.Sort
}

func (channel *Channel) GetPriority() int64 {
	if channel.Priority == nil {
		return 0
	}
	return *channel.Priority
}

func (channel *Channel) GetBaseURL() string {
	if channel.BaseURL == nil {
		return ""
	}
	return *channel.BaseURL
}

func (channel *Channel) GetFunctionCallEnabled() bool {
	if channel.FunctionCallEnabled == nil {
		return false
	}
	return *channel.FunctionCallEnabled
}
func (channel *Channel) GetImageSupported() bool {
	// 默认支持图片
	return channel.ImageSupported == nil || *channel.ImageSupported
}

func (channel *Channel) GetModelMapping() map[string]string {
	if channel.ModelMapping == nil || *channel.ModelMapping == "" || *channel.ModelMapping == "{}" {
		return nil
	}
	modelMapping := make(map[string]string)
	err := json.Unmarshal([]byte(*channel.ModelMapping), &modelMapping)
	if err != nil {
		logger.SysError(fmt.Sprintf("failed to unmarshal model mapping for channel %d, error: %s", channel.Id, err.Error()))
		return nil
	}
	return modelMapping
}

func (channel *Channel) GetModelMappingArr() []map[string]string {
	if channel.ModelMapping == nil || *channel.ModelMapping == "" || *channel.ModelMapping == "{}" {
		return nil
	}
	var modelMapping []map[string]string
	err := json.Unmarshal([]byte(*channel.ModelMapping), &modelMapping)
	if err != nil {
		logger.SysDebug(fmt.Sprintf("这个报错不影响后续逻辑执行 failed to unmarshal model mapping for channel %d, error: %s", channel.Id, err.Error()))
		return nil
	}
	return modelMapping
}

func (channel *Channel) GetExcludedResponseFields() string {
	if channel.ExcludedResponseFields == nil {
		return ""
	}
	return *channel.ExcludedResponseFields
}
func (channel *Channel) GetExcludedFields() string {
	if channel.ExcludedFields == nil {
		return ""
	}
	return *channel.ExcludedFields
}
func (channel *Channel) GetExtraFields() string {
	if channel.ExtraFields == nil {
		return ""
	}
	return *channel.ExtraFields
}
func (channel *Channel) GetTestRequestBody() string {
	if channel.TestRequestBody == nil {
		return ""
	}
	return *channel.TestRequestBody
}
func (channel *Channel) GetNonStrictTestMode() bool {
	if channel.NonStrictTestMode == nil {
		return false
	}
	return *channel.NonStrictTestMode
}
func (channel *Channel) GetRetryInterval() int {
	if channel.RetryInterval == nil {
		return 0
	}
	return *channel.RetryInterval
}
func (channel *Channel) GetUndeadModeEnabled() bool {
	if channel.UndeadModeEnabled == nil {
		return false
	}
	return *channel.UndeadModeEnabled
}

// GetRemark
func (channel *Channel) GetRemark() string {
	if channel.Remark == nil {
		return ""
	}
	return *channel.Remark
}

func (channel *Channel) Insert() error {
	return channel.InsertWithSync(false)
}

func (channel *Channel) InsertWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.InsertChannel(channel)
		if nosqlErr != nil {
			logger.SysError("failed to insert channel in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	var err error
	err = DB.Create(channel).Error
	if err != nil {
		return err
	}
	err = channel.AddAbilities()

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channel *Channel) Update() error {
	return channel.UpdateWithSync(false)
}

func (channel *Channel) UpdateWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.UpdateChannel(channel)
		if nosqlErr != nil {
			logger.SysError("failed to update channel in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(channel).Updates(channel).Error
		if err != nil {
			return err
		}
		err = tx.Model(channel).First(channel, "id = ?", channel.Id).Error
		if err != nil {
			return err
		}
		err = tx.Where("channel_id = ?", channel.Id).Delete(&Ability{}).Error
		if err != nil {
			return err
		}
		models_ := strings.Split(channel.Models, ",")
		groups_ := strings.Split(channel.Group, ",")
		abilities := make([]Ability, 0, len(models_))
		for _, model := range models_ {
			for _, group := range groups_ {
				ability := Ability{
					Group:               group,
					Model:               model,
					ChannelId:           channel.Id,
					Enabled:             &[]bool{channel.Status == ChannelStatusEnabled}[0],
					Sort:                channel.Sort,
					Weight:              channel.Weight,
					Priority:            channel.Priority,
					BillingType:         channel.BillingType,
					FunctionCallEnabled: channel.FunctionCallEnabled,
					ImageSupported:      channel.ImageSupported,
				}
				abilities = append(abilities, ability)
			}
		}
		err = tx.Create(&abilities).Error
		return err
	})

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channel *Channel) Delete() error {
	return channel.DeleteWithSync(false)
}

func (channel *Channel) DeleteWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.DeleteChannel(channel)
		if nosqlErr != nil {
			logger.SysError("failed to delete channel in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Transaction(func(tx *gorm.DB) error {
		err2 := tx.Delete(channel).Error
		if err2 != nil {
			return err2
		}
		err2 = tx.Where("channel_id = ?", channel.Id).Delete(&Ability{}).Error
		return err2
	})

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channel *Channel) LoadConfig() (ChannelConfig, error) {
	var cfg ChannelConfig
	if channel.Config == "" {
		return cfg, nil
	}
	err := json.Unmarshal([]byte(channel.Config), &cfg)
	if err != nil {
		return cfg, err
	}
	return cfg, nil
}

func UpdateChannelStatusById(id int, status int) {
	UpdateChannelStatusByIdWithSync(id, status, false)
}

func UpdateChannelStatusByIdWithSync(id int, status int, syncBothDB bool) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		err := db.UpdateChannelStatusById(id, status)
		if err != nil {
			logger.SysError("failed to update channel status in NoSQL: " + err.Error())
		}
		// 如果不需要同步两个数据库，直接返回
		if !syncBothDB {
			return
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&Ability{}).Where("channel_id = ?", id).Select("enabled").Update("enabled", status == common.ChannelStatusEnabled).Error
		if err != nil {
			logger.SysError("failed to update ability status in SQL: " + err.Error())
			return err
		}
		err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", status).Error
		if err != nil {
			logger.SysError("failed to update channel status in SQL: " + err.Error())
		}
		return err
	})
	if err != nil {
		logger.SysError("tx failed to update channel status in SQL: " + err.Error())
	}
}

func UpdateChannelStatusByIdReturnErr(id int, status int) error {
	// 使用渠道数据库接口，尝试使用安全的方法
	if DBAdapter != nil {
		return SafeUpdateChannelStatus(id, status)
	}

	// 回退到原来的SQL实现
	err := DB.Transaction(func(tx *gorm.DB) error {
		// 先查找channel状态，判断是否为启用状态
		var channel Channel
		err := tx.Model(&Channel{}).Where("id = ?", id).First(&channel).Error
		if err != nil {
			return err
		}
		if channel.Status != ChannelStatusEnabled {
			return errors.New("UpdateChannelStatusByIdReturnErr failed , because the channel is not enabled")
		}
		err = tx.Model(&Ability{}).Where("channel_id = ?", id).Select("enabled").Update("enabled", status == common.ChannelStatusEnabled).Error
		if err != nil {
			logger.SysError("failed to update ability status: " + err.Error())
			return err
		}
		err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", status).Error
		if err != nil {
			logger.SysError("failed to update channel status: " + err.Error())
		}
		return err
	})
	return err
}

// UpdateChannelAbilityStatusByIdReturnErr 更新指定渠道的特定能力状态，并根据情况更新渠道整体状态
//
// 参数:
//   - id: 渠道ID
//   - requestModel: 请求的模型名称，用于标识特定能力
//   - status: 要更新的状态，使用 common.ChannelStatus* 常量
//
// 返回:
//   - error: 如果更新过程中发生错误，返回相应的错误；否则返回 nil
//
// 函数逻辑:
//  1. 在事务中执行更新操作，确保数据一致性
//  2. 更新指定渠道和模型的 Ability 状态
//  3. 如果更新后的状态不是启用状态：
//     a. 检查是否所有 Ability 都被禁用，如果是，则将渠道状态更新为完全禁用
//     b. 否则，将渠道状态更新为部分禁用
//  4. 如果更新后的状态是启用状态，则检查该渠道的所有其他 Ability
//     如果所有 Ability 都是启用状态，则将渠道状态更新为完全启用
func UpdateChannelAbilityStatusByIdReturnErr(id int, requestModel string, status int) error {
	return UpdateChannelAbilityStatusByIdReturnErrWithSync(id, requestModel, status, false)
}

func UpdateChannelAbilityStatusByIdReturnErrWithSync(id int, requestModel string, status int, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口，尝试使用安全的方法
	if DBAdapter != nil {
		nosqlErr = SafeUpdateChannelAbilityStatus(id, requestModel, status)
		if nosqlErr != nil {
			logger.SysError("failed to update channel ability status in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	// 先检查渠道状态
	var channel Channel
	err := DB.Where("id = ?", id).First(&channel).Error
	if err != nil {
		return err
	}
	// 如果是手动禁用状态，直接返回
	if channel.Status == ChannelStatusManuallyDisabled {
		return errors.New("channel is manually disabled")
	}

	err = DB.Transaction(func(tx *gorm.DB) error {
		// 更新指定的 Ability 状态
		err := tx.Model(&Ability{}).Where("channel_id = ? and model = ?", id, requestModel).Select("enabled").Update("enabled", status == common.ChannelStatusEnabled).Error
		if err != nil {
			logger.SysError("failed to update ability status in SQL: " + err.Error())
			return err
		}

		if status != common.ChannelStatusEnabled {
			// 检查是否所有 Ability 都被禁用
			var enabledAbilityCount int64
			err = tx.Model(&Ability{}).Where("channel_id = ? AND enabled = ?", id, true).Count(&enabledAbilityCount).Error
			if err != nil {
				logger.SysError("failed to count enabled abilities in SQL: " + err.Error())
				return err
			}

			if enabledAbilityCount == 0 {
				// 所有 Ability 都被禁用，将渠道状态更新为完全禁用
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusAutoDisabled).Error
			} else {
				// 还有启用的 Ability，将渠道状态更新为部分禁用
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusPartiallyAutoDisabled).Error
			}
		} else {
			// 查询除了当前更新的 Ability 之外的所有 Ability
			var disabledAbilityCount int64
			err = tx.Model(&Ability{}).Where("channel_id = ? AND model != ? AND enabled = ?", id, requestModel, false).Count(&disabledAbilityCount).Error
			if err != nil {
				logger.SysError("failed to count disabled abilities in SQL: " + err.Error())
				return err
			}

			// 如果没有禁用的 Ability，则将渠道状态更新为完全启用
			if disabledAbilityCount == 0 {
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusEnabled).Error
			} else {
				// 还有禁用的 Ability，保持部分禁用状态
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusPartiallyAutoDisabled).Error
			}
		}

		return err
	})

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func UpdateChannelDisableReasonById(id int, disableReason string) {
	UpdateChannelDisableReasonByIdWithSync(id, disableReason, false)
}

func UpdateChannelDisableReasonByIdWithSync(id int, disableReason string, syncBothDB bool) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		err := db.UpdateChannelDisableReasonById(id, disableReason)
		if err != nil {
			logger.SysError("failed to update channel disable reason in NoSQL: " + err.Error())
		}
		// 如果不需要同步两个数据库，直接返回
		if !syncBothDB {
			return
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Model(&Channel{}).Where("id = ?", id).Update("disable_reason", disableReason).Error
	if err != nil {
		logger.SysError("failed to update channel disable reason in SQL: " + err.Error())
	}
}

func UpdateChannelUsedQuota(id int, quota int64) {
	UpdateChannelUsedQuotaWithSync(id, quota, false)
}

func UpdateChannelUsedQuotaWithSync(id int, quota int64, syncBothDB bool) {
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeChannelUsedQuota, id, quota)
		return
	}

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		err := db.UpdateChannelUsedQuota(id, quota)
		if err != nil {
			logger.SysError("failed to update channel used quota in NoSQL: " + err.Error())
		}
		// 如果不需要同步两个数据库，直接返回
		if !syncBothDB {
			return
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	updateChannelUsedQuota(id, quota)
}

func updateChannelUsedQuota(id int, quota int64) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		err := db.UpdateChannelUsedQuota(id, quota)
		if err != nil {
			logger.SysError("failed to update channel used quota in NoSQL: " + err.Error())
		}
		return
	}

	// SQL数据库操作（回退）
	err := DB.Model(&Channel{}).Where("id = ?", id).Update("used_quota", gorm.Expr("used_quota + ?", quota)).Error
	if err != nil {
		logger.SysError("failed to update channel used quota in SQL: " + err.Error())
	}
}

// 这个好像没有清除 ability 啊
func DeleteChannelByStatus(status int64) (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteChannelByStatus(status)
	}

	// 回退到原来的SQL实现
	result := DB.Where("status = ?", status).Delete(&Channel{})
	return result.RowsAffected, result.Error
}

func DeleteDisabledChannel() (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteChannelByType(99) // 99表示删除所有已禁用的渠道
	}

	// 回退到原来的SQL实现
	var err error
	var rows int64
	err = DB.Transaction(func(tx *gorm.DB) error {
		// 先查找
		var ids []int
		err = tx.Model(&Channel{}).Where("status = ? or status = ?", common.ChannelStatusAutoDisabled, common.ChannelStatusManuallyDisabled).Pluck("id", &ids).Error
		if err != nil {
			return err
		}
		// 删除channel
		result := tx.Where("id in ?", ids).Delete(&Channel{})
		if result.Error != nil {
			return result.Error
		}
		rows = result.RowsAffected
		// 删除ability
		err = tx.Where("channel_id IN ?", ids).Delete(&Ability{}).Error
		if err != nil {
			rows = 0
			return err
		}
		return result.Error
	})
	return rows, err
}

func DeleteChannelByType(deleteType int) (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteChannelByType(deleteType)
	}

	// 回退到原来的SQL实现，保持原有逻辑
	var err error
	var totalRows int64

	// 根据 deleteType 设置状态条件
	var statuses []int
	switch deleteType {
	case 2:
		statuses = append(statuses, common.ChannelStatusManuallyDisabled)
	case 3:
		statuses = append(statuses, common.ChannelStatusAutoDisabled)
	case 4:
		statuses = append(statuses, common.ChannelStatusMaxRetriesExceeded)
	case 99: // 特例，如果需要删除所有已禁用的渠道
		statuses = append(statuses, common.ChannelStatusAutoDisabled, common.ChannelStatusManuallyDisabled, common.ChannelStatusMaxRetriesExceeded)
	default:
		return 0, fmt.Errorf("invalid deleteType: %d", deleteType)
	}

	// 查找符合条件的渠道ID
	var allIds []int
	if len(statuses) > 0 {
		err = DB.Model(&Channel{}).Where("status IN ?", statuses).Pluck("id", &allIds).Error
		if err != nil {
			return 0, err
		}
	}

	// 如果没有找到符合条件的渠道，则直接返回
	if len(allIds) == 0 {
		return 0, nil
	}

	// 分批处理，避免IN语句包含过多ID
	const batchSize = 500 // 每批处理的ID数量
	for i := 0; i < len(allIds); i += batchSize {
		end := i + batchSize
		if end > len(allIds) {
			end = len(allIds)
		}
		batchIds := allIds[i:end]

		err = DB.Transaction(func(tx *gorm.DB) error {
			// 删除符合条件的渠道
			result := tx.Where("id IN ?", batchIds).Delete(&Channel{})
			if result.Error != nil {
				return result.Error
			}
			totalRows += result.RowsAffected

			// 删除相关联的能力
			err = tx.Where("channel_id IN ?", batchIds).Delete(&Ability{}).Error
			if err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			return totalRows, err // 返回已处理的行数和错误
		}
	}

	return totalRows, nil
}

// DeleteChannelByDisableReason 删除指定禁用原因的渠道
// disableReason: "account_deactivated" 表示删除所有账号停用的渠道
func DeleteChannelByDisableReason(disableReason string) (int64, error) {
	return DeleteChannelByDisableReasonWithSync(disableReason, false)
}

// DeleteChannelByDisableReasonWithSync 删除指定禁用原因的渠道，支持双写
func DeleteChannelByDisableReasonWithSync(disableReason string, syncBothDB bool) (int64, error) {
	var nosqlErr error
	var nosqlRows int64

	// 使用渠道数据库接口 (NoSQL)
	db := getChannelDB()
	if db != nil {
		nosqlRows, nosqlErr = db.DeleteChannelByDisableReason(disableReason)
		if nosqlErr != nil {
			logger.SysError("failed to delete channels by disable reason in NoSQL: " + nosqlErr.Error())
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlRows, nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作 - 回退到原来的SQL实现，保持原有逻辑
	var err error
	var totalRows int64

	// 查找符合条件的渠道ID
	var allIds []int
	query := DB.Model(&Channel{})

	// 根据不同的禁用原因构建不同的查询条件
	switch disableReason {
	case "account_deactivated":
		query = query.Where("disable_reason LIKE ?", "%account%deactivated%")
	case "quota_exceeded":
		query = query.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%quota%exceeded%", "%insufficient%quota%")
	case "rate_limit_exceeded":
		query = query.Where("disable_reason LIKE ? OR disable_reason LIKE ?", "%rate%limit%", "%too%many%requests%")
	case "invalid_key":
		query = query.Where("disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?", "%invalid%key%", "%incorrect%api%key%", "%authentication%failed%")
	case "connection_error":
		query = query.Where("disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?",
			"%connection%failed%", "%timeout%", "%refused%")
	default:
		// 处理双写的错误情况
		if syncBothDB && nosqlErr != nil {
			logger.SysError("NoSQL delete failed and SQL validation failed, data inconsistency may occur")
			return 0, fmt.Errorf("NoSQL delete failed: %v, and invalid disableReason: %s", nosqlErr, disableReason)
		}
		return 0, fmt.Errorf("invalid disableReason: %s", disableReason)
	}

	err = query.Pluck("id", &allIds).Error
	if err != nil {
		// 处理双写的错误情况
		if syncBothDB && nosqlErr == nil {
			logger.SysError("SQL query failed but NoSQL succeeded, data inconsistency may occur")
			return nosqlRows, fmt.Errorf("SQL query failed: %v", err)
		}
		return 0, err
	}

	// 如果没有找到符合条件的渠道，则直接返回
	if len(allIds) == 0 {
		// 在双写模式下，如果两个数据库的结果不一致，记录警告
		if syncBothDB && nosqlRows > 0 {
			logger.SysError(fmt.Sprintf("Data inconsistency: NoSQL deleted %d records but SQL found 0 matching records", nosqlRows))
		}
		return nosqlRows, nosqlErr
	}

	// 分批处理，避免IN语句包含过多ID
	const batchSize = 500 // 每批处理的ID数量
	for i := 0; i < len(allIds); i += batchSize {
		end := i + batchSize
		if end > len(allIds) {
			end = len(allIds)
		}
		batchIds := allIds[i:end]

		err = DB.Transaction(func(tx *gorm.DB) error {
			// 删除符合条件的渠道
			result := tx.Where("id IN ?", batchIds).Delete(&Channel{})
			if result.Error != nil {
				return result.Error
			}
			totalRows += result.RowsAffected

			// 删除相关联的能力
			err = tx.Where("channel_id IN ?", batchIds).Delete(&Ability{}).Error
			if err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			break // 出错时跳出循环
		}
	}

	// 处理双写的错误情况
	if syncBothDB && err == nil && nosqlErr != nil {
		logger.SysError("NoSQL delete by disable reason failed but SQL succeeded, data inconsistency may occur")
		return totalRows, fmt.Errorf("NoSQL delete failed: %v", nosqlErr)
	}

	return totalRows, err
}

func DeleteChannelByIds(ids []int) (int64, error) {
	return DeleteChannelByIdsWithSync(ids, false)
}

func DeleteChannelByIdsWithSync(ids []int, syncBothDB bool) (int64, error) {
	var nosqlErr error
	var nosqlRows int64

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlRows, nosqlErr = db.DeleteChannelByIds(ids)
		if nosqlErr != nil {
			logger.SysError("failed to delete channels by ids in NoSQL: " + nosqlErr.Error())
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlRows, nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作 - 回退到原来的SQL实现，保持原有逻辑
	var err error
	var totalRows int64

	// 如果没有ID，直接返回
	if len(ids) == 0 {
		return 0, nil
	}

	// 分批处理，避免IN语句包含过多ID
	const batchSize = 500 // 每批处理的ID数量
	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}
		batchIds := ids[i:end]

		err = DB.Transaction(func(tx *gorm.DB) error {
			result := tx.Where("id IN ?", batchIds).Delete(&Channel{})
			if result.Error != nil {
				return result.Error
			}
			totalRows += result.RowsAffected

			// 删除ability
			err = tx.Where("channel_id IN ?", batchIds).Delete(&Ability{}).Error
			if err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			break // 出错时跳出循环
		}
	}

	// 处理双写的错误情况
	if syncBothDB && err == nil && nosqlErr != nil {
		logger.SysError("NoSQL batch delete failed but SQL succeeded, data inconsistency may occur")
		return totalRows, fmt.Errorf("NoSQL batch delete failed: %v", nosqlErr)
	}

	return totalRows, err
}

type StatusCount struct {
	Status int
	Count  int
}

// CountChannelByStatus 统计各个状态的渠道的数量
func CountChannelByStatus() (map[int]int, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.CountChannelByStatus()
	}

	// 回退到原来的SQL实现
	var results []StatusCount
	err := DB.Model(&Channel{}).Select("status, count(*) as count").Group("status").Scan(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[int]int)
	for _, result := range results {
		countMap[result.Status] = result.Count
	}

	return countMap, nil
}

func GetChannelModelMappingsBySudoGroup(group string) ([]Channel, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetChannelModelMappingsBySudoGroup(group)
	}

	// 回退到原来的SQL实现
	tx := DB
	tx = tx.Omit("key")
	var channels []Channel
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	var err error
	if common.UsingPostgreSQL {
		err = tx.Select("id, name, "+groupCol+", model_mapping").Where("model_mapping IS NOT NULL AND model_mapping != '' AND model_mapping != '{}'").
			Where("position(? in "+groupCol+") > 0", group).
			Find(&channels).Error
	} else {
		err = tx.Select("id, name, "+groupCol+", model_mapping").Where("model_mapping IS NOT NULL AND model_mapping != '' AND model_mapping != '{}'").
			Where("FIND_IN_SET(?, "+groupCol+") > 0", group).
			Find(&channels).Error
	}
	if err != nil {
		return nil, err
	}
	return channels, nil
}

func GetAllModels() ([]string, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAllModels()
	}

	// 回退到原来的SQL实现
	var rows []string
	err := DB.Model(&Channel{}).Pluck("models", &rows).Error
	if err != nil {
		return nil, err
	}

	// 将所有行的models值合并到一个切片中
	var allModels []string
	for _, row := range rows {
		models := strings.Split(row, ",")
		allModels = append(allModels, models...)
	}

	// 对合并后的切片进行去重
	uniqueModels := make([]string, 0)
	seen := make(map[string]bool)
	for _, model := range allModels {
		if !seen[model] {
			seen[model] = true
			uniqueModels = append(uniqueModels, model)
		}
	}

	return uniqueModels, nil
}

func (channel *Channel) GetV1MessagesSupported() bool {
	// 获取渠道扩展信息
	channelEx, err := CacheGetChannelExByChannelId(channel.Id)
	if err != nil {
		logger.SysError(fmt.Sprintf("failed to get channel extend for channel %d, error: %s", channel.Id, err.Error()))
		return false
	}

	// 如果渠道扩展信息为空，则默认不支持
	if channelEx == nil {
		return false
	}

	// 返回 TransparentProxyEnabled 的值
	return channelEx.TransparentProxyEnabled
}

// CreateChannelWithExtend 创建渠道和渠道扩展（支持数据库切换）
func CreateChannelWithExtend(channel *Channel, channelExtend *ChannelExtend) error {
	return CreateChannelWithExtendWithSync(channel, channelExtend, false)
}

func CreateChannelWithExtendWithSync(channel *Channel, channelExtend *ChannelExtend, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.InsertChannel(channel)
		if nosqlErr != nil {
			logger.SysError("failed to insert channel in NoSQL: " + nosqlErr.Error())
		} else {
			channelExtend.ChannelId = channel.Id
			extendErr := db.InsertChannelExtend(channelExtend)
			if extendErr != nil {
				logger.SysError("failed to insert channel extend in NoSQL: " + extendErr.Error())
				nosqlErr = extendErr
			}
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := BatchInsertChannelsByTx(tx, []Channel{*channel})
	if err != nil {
		tx.Rollback()
		return err
	}

	channelExtend.ChannelId = channel.Id
	err = BatchInsertChannelExtendsByTx(tx, []ChannelExtend{*channelExtend}, true)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Commit().Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

// BatchCreateChannelsWithExtends 批量创建渠道和渠道扩展（支持数据库切换）
func BatchCreateChannelsWithExtends(channels []Channel, channelExtends []ChannelExtend) error {
	return BatchCreateChannelsWithExtendsWithSync(channels, channelExtends, false)
}

func BatchCreateChannelsWithExtendsWithSync(channels []Channel, channelExtends []ChannelExtend, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.BatchInsertChannels(channels)
		if nosqlErr != nil {
			logger.SysError("failed to batch insert channels in NoSQL: " + nosqlErr.Error())
		} else {
			// 设置渠道扩展的渠道ID
			for i := 0; i < len(channels); i++ {
				if i < len(channelExtends) {
					channelExtends[i].ChannelId = channels[i].Id
				}
			}

			extendErr := db.BatchInsertChannelExtends(channelExtends, false)
			if extendErr != nil {
				logger.SysError("failed to batch insert channel extends in NoSQL: " + extendErr.Error())
				nosqlErr = extendErr
			}
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	const batchSize = 100
	const txBatchCount = 5

	totalProcessed := 0
	batchCount := 0
	tx := DB.Begin()

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 分批处理
	for i := 0; i < len(channels); i += batchSize {
		end := i + batchSize
		if end > len(channels) {
			end = len(channels)
		}

		batchChannels := channels[i:end]
		batchChannelExtends := channelExtends[i:end]

		if len(batchChannels) > 0 {
			// 批量插入渠道
			err := BatchInsertChannelsByTx(tx, batchChannels)
			if err != nil {
				tx.Rollback()
				return err
			}

			// 设置渠道扩展的渠道ID
			for j := 0; j < len(batchChannels); j++ {
				if j < len(batchChannelExtends) {
					batchChannelExtends[j].ChannelId = batchChannels[j].Id
				}
			}

			// 批量插入渠道扩展
			err = BatchInsertChannelExtendsByTx(tx, batchChannelExtends, true)
			if err != nil {
				tx.Rollback()
				return err
			}

			totalProcessed += len(batchChannels)
		}

		// 每处理txBatchCount个批次，提交当前事务并开启新事务
		batchCount++
		if batchCount >= txBatchCount {
			err := tx.Commit().Error
			if err != nil {
				return err
			}
			tx = DB.Begin()
			batchCount = 0
		}
	}

	// 提交最后一个事务
	var err error
	if batchCount > 0 {
		err = tx.Commit().Error
	}

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

// DeleteChannelWithExtend 删除渠道和渠道扩展（支持数据库切换）
func DeleteChannelWithExtend(channelId int) error {
	return DeleteChannelWithExtendWithSync(channelId, false)
}

func DeleteChannelWithExtendWithSync(channelId int, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		channel := &Channel{Id: channelId}
		nosqlErr = db.DeleteChannel(channel)
		if nosqlErr != nil {
			logger.SysError("failed to delete channel in NoSQL: " + nosqlErr.Error())
		} else {
			extendErr := db.DeleteChannelExtendByChannelId(channelId)
			if extendErr != nil {
				logger.SysError("failed to delete channel extend in NoSQL: " + extendErr.Error())
				nosqlErr = extendErr
			}
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	channel := Channel{Id: channelId}
	err := channel.DeleteByTx(tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = DeleteChannelExtendByChannelIdByTx(tx, channelId)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Commit().Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

// UpdateChannelWithExtend 更新渠道和渠道扩展（支持数据库切换）
func UpdateChannelWithExtend(channel *Channel, channelExtend *ChannelExtend, statusOnly bool, cleanUsage bool) error {
	return UpdateChannelWithExtendWithSync(channel, channelExtend, statusOnly, cleanUsage, false)
}

func UpdateChannelWithExtendWithSync(channel *Channel, channelExtend *ChannelExtend, statusOnly bool, cleanUsage bool, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.UpdateChannel(channel)
		if nosqlErr != nil {
			logger.SysError("failed to update channel in NoSQL: " + nosqlErr.Error())
		} else {
			// 如果只是更新状态或清理使用量，则不更新渠道扩展
			if !statusOnly && !cleanUsage {
				extendErr := db.UpdateChannelExtend(channelExtend)
				if extendErr != nil {
					logger.SysError("failed to update channel extend in NoSQL: " + extendErr.Error())
					nosqlErr = extendErr
				}
			}
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := channel.UpdateByTx(tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 如果只是更新状态或清理使用量，则不更新渠道扩展
	if !statusOnly && !cleanUsage {
		err = channelExtend.Update(tx)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	err = tx.Commit().Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (channel *Channel) UpdateByTx(tx *gorm.DB) error {
	err := tx.Model(channel).Updates(channel).Error
	if err != nil {
		return err
	}
	err = tx.Model(channel).First(channel, "id = ?", channel.Id).Error
	if err != nil {
		return err
	}
	err = tx.Where("channel_id = ?", channel.Id).Delete(&Ability{}).Error
	if err != nil {
		return err
	}
	models_ := strings.Split(channel.Models, ",")
	groups_ := strings.Split(channel.Group, ",")
	abilities := make([]Ability, 0, len(models_))
	for _, model := range models_ {
		for _, group := range groups_ {
			ability := Ability{
				Group:               group,
				Model:               model,
				ChannelId:           channel.Id,
				Enabled:             &[]bool{channel.Status == ChannelStatusEnabled}[0],
				Sort:                channel.Sort,
				Weight:              channel.Weight,
				Priority:            channel.Priority,
				BillingType:         channel.BillingType,
				FunctionCallEnabled: channel.FunctionCallEnabled,
				ImageSupported:      channel.ImageSupported,
			}
			abilities = append(abilities, ability)
		}
	}
	err = tx.Create(&abilities).Error
	return err
}

func (channel *Channel) UpdateResponseTime(responseTime int64) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		// 更新响应时间和测试时间
		channel.TestTime = helper.GetTimestamp()
		channel.ResponseTime = int(responseTime)
		err := db.UpdateChannel(channel)
		if err != nil {
			logger.SysError("failed to update response time in NoSQL: " + err.Error())
		}
		return
	}

	// SQL数据库操作（回退）
	err := DB.Model(channel).Select("response_time", "test_time").Updates(Channel{
		TestTime:     helper.GetTimestamp(),
		ResponseTime: int(responseTime),
	}).Error
	if err != nil {
		logger.SysError("failed to update response time in SQL: " + err.Error())
	}
}

func (channel *Channel) UpdateBalance(balance float64) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		// 更新余额和余额更新时间
		channel.BalanceUpdatedTime = helper.GetTimestamp()
		channel.Balance = balance
		err := db.UpdateChannel(channel)
		if err != nil {
			logger.SysError("failed to update balance in NoSQL: " + err.Error())
		}
		return
	}

	// SQL数据库操作（回退）
	err := DB.Model(channel).Select("balance_updated_time", "balance").Updates(Channel{
		BalanceUpdatedTime: helper.GetTimestamp(),
		Balance:            balance,
	}).Error
	if err != nil {
		logger.SysError("failed to update balance in SQL: " + err.Error())
	}
}

func (channel *Channel) DeleteByTx(tx *gorm.DB) error {
	err := tx.Delete(channel).Error
	if err != nil {
		return err
	}
	err = tx.Where("channel_id = ?", channel.Id).Delete(&Ability{}).Error
	return err
}

// DeleteChannelExtendsNotExistsInChannel 删除不存在对应 channel 的 channel_extends 记录
func DeleteChannelExtendsNotExistsInChannel() (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteChannelExtendsNotExistsInChannel()
	}

	// 回退到原来的SQL实现
	result := DB.Where("not exists (SELECT 1 from channels where id = channel_id)").Delete(&ChannelExtend{})
	return result.RowsAffected, result.Error
}

// BanStatistics 封号统计结果
type BanStatistics struct {
	GroupName               string  `json:"group_name"`                 // 分组名称（服务器名，来自备注字段）
	TotalCount              int64   `json:"total_count"`                // 总渠道数
	BannedCount             int64   `json:"banned_count"`               // 被封渠道数
	BanRate                 float64 `json:"ban_rate"`                   // 封号率
	ActiveCount             int64   `json:"active_count"`               // 正常渠道数
	LatestActiveCreatedTime *int64  `json:"latest_active_created_time"` // 最新一条正常状态渠道的创建时间（Unix时间戳）
}

// ChannelStatistics 渠道统计结果（通用化）
type ChannelStatistics struct {
	GroupName               string  `json:"group_name"`                 // 分组名称（服务器名，来自备注字段）
	TotalCount              int64   `json:"total_count"`                // 总渠道数
	ActiveCount             int64   `json:"active_count"`               // 正常渠道数（status = 1）
	DisabledCount           int64   `json:"disabled_count"`             // 禁用渠道数（status = 3）
	UnknownCount            int64   `json:"unknown_count"`              // 其他状态渠道数
	DisabledRate            float64 `json:"disabled_rate"`              // 禁用率（百分比）
	FilteredCount           int64   `json:"filtered_count"`             // 符合特定条件的渠道数（如特定禁用原因）
	FilteredRate            float64 `json:"filtered_rate"`              // 符合条件的渠道比例（百分比）
	LatestActiveCreatedTime *int64  `json:"latest_active_created_time"` // 最新一条正常状态渠道的创建时间（Unix时间戳）
	AverageUsedQuota        float64 `json:"average_used_quota"`         // 渠道已用余额的平均值
}

// ChannelCreationSpeedItem 渠道创建速度统计项
type ChannelCreationSpeedItem struct {
	GroupName       string `json:"group_name"`       // 分组名称
	TimePoint       int64  `json:"time_point"`       // 时间点（Unix时间戳）
	CreationCount   int64  `json:"creation_count"`   // 该时间点创建的渠道数量
	CumulativeCount int64  `json:"cumulative_count"` // 累计创建的渠道数量
}

// GetChannelStatistics 获取渠道统计信息（通用化）
func GetChannelStatistics(statusFilter int, disableReason string, groupBy string) ([]*ChannelStatistics, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetChannelStatistics(statusFilter, disableReason, groupBy)
	}

	// 回退到SQL实现
	return getChannelStatisticsSQL(statusFilter, disableReason, groupBy)
}

// getChannelStatisticsSQL SQL实现的渠道统计（通用化，优化性能）
func getChannelStatisticsSQL(statusFilter int, disableReason string, groupBy string) ([]*ChannelStatistics, error) {
	var results []*ChannelStatistics

	// 构建禁用原因查询条件
	var disableReasonClause string
	var args []interface{}

	if disableReason != "" && disableReason != "all" {
		switch disableReason {
		case "account_deactivated":
			disableReasonClause = "AND disable_reason LIKE ?"
			args = append(args, "%account%deactivated%")
		case "quota_exceeded":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%quota%exceeded%", "%insufficient%quota%")
		case "rate_limit_exceeded":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%rate%limit%", "%too%many%requests%")
		case "invalid_key":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%invalid%key%", "%incorrect%api%key%", "%authentication%failed%")
		case "connection_error":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%connection%failed%", "%timeout%", "%refused%")
		case "internal_server_error":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%internal%server%error%", "%500%", "%server%error%")
		default:
			return nil, fmt.Errorf("unsupported disableReason parameter: %s", disableReason)
		}
	}

	// 构建分组字段
	var groupField string
	switch groupBy {
	case "domain":
		// 按域名分组：从name字段中提取@后面的域名部分
		if common.UsingPostgreSQL {
			groupField = `CASE 
				WHEN "name" LIKE '%@%' THEN CONCAT('@', SUBSTRING("name" FROM POSITION('@' IN "name") + 1))
				ELSE COALESCE(remark, 'Unknown')
			END`
		} else {
			groupField = `CASE 
				WHEN ` + "`name`" + ` LIKE '%@%' THEN CONCAT('@', SUBSTRING(` + "`name`" + `, LOCATE('@', ` + "`name`" + `) + 1))
				ELSE COALESCE(remark, 'Unknown')
			END`
		}
	case "server":
		// 按服务器分组：使用remark字段
		groupField = "COALESCE(remark, 'Unknown')"
	default:
		return nil, fmt.Errorf("unsupported groupBy parameter: %s", groupBy)
	}

	// 优化后的单次查询，避免复杂的子查询和JOIN
	var query string
	if statusFilter == 0 {
		// 统计所有状态 - 使用单次查询优化
		query = fmt.Sprintf(`
		SELECT 
			%s as group_name,
			COUNT(*) as total_count,
			SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count,
			SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as disabled_count,
			SUM(CASE WHEN status NOT IN (1, 3) THEN 1 ELSE 0 END) as unknown_count,
			SUM(CASE WHEN status = 3 %s THEN 1 ELSE 0 END) as filtered_count,
			MAX(CASE WHEN status = 1 THEN created_time ELSE NULL END) as latest_active_created_time,
			AVG(used_quota) as average_used_quota
		FROM channels
		GROUP BY %s
		ORDER BY disabled_count DESC, total_count DESC`,
			groupField, disableReasonClause, groupField)
	} else {
		// 只统计特定状态 - 使用单次查询优化
		query = fmt.Sprintf(`
		SELECT 
			%s as group_name,
			COUNT(*) as total_count,
			SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count,
			SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as disabled_count,
			SUM(CASE WHEN status NOT IN (1, 3) THEN 1 ELSE 0 END) as unknown_count,
			SUM(CASE WHEN status = ? %s THEN 1 ELSE 0 END) as filtered_count,
			MAX(CASE WHEN status = 1 THEN created_time ELSE NULL END) as latest_active_created_time,
			AVG(used_quota) as average_used_quota
		FROM channels
		WHERE status = ?
		GROUP BY %s
		ORDER BY filtered_count DESC, total_count DESC`,
			groupField, disableReasonClause, groupField)
		args = append([]interface{}{statusFilter}, args...)
		args = append(args, statusFilter)
	}

	// 执行查询
	rows, err := DB.Raw(query, args...).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 处理结果
	for rows.Next() {
		var stat ChannelStatistics
		var latestActiveCreatedTime *int64
		var averageUsedQuota *float64
		err := rows.Scan(&stat.GroupName, &stat.TotalCount, &stat.ActiveCount,
			&stat.DisabledCount, &stat.UnknownCount, &stat.FilteredCount, &latestActiveCreatedTime, &averageUsedQuota)
		if err != nil {
			return nil, err
		}

		// 设置最新正常状态渠道创建时间
		stat.LatestActiveCreatedTime = latestActiveCreatedTime

		// 设置平均已用余额
		if averageUsedQuota != nil {
			stat.AverageUsedQuota = *averageUsedQuota
		} else {
			stat.AverageUsedQuota = 0
		}

		// 计算比例
		if stat.TotalCount > 0 {
			stat.DisabledRate = float64(stat.DisabledCount) / float64(stat.TotalCount) * 100
			stat.FilteredRate = float64(stat.FilteredCount) / float64(stat.TotalCount) * 100
		}

		results = append(results, &stat)
	}

	return results, nil
}

// GetChannelBanStatistics 获取渠道封号统计信息（保持向后兼容）
func GetChannelBanStatistics(disableReason string) ([]*BanStatistics, error) {
	// 调用新的通用统计接口，默认按服务器分组
	stats, err := GetChannelStatistics(0, disableReason, "server")
	if err != nil {
		return nil, err
	}

	// 转换为旧格式
	var results []*BanStatistics
	for _, stat := range stats {
		banStat := &BanStatistics{
			GroupName:               stat.GroupName,
			TotalCount:              stat.TotalCount,
			BannedCount:             stat.DisabledCount,
			ActiveCount:             stat.ActiveCount,
			BanRate:                 stat.DisabledRate,
			LatestActiveCreatedTime: stat.LatestActiveCreatedTime,
		}
		results = append(results, banStat)
	}

	return results, nil
}

// GetChannelCreationSpeedStatistics 获取渠道创建速度统计信息
func GetChannelCreationSpeedStatistics(statusFilter int, disableReason string, groupBy string, timeGranularity string) (map[string][]*ChannelCreationSpeedItem, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetChannelCreationSpeedStatistics(statusFilter, disableReason, groupBy, timeGranularity)
	}

	// 回退到SQL实现
	return getChannelCreationSpeedStatisticsSQL(statusFilter, disableReason, groupBy, timeGranularity)
}

// getChannelCreationSpeedStatisticsSQL SQL实现的渠道创建速度统计
func getChannelCreationSpeedStatisticsSQL(statusFilter int, disableReason string, groupBy string, timeGranularity string) (map[string][]*ChannelCreationSpeedItem, error) {
	results := make(map[string][]*ChannelCreationSpeedItem)

	// 构建禁用原因查询条件
	var disableReasonClause string
	var args []interface{}

	if disableReason != "" && disableReason != "all" {
		switch disableReason {
		case "account_deactivated":
			disableReasonClause = "AND disable_reason LIKE ?"
			args = append(args, "%account%deactivated%")
		case "quota_exceeded":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%quota%exceeded%", "%insufficient%quota%")
		case "rate_limit_exceeded":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%rate%limit%", "%too%many%requests%")
		case "invalid_key":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%invalid%key%", "%incorrect%api%key%", "%authentication%failed%")
		case "connection_error":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%connection%failed%", "%timeout%", "%refused%")
		case "internal_server_error":
			disableReasonClause = "AND (disable_reason LIKE ? OR disable_reason LIKE ? OR disable_reason LIKE ?)"
			args = append(args, "%internal%server%error%", "%500%", "%server%error%")
		}
	}

	// 构建分组字段
	var groupField string
	switch groupBy {
	case "domain":
		// 按域名分组：从name字段中提取@后面的域名部分
		if common.UsingPostgreSQL {
			groupField = `CASE 
				WHEN "name" LIKE '%@%' THEN CONCAT('@', SUBSTRING("name" FROM POSITION('@' IN "name") + 1))
				ELSE COALESCE(remark, 'Unknown')
			END`
		} else {
			groupField = `CASE 
				WHEN ` + "`name`" + ` LIKE '%@%' THEN CONCAT('@', SUBSTRING(` + "`name`" + `, LOCATE('@', ` + "`name`" + `) + 1))
				ELSE COALESCE(remark, 'Unknown')
			END`
		}
	case "server":
		// 按服务器分组：使用remark字段
		groupField = "COALESCE(remark, 'Unknown')"
	default:
		return nil, fmt.Errorf("unsupported groupBy parameter: %s", groupBy)
	}

	// 构建时间格式化字段
	var timeFormat string
	var timeRange string
	switch timeGranularity {
	case "hour":
		// 按小时统计，获取最近30天数据（修改为30天以包含历史数据），返回Unix时间戳
		if common.UsingPostgreSQL {
			timeFormat = "EXTRACT(EPOCH FROM DATE_TRUNC('hour', TO_TIMESTAMP(created_time)))"
			timeRange = "AND created_time >= EXTRACT(EPOCH FROM NOW() - INTERVAL '30 days')"
		} else {
			timeFormat = "UNIX_TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(created_time), '%Y-%m-%d %H:00:00'))"
			timeRange = "AND created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))"
		}
	case "day":
		// 按天统计，获取最近90天数据，返回Unix时间戳
		if common.UsingPostgreSQL {
			timeFormat = "EXTRACT(EPOCH FROM DATE_TRUNC('day', TO_TIMESTAMP(created_time)))"
			timeRange = "AND created_time >= EXTRACT(EPOCH FROM NOW() - INTERVAL '90 days')"
		} else {
			timeFormat = "UNIX_TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(created_time), '%Y-%m-%d 00:00:00'))"
			timeRange = "AND created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 90 DAY))"
		}
	default:
		return nil, fmt.Errorf("unsupported timeGranularity parameter: %s", timeGranularity)
	}

	// 构建状态过滤条件
	var statusClause string
	if statusFilter != 0 {
		statusClause = "AND status = ?"
		args = append(args, statusFilter)
	}

	// 构建SQL查询 - 不使用窗口函数，先获取基础数据
	query := fmt.Sprintf(`
		SELECT 
			%s as group_name,
			%s as time_point,
			COUNT(*) as creation_count
		FROM channels
		WHERE 1=1 %s %s %s
		GROUP BY %s, %s
		ORDER BY group_name, time_point`,
		groupField, timeFormat, timeRange, statusClause, disableReasonClause, groupField, timeFormat)

	// 执行查询
	rows, err := DB.Raw(query, args...).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 临时存储数据，用于计算累计值
	type tempItem struct {
		GroupName     string
		TimePoint     int64
		CreationCount int64
	}

	var tempItems []tempItem

	// 处理结果
	for rows.Next() {
		var item tempItem
		var timePointFloat float64
		err := rows.Scan(&item.GroupName, &timePointFloat, &item.CreationCount)
		if err != nil {
			return nil, err
		}
		item.TimePoint = int64(timePointFloat)
		tempItems = append(tempItems, item)
	}

	// 按分组计算累计值
	groupCumulativeMap := make(map[string]int64)

	for _, item := range tempItems {
		// 累加该分组的创建数
		groupCumulativeMap[item.GroupName] += item.CreationCount

		// 创建最终的结果项
		resultItem := &ChannelCreationSpeedItem{
			GroupName:       item.GroupName,
			TimePoint:       item.TimePoint,
			CreationCount:   item.CreationCount,
			CumulativeCount: groupCumulativeMap[item.GroupName],
		}

		// 按分组名称组织数据
		results[item.GroupName] = append(results[item.GroupName], resultItem)
	}

	return results, nil
}
