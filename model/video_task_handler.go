package model

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common/logger"
)

// VideoTaskHandler VEO视频任务处理器
type VideoTaskHandler struct {
	BaseTaskHandler
}

// VeoRequest VEO API请求结构
type VeoRequest struct {
	Instances  []VeoInstance `json:"instances"`
	Parameters VeoParameters `json:"parameters"`
}

type VeoInstance struct {
	Prompt    string    `json:"prompt,omitempty"`
	Image     *VeoImage `json:"image,omitempty"`     // 图片生成视频
	LastFrame *VeoImage `json:"lastFrame,omitempty"` // 视频垫图 - 最后一帧
	Video     *VeoVideo `json:"video,omitempty"`     // 视频延长
}

type VeoImage struct {
	BytesBase64Encoded string `json:"bytesBase64Encoded,omitempty"`
	GcsUri             string `json:"gcsUri,omitempty"`
	MimeType           string `json:"mimeType"`
}

type VeoParameters struct {
	AspectRatio      string `json:"aspectRatio,omitempty"`
	SampleCount      int    `json:"sampleCount,omitempty"`
	DurationSeconds  string `json:"durationSeconds,omitempty"`  // VEO API要求字符串格式
	PersonGeneration string `json:"personGeneration,omitempty"` // "allow_all", "allow_adult", "dont_allow"
	AddWatermark     bool   `json:"addWatermark,omitempty"`
	IncludeRaiReason bool   `json:"includeRaiReason,omitempty"`
	GenerateAudio    bool   `json:"generateAudio,omitempty"`
	EnhancePrompt    bool   `json:"enhancePrompt,omitempty"`
	StorageUri       string `json:"storageUri,omitempty"`
	NegativePrompt   string `json:"negativePrompt,omitempty"`
	Seed             *int   `json:"seed,omitempty"`
}

// VeoResponse VEO API响应结构
type VeoResponse struct {
	Name     string                 `json:"name"`
	Done     bool                   `json:"done"`
	Response *VeoResponseData       `json:"response,omitempty"`
	Error    *VeoError              `json:"error,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

type VeoResponseData struct {
	Type                  string      `json:"@type"`
	Videos                []VeoVideo  `json:"videos,omitempty"`
	RaiMediaFilteredCount int         `json:"raiMediaFilteredCount,omitempty"`
	GeneratedSamples      []VeoSample `json:"generatedSamples,omitempty"`
}

type VeoVideo struct {
	BytesBase64Encoded string `json:"bytesBase64Encoded,omitempty"`
	GcsUri             string `json:"gcsUri,omitempty"`
	MimeType           string `json:"mimeType,omitempty"`
	Uri                string `json:"uri,omitempty"`
}

type VeoSample struct {
	Video VeoVideo `json:"video"`
}

type VeoError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// VideoGenerationRequest 视频生成请求
type VideoGenerationRequest struct {
	Model            string `json:"model"`
	Prompt           string `json:"prompt"`
	DurationSeconds  int    `json:"duration_seconds"`
	AspectRatio      string `json:"aspect_ratio"`
	SampleCount      int    `json:"sample_count"`
	EnhancePrompt    bool   `json:"enhance_prompt"`
	GenerateAudio    bool   `json:"generate_audio"`
	AddWatermark     bool   `json:"add_watermark"`
	IncludeRaiReason bool   `json:"include_rai_reason"`
	PersonGeneration string `json:"person_generation,omitempty"`
	StorageUri       string `json:"storage_uri,omitempty"`
	// 垫图功能支持
	Image             string `json:"image,omitempty"`                // 图片生成视频 (base64 或 GCS URI)
	ImageMimeType     string `json:"image_mime_type,omitempty"`      // 图片MIME类型
	LastFrame         string `json:"last_frame,omitempty"`           // 视频垫图 - 最后一帧 (base64 或 GCS URI)
	LastFrameMimeType string `json:"last_frame_mime_type,omitempty"` // 最后一帧MIME类型
	Video             string `json:"video,omitempty"`                // 视频延长 (base64 或 GCS URI)
	VideoMimeType     string `json:"video_mime_type,omitempty"`      // 视频MIME类型
	// 其他参数
	NegativePrompt string `json:"negative_prompt,omitempty"`
	Seed           *int   `json:"seed,omitempty"`
}

// Execute 执行异步视频生成任务
func (h *VideoTaskHandler) Execute(ctx context.Context, task *Task) error {
	// 解析请求数据
	var request VideoGenerationRequest
	err := task.GetData(&request)
	if err != nil {
		return fmt.Errorf("解析请求数据失败: %v", err)
	}

	// 验证请求参数
	err = h.ValidateRequest(&request)
	if err != nil {
		return fmt.Errorf("请求参数验证失败: %v", err)
	}

	// 获取渠道信息
	channel, err := GetChannelById(task.ChannelId, true)
	if err != nil {
		return fmt.Errorf("获取渠道信息失败: %v", err)
	}

	// 构建VEO请求
	veoRequest := h.BuildVeoRequest(&request)

	// 创建视频生成任务
	operationName, err := h.createVideoGenerationTask(ctx, channel, veoRequest)
	if err != nil {
		return fmt.Errorf("创建视频生成任务失败: %v", err)
	}

	// 更新任务状态
	task.TaskID = h.extractTaskIDFromOperationName(operationName)
	task.Status = TaskStatusSubmitted
	task.StartTime = time.Now().Unix()
	task.Progress = "10%"
	err = task.Update()
	if err != nil {
		logger.SysError(fmt.Sprintf("更新任务状态失败: %v", err))
	}

	// 开始轮询任务状态
	return h.pollTaskStatus(ctx, task, channel, operationName)
}

// ValidateRequest 验证请求参数
func (h *VideoTaskHandler) ValidateRequest(request *VideoGenerationRequest) error {
	if request.Prompt == "" {
		return fmt.Errorf("prompt不能为空")
	}
	if request.DurationSeconds <= 0 {
		request.DurationSeconds = 8 // 默认8秒
	}
	if request.AspectRatio == "" {
		request.AspectRatio = "16:9" // 默认16:9
	}
	if request.SampleCount <= 0 {
		request.SampleCount = 1 // 默认1个样本
	}
	// 验证person_generation参数
	if request.PersonGeneration != "" {
		validValues := []string{"allow_all", "allow_adult", "dont_allow"}
		valid := false
		for _, v := range validValues {
			if request.PersonGeneration == v {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("person_generation必须是以下值之一: %v", validValues)
		}
	}
	return nil
}

// BuildVeoRequest 构建VEO API请求
func (h *VideoTaskHandler) BuildVeoRequest(request *VideoGenerationRequest) *VeoRequest {
	veoReq := &VeoRequest{
		Instances: []VeoInstance{
			{
				Prompt: request.Prompt,
			},
		},
		Parameters: VeoParameters{
			AspectRatio:      request.AspectRatio,
			SampleCount:      request.SampleCount,
			DurationSeconds:  strconv.Itoa(request.DurationSeconds), // 转换为字符串
			PersonGeneration: request.PersonGeneration,
			AddWatermark:     request.AddWatermark,
			IncludeRaiReason: request.IncludeRaiReason,
			GenerateAudio:    request.GenerateAudio,
			EnhancePrompt:    request.EnhancePrompt,
			StorageUri:       request.StorageUri,
		},
	}

	// 设置默认值
	if veoReq.Parameters.AspectRatio == "" {
		veoReq.Parameters.AspectRatio = "16:9"
	}
	if veoReq.Parameters.SampleCount == 0 {
		veoReq.Parameters.SampleCount = 1
	}
	if veoReq.Parameters.PersonGeneration == "" {
		veoReq.Parameters.PersonGeneration = "allow_all"
	}
	// 如果没有明确设置，使用默认值
	if !request.AddWatermark && !request.IncludeRaiReason {
		veoReq.Parameters.AddWatermark = true
		veoReq.Parameters.IncludeRaiReason = true
	}

	// 添加可选参数
	if request.NegativePrompt != "" {
		veoReq.Parameters.NegativePrompt = request.NegativePrompt
	}
	if request.Seed != nil {
		veoReq.Parameters.Seed = request.Seed
	}

	// 处理垫图功能
	if request.Image != "" {
		// 图片生成视频
		mimeType := request.ImageMimeType
		if mimeType == "" {
			mimeType = "image/jpeg" // 默认JPEG
		}

		veoImage := &VeoImage{
			MimeType: mimeType,
		}

		// 判断是base64还是GCS URI
		if strings.HasPrefix(request.Image, "gs://") {
			veoImage.GcsUri = request.Image
		} else {
			veoImage.BytesBase64Encoded = request.Image
		}

		veoReq.Instances[0].Image = veoImage
	}

	if request.LastFrame != "" {
		// 视频垫图 - 最后一帧
		mimeType := request.LastFrameMimeType
		if mimeType == "" {
			mimeType = "image/jpeg" // 默认JPEG
		}

		veoLastFrame := &VeoImage{
			MimeType: mimeType,
		}

		// 判断是base64还是GCS URI
		if strings.HasPrefix(request.LastFrame, "gs://") {
			veoLastFrame.GcsUri = request.LastFrame
		} else {
			veoLastFrame.BytesBase64Encoded = request.LastFrame
		}

		veoReq.Instances[0].LastFrame = veoLastFrame
	}

	if request.Video != "" {
		// 视频延长
		mimeType := request.VideoMimeType
		if mimeType == "" {
			mimeType = "video/mp4" // 默认MP4
		}

		veoVideo := &VeoVideo{
			MimeType: mimeType,
		}

		// 判断是base64还是GCS URI
		if strings.HasPrefix(request.Video, "gs://") {
			veoVideo.GcsUri = request.Video
		} else {
			veoVideo.BytesBase64Encoded = request.Video
		}

		veoReq.Instances[0].Video = veoVideo
	}

	return veoReq
}

// createVideoGenerationTask 创建视频生成任务
func (h *VideoTaskHandler) createVideoGenerationTask(ctx context.Context, channel *Channel, veoRequest *VeoRequest) (string, error) {
	// 获取项目ID和区域 - 支持两种配置方式
	projectID, region, err := h.getProjectIDAndRegion(channel)
	if err != nil {
		return "", fmt.Errorf("获取项目配置失败: %v", err)
	}

	// 使用predictLongRunning端点，这是VEO的正确端点
	url := fmt.Sprintf("https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/veo-3.0-generate-preview:predictLongRunning",
		region, projectID, region)

	// 序列化请求体
	requestBody, err := json.Marshal(veoRequest)
	if err != nil {
		return "", fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 获取访问令牌
	token, err := h.getAccessToken(ctx, channel)
	if err != nil {
		return "", fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var veoResp VeoResponse
	err = json.Unmarshal(responseBody, &veoResp)
	if err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if veoResp.Error != nil {
		return "", fmt.Errorf("VEO API错误: %s", veoResp.Error.Message)
	}

	return veoResp.Name, nil
}

// extractTaskIDFromOperationName 从操作名称中提取任务ID
func (h *VideoTaskHandler) extractTaskIDFromOperationName(operationName string) string {
	// 操作名称格式: projects/{project}/locations/{location}/operations/{operation-id}
	parts := strings.Split(operationName, "/")
	if len(parts) >= 6 {
		return parts[5] // 返回operation-id部分
	}
	return operationName
}

// pollTaskStatus 轮询任务状态
func (h *VideoTaskHandler) pollTaskStatus(ctx context.Context, task *Task, channel *Channel, operationName string) error {
	maxWaitTime := 30 * time.Minute   // 30分钟超时
	checkInterval := 30 * time.Second // 30秒检查一次
	startTime := time.Now()

	logger.SysLog(fmt.Sprintf("开始轮询任务状态: %s", task.TaskID))

	for time.Since(startTime) < maxWaitTime {
		// 检查任务状态
		veoResp, err := h.getTaskStatus(ctx, channel, operationName)
		if err != nil {
			logger.SysError(fmt.Sprintf("查询任务状态失败: %v", err))
			time.Sleep(checkInterval)
			continue
		}

		// 更新进度
		progress := h.CalculateProgress(veoResp)
		task.Progress = progress

		if veoResp.Done {
			// 任务完成
			if veoResp.Error != nil {
				// 任务失败
				task.Status = TaskStatusFailure
				task.FailReason = veoResp.Error.Message
				task.FinishTime = time.Now().Unix()
				task.Progress = "100%"

				logger.SysError(fmt.Sprintf("任务失败: %s - %s", task.TaskID, veoResp.Error.Message))
			} else {
				// 任务成功
				task.Status = TaskStatusSuccess
				task.FinishTime = time.Now().Unix()
				task.Progress = "100%"

				// 转换结果
				result := h.ConvertVeoResult(veoResp)
				task.SetData(result)

				// 提取结果URL
				if resultData, ok := result["data"].([]map[string]interface{}); ok && len(resultData) > 0 {
					if url, exists := resultData[0]["url"]; exists {
						if urlStr, ok := url.(string); ok {
							task.ResultUrl = urlStr
						}
					}
				}

				logger.SysLog(fmt.Sprintf("任务完成: %s", task.TaskID))
			}

			// 更新任务状态
			err = task.Update()
			if err != nil {
				logger.SysError(fmt.Sprintf("更新任务状态失败: %v", err))
			}

			return nil
		} else {
			// 任务进行中
			task.Status = TaskStatusInProgress
			err = task.Update()
			if err != nil {
				logger.SysError(fmt.Sprintf("更新任务进度失败: %v", err))
			}

			logger.SysLog(fmt.Sprintf("任务进行中: %s - 进度: %s", task.TaskID, progress))
		}

		// 等待下次检查
		time.Sleep(checkInterval)
	}

	// 超时处理
	task.Status = TaskStatusFailure
	task.FailReason = "任务超时"
	task.FinishTime = time.Now().Unix()
	task.Progress = "100%"

	err := task.Update()
	if err != nil {
		logger.SysError(fmt.Sprintf("更新超时任务状态失败: %v", err))
	}

	return fmt.Errorf("任务超时")
}

// RetryWithDifferentChannel 使用不同渠道重试任务 - 参考 MJ Plus 的重试逻辑
func (h *VideoTaskHandler) RetryWithDifferentChannel(ctx context.Context, task *Task, originalErr error) error {
	logger.SysLog(fmt.Sprintf("尝试使用不同渠道重试任务: %s", task.TaskID))

	// 获取原始请求数据
	var request VideoGenerationRequest
	err := task.GetData(&request)
	if err != nil {
		return fmt.Errorf("获取原始请求数据失败: %v", err)
	}

	// 尝试获取其他可用渠道
	excludeIds := []int{task.ChannelId} // 排除当前失败的渠道

	// 这里可以添加更复杂的渠道选择逻辑
	// 暂时返回原始错误，实际实现需要集成渠道管理系统
	logger.SysError(fmt.Sprintf("渠道重试功能需要集成渠道管理系统，排除渠道: %v，当前返回原始错误: %v", excludeIds, originalErr))

	return originalErr
}

// CalculateProgressByIteration 智能计算任务进度（基于迭代次数）
func (h *VideoTaskHandler) CalculateProgressByIteration(currentIteration, maxIterations int) string {
	if currentIteration <= 0 {
		return "5%"
	}

	// 使用非线性进度计算，前期快速增长，后期缓慢增长
	progress := 5 + int(float64(currentIteration)/float64(maxIterations)*85)

	// 确保进度不超过95%（留5%给最终处理）
	if progress > 95 {
		progress = 95
	}

	return fmt.Sprintf("%d%%", progress)
}

// enhancedPollTaskStatus 增强版轮询任务状态 - 集成垫图功能和重试逻辑
func (h *VideoTaskHandler) enhancedPollTaskStatus(ctx context.Context, task *Task, channel *Channel, operationName string) error {
	maxWaitTime := 30 * time.Minute   // 30分钟超时
	checkInterval := 30 * time.Second // 30秒检查一次
	startTime := time.Now()
	consecutiveErrors := 0
	maxConsecutiveErrors := 5

	logger.SysLog(fmt.Sprintf("开始增强版轮询任务状态: %s (支持垫图功能)", task.TaskID))

	for time.Since(startTime) < maxWaitTime {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 检查任务状态
		veoResp, err := h.getTaskStatus(ctx, channel, operationName)
		if err != nil {
			consecutiveErrors++
			logger.SysError(fmt.Sprintf("查询任务状态失败 (第%d次连续错误): %v", consecutiveErrors, err))

			// 如果连续错误次数过多，考虑切换渠道重试
			if consecutiveErrors >= maxConsecutiveErrors {
				logger.SysError(fmt.Sprintf("连续错误次数达到%d次，尝试切换渠道重试", maxConsecutiveErrors))
				return h.RetryWithDifferentChannel(ctx, task, err)
			}

			// 指数退避策略
			backoffDuration := time.Duration(consecutiveErrors) * checkInterval
			if backoffDuration > 2*time.Minute {
				backoffDuration = 2 * time.Minute
			}
			time.Sleep(backoffDuration)
			continue
		}

		// 重置连续错误计数
		consecutiveErrors = 0

		// 计算智能进度
		elapsed := time.Since(startTime)
		progress := h.CalculateProgressByTime(elapsed, maxWaitTime)
		task.Progress = progress

		if veoResp.Done {
			// 任务完成
			if veoResp.Error != nil {
				// 任务失败
				task.Status = TaskStatusFailure
				task.FailReason = fmt.Sprintf("VEO API错误: %s (代码: %d)", veoResp.Error.Message, veoResp.Error.Code)
				task.FinishTime = time.Now().Unix()
				task.Progress = "100%"

				logger.SysError(fmt.Sprintf("任务失败: %s - %s", task.TaskID, task.FailReason))
			} else {
				// 任务成功
				task.Status = TaskStatusSuccess
				task.FinishTime = time.Now().Unix()
				task.Progress = "100%"

				// 转换结果 - 支持垫图功能的结果处理
				result := h.ConvertVeoResultWithImageSupport(veoResp)
				task.SetData(result)

				// 提取结果URL
				if resultData, ok := result["data"].([]map[string]interface{}); ok && len(resultData) > 0 {
					if url, exists := resultData[0]["url"]; exists {
						if urlStr, ok := url.(string); ok {
							task.ResultUrl = urlStr
						}
					}
				}

				logger.SysLog(fmt.Sprintf("任务完成: %s (支持垫图功能)", task.TaskID))
			}

			// 更新任务状态
			err = task.Update()
			if err != nil {
				logger.SysError(fmt.Sprintf("更新任务状态失败: %v", err))
			}

			return nil
		} else {
			// 任务进行中
			task.Status = TaskStatusInProgress
			err = task.Update()
			if err != nil {
				logger.SysError(fmt.Sprintf("更新任务进度失败: %v", err))
			}

			logger.SysLog(fmt.Sprintf("任务进行中: %s - 进度: %s", task.TaskID, progress))
		}

		// 等待下次检查
		time.Sleep(checkInterval)
	}

	// 超时处理
	task.Status = TaskStatusFailure
	task.FailReason = "任务超时"
	task.FinishTime = time.Now().Unix()
	task.Progress = "100%"

	err := task.Update()
	if err != nil {
		logger.SysError(fmt.Sprintf("更新超时任务状态失败: %v", err))
	}

	return fmt.Errorf("任务超时")
}

// CalculateProgressByTime 根据时间计算进度
func (h *VideoTaskHandler) CalculateProgressByTime(elapsed, maxWaitTime time.Duration) string {
	if elapsed <= 0 {
		return "5%"
	}

	// 计算时间进度百分比
	timeProgress := float64(elapsed) / float64(maxWaitTime)

	// 使用非线性进度计算，视频生成通常前期快，后期慢
	var progress int
	if timeProgress < 0.1 {
		// 前10%时间，进度到20%
		progress = int(timeProgress * 200)
	} else if timeProgress < 0.5 {
		// 10%-50%时间，进度从20%到70%
		progress = 20 + int((timeProgress-0.1)*125)
	} else if timeProgress < 0.9 {
		// 50%-90%时间，进度从70%到90%
		progress = 70 + int((timeProgress-0.5)*50)
	} else {
		// 最后10%时间，进度从90%到95%
		progress = 90 + int((timeProgress-0.9)*50)
	}

	// 确保进度在合理范围内
	if progress < 5 {
		progress = 5
	}
	if progress > 95 {
		progress = 95
	}

	return fmt.Sprintf("%d%%", progress)
}

// ConvertVeoResultWithImageSupport 转换VEO结果，支持垫图功能
func (h *VideoTaskHandler) ConvertVeoResultWithImageSupport(veoResp *VeoResponse) map[string]interface{} {
	result := map[string]interface{}{
		"id":      veoResp.Name,
		"object":  "video.generation",
		"created": time.Now().Unix(),
		"model":   "veo-3.0-generate-preview",
		"status":  "completed",
	}

	if veoResp.Response != nil {
		var videos []map[string]interface{}

		// 处理新格式的videos数组
		for _, video := range veoResp.Response.Videos {
			videoData := map[string]interface{}{
				"url": video.Uri,
			}
			if video.BytesBase64Encoded != "" {
				videoData["b64_json"] = video.BytesBase64Encoded
			}
			// 添加垫图相关的元数据
			if video.GcsUri != "" {
				videoData["gcs_uri"] = video.GcsUri
			}
			if video.MimeType != "" {
				videoData["mime_type"] = video.MimeType
			}
			videos = append(videos, videoData)
		}

		// 处理旧格式的generatedSamples
		for _, sample := range veoResp.Response.GeneratedSamples {
			videoData := map[string]interface{}{
				"url": sample.Video.Uri,
			}
			if sample.Video.BytesBase64Encoded != "" {
				videoData["b64_json"] = sample.Video.BytesBase64Encoded
			}
			// 添加垫图相关的元数据
			if sample.Video.GcsUri != "" {
				videoData["gcs_uri"] = sample.Video.GcsUri
			}
			if sample.Video.MimeType != "" {
				videoData["mime_type"] = sample.Video.MimeType
			}
			videos = append(videos, videoData)
		}

		result["data"] = videos

		// 添加垫图功能相关的统计信息
		if len(videos) > 0 {
			result["video_count"] = len(videos)
			result["supports_image_conditioning"] = true
		}
	}

	return result
}

// getTaskStatus 获取任务状态
func (h *VideoTaskHandler) getTaskStatus(ctx context.Context, channel *Channel, operationName string) (*VeoResponse, error) {
	// 获取项目ID和区域 - 支持两种配置方式
	projectID, region, err := h.getProjectIDAndRegion(channel)
	if err != nil {
		return nil, fmt.Errorf("获取项目配置失败: %v", err)
	}

	url := fmt.Sprintf("https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/veo-3.0-generate-preview:fetchPredictOperation",
		region, projectID, region)

	// 构建请求体 - VEO API需要POST请求和operationName参数
	requestBody := map[string]string{
		"operationName": operationName,
	}

	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求 - 使用POST方法
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBodyBytes))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 获取访问令牌
	token, err := h.getAccessToken(ctx, channel)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", "Bearer "+token)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var veoResp VeoResponse
	err = json.Unmarshal(responseBody, &veoResp)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &veoResp, nil
}

// CalculateProgress 计算任务进度
func (h *VideoTaskHandler) CalculateProgress(veoResp *VeoResponse) string {
	if veoResp.Done {
		return "100%"
	}

	// 根据元数据计算进度
	if veoResp.Metadata != nil {
		if progressValue, exists := veoResp.Metadata["progressPercentage"]; exists {
			if progress, ok := progressValue.(float64); ok {
				return fmt.Sprintf("%.0f%%", progress)
			}
		}
	}

	// 默认进度估算
	return "50%"
}

// ConvertVeoResult 转换VEO结果为OpenAI格式
func (h *VideoTaskHandler) ConvertVeoResult(veoResp *VeoResponse) map[string]interface{} {
	result := map[string]interface{}{
		"object": "video.generation",
		"data":   []map[string]interface{}{},
	}

	if veoResp.Response != nil {
		var videos []map[string]interface{}

		// 处理新格式的videos数组
		for _, video := range veoResp.Response.Videos {
			videoData := map[string]interface{}{
				"url": video.Uri,
			}
			if video.BytesBase64Encoded != "" {
				videoData["b64_json"] = video.BytesBase64Encoded
			}
			videos = append(videos, videoData)
		}

		// 处理旧格式的generatedSamples
		for _, sample := range veoResp.Response.GeneratedSamples {
			videoData := map[string]interface{}{
				"url": sample.Video.Uri,
			}
			if sample.Video.BytesBase64Encoded != "" {
				videoData["b64_json"] = sample.Video.BytesBase64Encoded
			}
			videos = append(videos, videoData)
		}

		result["data"] = videos
	}

	return result
}

// BaseTaskHandler 基础任务处理器
type BaseTaskHandler struct{}

// getProjectIDAndRegion 获取项目ID和区域 - 支持两种配置方式
func (h *VideoTaskHandler) getProjectIDAndRegion(channel *Channel) (string, string, error) {
	var projectID, region string

	// 方式1: 尝试从API Key中解析凭据（TaskAdaptor方式）
	if channel.Key != "" {
		var creds map[string]interface{}
		if err := json.Unmarshal([]byte(channel.Key), &creds); err == nil {
			if pid, ok := creds["project_id"].(string); ok {
				projectID = pid
				// 从凭据中获取区域（如果有）
				if r, ok := creds["region"].(string); ok {
					region = r
				}
			}
		}
	}

	// 方式2: 如果从API Key中没有获取到，尝试从Config中解析（普通Adaptor方式）
	if projectID == "" && channel.Config != "" {
		var config ChannelConfig
		if err := json.Unmarshal([]byte(channel.Config), &config); err == nil {
			projectID = config.VertexAIProjectID
			if region == "" {
				region = config.Region
			}
		}
	}

	// 设置默认区域
	if region == "" {
		region = "us-central1"
	}

	// 验证必需的项目ID
	if projectID == "" {
		return "", "", fmt.Errorf("未找到project_id，请检查渠道配置或API密钥")
	}

	return projectID, region, nil
}

// getAccessToken 获取访问令牌 - 支持两种认证方式
func (h *VideoTaskHandler) getAccessToken(ctx context.Context, channel *Channel) (string, error) {
	// 方式1: 尝试从API Key中解析凭据并生成token（TaskAdaptor方式）
	if channel.Key != "" {
		var creds map[string]interface{}
		if err := json.Unmarshal([]byte(channel.Key), &creds); err == nil {
			// 如果API Key是JSON格式的凭据，使用现有的token获取逻辑
			// 这里需要调用与vertexai/token.go相同的逻辑
			// 为了简化，我们先尝试直接使用Key作为token
			if _, ok := creds["type"]; ok {
				// 这是一个服务账号JSON，需要生成access token
				// 暂时返回错误，提示使用Config方式
				return "", fmt.Errorf("检测到服务账号JSON格式，请将凭据配置到渠道Config中的vertex_ai_adc字段")
			}
		} else {
			// 如果不是JSON格式，可能是直接的access token
			return channel.Key, nil
		}
	}

	// 方式2: 从Config中获取ADC凭据（普通Adaptor方式）
	if channel.Config != "" {
		var config ChannelConfig
		if err := json.Unmarshal([]byte(channel.Config), &config); err == nil {
			if config.VertexAIADC != "" {
				// 使用现有的token获取逻辑
				// 这里应该调用vertexai包中的getToken函数
				// 为了避免循环依赖，我们暂时返回错误提示
				return "", fmt.Errorf("ADC凭据认证暂未实现，请使用access token方式")
			}
		}
	}

	return "", fmt.Errorf("未找到有效的认证信息，请配置API密钥或ADC凭据")
}

// RegisterVideoTaskHandler 注册视频任务处理器
func RegisterVideoTaskHandler() {
	// 这里可以注册到任务管理器中
	logger.SysLog("VEO视频任务处理器已注册")
}
