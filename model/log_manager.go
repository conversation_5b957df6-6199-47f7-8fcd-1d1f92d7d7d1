package model

import (
	"context"
	"fmt"
	"sync"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
)

// LogManager 日志管理器，负责管理不同的日志存储实现
type LogManager struct {
	primaryStorage  LogStorage
	fallbackStorage LogStorage
	storageType     string
	mu              sync.RWMutex
}

var (
	logManager     *LogManager
	logManagerOnce sync.Once
)

// GetLogManager 获取日志管理器单例
func GetLogManager() *LogManager {
	logManagerOnce.Do(func() {
		logManager = &LogManager{}
		if err := logManager.Initialize(); err != nil {
			logger.SysError("Failed to initialize log manager: " + err.Error())
			// 如果初始化失败，使用MySQL作为默认存储
			logManager.primaryStorage = NewMySQLLogStorage(LOG_DB)
			logManager.storageType = "mysql"
		}
	})
	return logManager
}

// Initialize 初始化日志管理器
func (lm *LogManager) Initialize() error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	// 根据配置创建主存储
	primaryStorage, err := lm.createStorage(config.LogStorageType, config.LogStorageConnectionString)
	if err != nil {
		logger.SysError("Failed to create primary storage: " + err.Error())
		if !config.LogStorageFallbackToMySQL {
			return err
		}
		// 使用MySQL作为fallback
		primaryStorage = NewMySQLLogStorage(LOG_DB)
		logger.SysLog("Using MySQL as fallback storage")
	}

	lm.primaryStorage = primaryStorage
	lm.storageType = config.LogStorageType

	// 如果启用了fallback，创建MySQL作为备用存储
	if config.LogStorageFallbackToMySQL && config.LogStorageType != "mysql" {
		lm.fallbackStorage = NewMySQLLogStorage(LOG_DB)
	}

	logger.SysLog(fmt.Sprintf("Log manager initialized with primary storage: %s", lm.storageType))
	return nil
}

// createStorage 根据类型创建存储实例
func (lm *LogManager) createStorage(storageType, connectionString string) (LogStorage, error) {
	switch storageType {
	case "mysql":
		return NewMySQLLogStorage(LOG_DB), nil
	case "clickhouse":
		return NewClickHouseLogStorage(connectionString)
	case "elasticsearch":
		return NewElasticsearchLogStorage(connectionString)
	default:
		return nil, fmt.Errorf("unsupported storage type: %s", storageType)
	}
}

// SwitchStorage 切换存储类型
func (lm *LogManager) SwitchStorage(newStorageType, connectionString string) error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	// 创建新的存储实例
	newStorage, err := lm.createStorage(newStorageType, connectionString)
	if err != nil {
		return fmt.Errorf("failed to create new storage: %v", err)
	}

	// 测试新存储的连接
	if err := newStorage.HealthCheck(); err != nil {
		return fmt.Errorf("new storage health check failed: %v", err)
	}

	// 关闭旧的存储
	if lm.primaryStorage != nil {
		if err := lm.primaryStorage.Close(); err != nil {
			logger.SysError("Failed to close old storage: " + err.Error())
		}
	}

	// 切换到新存储
	lm.primaryStorage = newStorage
	lm.storageType = newStorageType

	logger.SysLog(fmt.Sprintf("Switched to new storage type: %s", newStorageType))
	return nil
}

// GetCurrentStorageType 获取当前存储类型
func (lm *LogManager) GetCurrentStorageType() string {
	lm.mu.RLock()
	defer lm.mu.RUnlock()
	return lm.storageType
}

// RecordLog 记录日志（带fallback）
func (lm *LogManager) RecordLog(ctx context.Context, log *Log) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	err := lm.primaryStorage.RecordLog(ctx, log)
	if err == nil {
		return nil
	}

	logger.Error(ctx, "Primary storage failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	//if lm.fallbackStorage != nil {
	//	logger.SysLog("Trying fallback storage")
	//	fallbackErr := lm.fallbackStorage.RecordLog(ctx, log)
	//	if fallbackErr != nil {
	//		logger.Error(ctx, "Fallback storage also failed: "+fallbackErr.Error())
	//		return fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
	//	}
	//	return nil
	//}

	return err
}

// RecordLogBatch 批量记录日志（带fallback）
func (lm *LogManager) RecordLogBatch(ctx context.Context, logs []*Log) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	err := lm.primaryStorage.RecordLogBatch(ctx, logs)
	if err == nil {
		return nil
	}

	logger.Error(ctx, "Primary storage batch failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for batch")
		fallbackErr := lm.fallbackStorage.RecordLogBatch(ctx, logs)
		if fallbackErr != nil {
			logger.Error(ctx, "Fallback storage batch also failed: "+fallbackErr.Error())
			return fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return nil
	}

	return err
}

// RecordConsumeLogByDetailIfZeroQuota 记录消费日志（如果配额为零）（带fallback）
func (lm *LogManager) RecordConsumeLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, xForwardedFor string, xRealIp string, cfConnectingIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, quota int, costQuota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64,
	isStream bool, content string, other string) (*Log, error) {

	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	log, err := lm.primaryStorage.RecordConsumeLogByDetailIfZeroQuota(ctx, requestId, ip, remoteIp, xForwardedFor, xRealIp, cfConnectingIp, userId, channelId,
		promptTokens, completionTokens, modelName, tokenName, tokenKey, tokenGroup, channelName, quota, costQuota,
		requestDuration, responseFirstByteDuration, totalDuration, isStream, content, other)
	if err == nil {
		return log, nil
	}

	logger.Error(ctx, "Primary storage RecordConsumeLogByDetailIfZeroQuota failed: "+err.Error())

	//// 如果主存储失败且有fallback，尝试fallback
	//if lm.fallbackStorage != nil {
	//	logger.Info(ctx, "Trying fallback storage for RecordConsumeLogByDetailIfZeroQuota")
	//	fallbackLog, fallbackErr := lm.fallbackStorage.RecordConsumeLogByDetailIfZeroQuota(ctx, requestId, ip, remoteIp, userId, channelId,
	//		promptTokens, completionTokens, modelName, tokenName, tokenKey, tokenGroup, channelName, quota, costQuota,
	//		requestDuration, responseFirstByteDuration, totalDuration, isStream, content, other)
	//	if fallbackErr == nil {
	//		return fallbackLog, nil
	//	}
	//	logger.Error(ctx, "Fallback storage RecordConsumeLogByDetailIfZeroQuota also failed: "+fallbackErr.Error())
	//}

	return log, err
}

// RecordRefundLogByDetailIfZeroQuota 记录退款日志（如果配额为零）（带fallback）
func (lm *LogManager) RecordRefundLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, xForwardedFor string, xRealIp string, cfConnectingIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string,
	quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string) (*Log, error) {

	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	log, err := lm.primaryStorage.RecordRefundLogByDetailIfZeroQuota(ctx, requestId, ip, remoteIp, xForwardedFor, xRealIp, cfConnectingIp, userId, channelId,
		promptTokens, completionTokens, modelName, tokenName, tokenKey, channelName, quota, requestDuration,
		responseFirstByteDuration, totalDuration, isStream, content)
	if err == nil {
		return log, nil
	}

	logger.Error(ctx, "Primary storage RecordRefundLogByDetailIfZeroQuota failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	//if lm.fallbackStorage != nil {
	//	logger.Info(ctx, "Trying fallback storage for RecordRefundLogByDetailIfZeroQuota")
	//	fallbackLog, fallbackErr := lm.fallbackStorage.RecordRefundLogByDetailIfZeroQuota(ctx, requestId, ip, remoteIp, userId, channelId,
	//		promptTokens, completionTokens, modelName, tokenName, tokenKey, channelName, quota, requestDuration,
	//		responseFirstByteDuration, totalDuration, isStream, content)
	//	if fallbackErr == nil {
	//		return fallbackLog, nil
	//	}
	//	logger.Error(ctx, "Fallback storage RecordRefundLogByDetailIfZeroQuota also failed: "+fallbackErr.Error())
	//}

	return log, err
}

// GetAllLogs 获取所有日志（只使用主存储）
func (lm *LogManager) GetAllLogs(userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, startIdx int, num int, channel int, isStream string, requestId string,
	ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64,
	requestDurationMin *float64, requestDurationMax *float64, responseFirstByteDurationMin *float64,
	responseFirstByteDurationMax *float64, excludeModels []string, errorCode string,
	excludeErrorCodes []string, quotaMin *int, quotaMax *int) ([]*Log, error) {

	lm.mu.RLock()
	defer lm.mu.RUnlock()

	return lm.primaryStorage.GetAllLogs(userId, timezone, logType, startTimestamp, endTimestamp,
		modelName, username, tokenName, tokenKey, tokenGroup, channelName, startIdx, num,
		channel, isStream, requestId, ip, promptTokensMin, promptTokensMax, completionTokensMin,
		completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
		requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
		excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)
}

// CountAllLogs 统计所有日志数量（只使用主存储）
func (lm *LogManager) CountAllLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (int64, error) {

	lm.mu.RLock()
	defer lm.mu.RUnlock()

	return lm.primaryStorage.CountAllLogs(userId, logType, startTimestamp, endTimestamp,
		modelName, username, tokenName, tokenKey, tokenGroup, channelName, channel,
		isStream, requestId, ip, promptTokensMin, promptTokensMax, completionTokensMin,
		completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
		requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
		excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)
}

// HealthCheck 健康检查
func (lm *LogManager) HealthCheck() error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	if lm.primaryStorage == nil {
		return fmt.Errorf("primary storage not initialized")
	}

	return lm.primaryStorage.HealthCheck()
}

// Close 关闭所有存储连接
func (lm *LogManager) Close() error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	var errors []error

	if lm.primaryStorage != nil {
		if err := lm.primaryStorage.Close(); err != nil {
			errors = append(errors, fmt.Errorf("primary storage close error: %v", err))
		}
	}

	if lm.fallbackStorage != nil {
		if err := lm.fallbackStorage.Close(); err != nil {
			errors = append(errors, fmt.Errorf("fallback storage close error: %v", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("close errors: %v", errors)
	}

	return nil
}

// RecordLogExtend 记录LogExtend（无fallback）
func (lm *LogManager) RecordLogExtend(ctx context.Context, logExtend *LogExtend) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，LogExtend不是核心数据
	return lm.primaryStorage.RecordLogExtend(ctx, logExtend)
}

// RecordLogExtendBatch 批量记录LogExtend（无fallback）
func (lm *LogManager) RecordLogExtendBatch(ctx context.Context, logExtends []*LogExtend) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，LogExtend不是核心数据
	return lm.primaryStorage.RecordLogExtendBatch(ctx, logExtends)
}

// GetLogExtendByLogId 根据LogId获取LogExtend（无fallback，简化逻辑）
func (lm *LogManager) GetLogExtendByLogId(logId int) (*LogExtend, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，不使用fallback
	// LogExtend不是核心数据，查不到就返回错误即可
	return lm.primaryStorage.GetLogExtendByLogId(logId)
}

// DeleteLogExtendByLogId 根据LogId删除LogExtend（无fallback）
func (lm *LogManager) DeleteLogExtendByLogId(logId int) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，LogExtend不是核心数据
	return lm.primaryStorage.DeleteLogExtendByLogId(logId)
}

// DeleteLogExtendByTimestamp 根据时间戳删除LogExtend（无fallback）
func (lm *LogManager) DeleteLogExtendByTimestamp(targetTimestamp int64) (int64, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，LogExtend不是核心数据
	return lm.primaryStorage.DeleteLogExtendByTimestamp(targetTimestamp)
}

// DeleteInvalidLogExtend 删除无效的LogExtend记录（无fallback）
func (lm *LogManager) DeleteInvalidLogExtend() error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，LogExtend不是核心数据
	return lm.primaryStorage.DeleteInvalidLogExtend()
}

// TruncateLogExtendTable 清空LogExtend表（无fallback）
func (lm *LogManager) TruncateLogExtendTable(ctx context.Context) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 直接使用主存储，LogExtend不是核心数据
	return lm.primaryStorage.TruncateLogExtendTable(ctx)
}

// RecordSysLogToDBAndFile 记录系统日志到数据库和文件（带fallback）
func (lm *LogManager) RecordSysLogToDBAndFile(ctx context.Context, requestId string, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	err := lm.primaryStorage.RecordSysLogToDBAndFile(ctx, requestId, logType, userId, channelId, modelName, tokenName, channelName, content, prompt)
	if err == nil {
		return nil
	}

	logger.Error(ctx, "Primary storage RecordSysLogToDBAndFile failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for RecordSysLogToDBAndFile")
		fallbackErr := lm.fallbackStorage.RecordSysLogToDBAndFile(ctx, requestId, logType, userId, channelId, modelName, tokenName, channelName, content, prompt)
		if fallbackErr != nil {
			logger.Error(ctx, "Fallback storage also failed: "+fallbackErr.Error())
			return fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return nil
	}

	return err
}

// RecordSysLogToDBAndFileByGinContext 记录系统日志到数据库和文件（通过Gin Context）（带fallback）
func (lm *LogManager) RecordSysLogToDBAndFileByGinContext(c interface{}, logType int, content string, prompt string) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	err := lm.primaryStorage.RecordSysLogToDBAndFileByGinContext(c, logType, content, prompt)
	if err == nil {
		return nil
	}

	logger.Error(nil, "Primary storage RecordSysLogToDBAndFileByGinContext failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for RecordSysLogToDBAndFileByGinContext")
		fallbackErr := lm.fallbackStorage.RecordSysLogToDBAndFileByGinContext(c, logType, content, prompt)
		if fallbackErr != nil {
			logger.Error(nil, "Fallback storage also failed: "+fallbackErr.Error())
			return fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return nil
	}

	return err
}

// RecordLogToDBAndFileByMeta 记录日志到数据库和文件（通过Meta）（带fallback）
func (lm *LogManager) RecordLogToDBAndFileByMeta(ctx context.Context, logType int, toFile bool, meta Meta, content string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64) error {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	err := lm.primaryStorage.RecordLogToDBAndFileByMeta(ctx, logType, toFile, meta, content, quota, requestDuration, responseFirstByteDuration, totalDuration)
	if err == nil {
		return nil
	}

	logger.Error(ctx, "Primary storage RecordLogToDBAndFileByMeta failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for RecordLogToDBAndFileByMeta")
		fallbackErr := lm.fallbackStorage.RecordLogToDBAndFileByMeta(ctx, logType, toFile, meta, content, quota, requestDuration, responseFirstByteDuration, totalDuration)
		if fallbackErr != nil {
			logger.Error(ctx, "Fallback storage also failed: "+fallbackErr.Error())
			return fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return nil
	}

	return err
}

// GetUserLogs 获取用户日志（带fallback）
func (lm *LogManager) GetUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string, tokenName string, tokenKey string, startIdx int, num int, isStream string, requestId string) ([]*Log, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	logs, err := lm.primaryStorage.GetUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, startIdx, num, isStream, requestId)
	if err == nil {
		return logs, nil
	}

	logger.Error(context.Background(), "Primary storage GetUserLogs failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for GetUserLogs")
		fallbackLogs, fallbackErr := lm.fallbackStorage.GetUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, startIdx, num, isStream, requestId)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return nil, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackLogs, nil
	}

	return nil, err
}

// SearchAllLogs 搜索所有日志（带fallback）
func (lm *LogManager) SearchAllLogs(keyword string) ([]*Log, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	logs, err := lm.primaryStorage.SearchAllLogs(keyword)
	if err == nil {
		return logs, nil
	}

	logger.Error(context.Background(), "Primary storage SearchAllLogs failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for SearchAllLogs")
		fallbackLogs, fallbackErr := lm.fallbackStorage.SearchAllLogs(keyword)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return nil, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackLogs, nil
	}

	return nil, err
}

// SearchUserLogs 搜索用户日志（带fallback）
func (lm *LogManager) SearchUserLogs(userId int, keyword string) ([]*Log, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	logs, err := lm.primaryStorage.SearchUserLogs(userId, keyword)
	if err == nil {
		return logs, nil
	}

	logger.Error(context.Background(), "Primary storage SearchUserLogs failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for SearchUserLogs")
		fallbackLogs, fallbackErr := lm.fallbackStorage.SearchUserLogs(userId, keyword)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return nil, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackLogs, nil
	}

	return nil, err
}

// SearchUserLogsByKey 根据key搜索用户日志（带fallback）
func (lm *LogManager) SearchUserLogsByKey(key string, startIdx int, num int) ([]*Log, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	logs, err := lm.primaryStorage.SearchUserLogsByKey(key, startIdx, num)
	if err == nil {
		return logs, nil
	}

	logger.Error(context.Background(), "Primary storage SearchUserLogsByKey failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for SearchUserLogsByKey")
		fallbackLogs, fallbackErr := lm.fallbackStorage.SearchUserLogsByKey(key, startIdx, num)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return nil, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackLogs, nil
	}

	return nil, err
}

// CountUserLogsByKey 根据key统计用户日志数量（带fallback）
func (lm *LogManager) CountUserLogsByKey(key string) (int64, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	count, err := lm.primaryStorage.CountUserLogsByKey(key)
	if err == nil {
		return count, nil
	}

	logger.Error(context.Background(), "Primary storage CountUserLogsByKey failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for CountUserLogsByKey")
		fallbackCount, fallbackErr := lm.fallbackStorage.CountUserLogsByKey(key)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return 0, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackCount, nil
	}

	return 0, err
}

// CountUserLogs 统计用户日志数量（带fallback）
func (lm *LogManager) CountUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string, tokenName string, tokenKey string, isStream string, requestId string) (int64, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	count, err := lm.primaryStorage.CountUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, isStream, requestId)
	if err == nil {
		return count, nil
	}

	logger.Error(context.Background(), "Primary storage CountUserLogs failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for CountUserLogs")
		fallbackCount, fallbackErr := lm.fallbackStorage.CountUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, isStream, requestId)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return 0, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackCount, nil
	}

	return 0, err
}

// SumUsedQuota 统计使用的配额（带fallback）
func (lm *LogManager) SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, modelName string, username string, tokenName string, tokenKey string, tokenGroup string, channel int, useRedis bool) (Stat, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	stat, err := lm.primaryStorage.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, useRedis)
	if err == nil {
		return stat, nil
	}

	logger.Error(context.Background(), "Primary storage SumUsedQuota failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for SumUsedQuota")
		fallbackStat, fallbackErr := lm.fallbackStorage.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, useRedis)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return Stat{}, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackStat, nil
	}

	return Stat{}, err
}

// SumUsedQuotaByKey 根据key统计使用的配额（带fallback）
func (lm *LogManager) SumUsedQuotaByKey(key string, startTimestamp int64, endTimestamp int64) (Stat, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	stat, err := lm.primaryStorage.SumUsedQuotaByKey(key, startTimestamp, endTimestamp)
	if err == nil {
		return stat, nil
	}

	logger.Error(context.Background(), "Primary storage SumUsedQuotaByKey failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for SumUsedQuotaByKey")
		fallbackStat, fallbackErr := lm.fallbackStorage.SumUsedQuotaByKey(key, startTimestamp, endTimestamp)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return Stat{}, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackStat, nil
	}

	return Stat{}, err
}

// SumAllDailyUsageStatsByDimension 按维度统计每日使用情况（带fallback）
func (lm *LogManager) SumAllDailyUsageStatsByDimension(userId int, timezone string, tokenName string, username string, channel int, channelName string, modelName string, startTimestamp int64, endTimestamp int64, dimension string, granularity string) ([]*DailyModelUsageStats, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 尝试主存储
	stats, err := lm.primaryStorage.SumAllDailyUsageStatsByDimension(userId, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)
	if err == nil {
		return stats, nil
	}

	logger.Error(context.Background(), "Primary storage SumAllDailyUsageStatsByDimension failed: "+err.Error())

	// 如果主存储失败且有fallback，尝试fallback
	if lm.fallbackStorage != nil {
		logger.SysLog("Trying fallback storage for SumAllDailyUsageStatsByDimension")
		fallbackStats, fallbackErr := lm.fallbackStorage.SumAllDailyUsageStatsByDimension(userId, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)
		if fallbackErr != nil {
			logger.Error(context.Background(), "Fallback storage also failed: "+fallbackErr.Error())
			return nil, fmt.Errorf("both primary and fallback storage failed: primary=%v, fallback=%v", err, fallbackErr)
		}
		return fallbackStats, nil
	}

	return nil, err
}
