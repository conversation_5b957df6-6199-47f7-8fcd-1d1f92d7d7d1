package model

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/constants"
	"github.com/songquanpeng/one-api/common/ctxkey"

	"github.com/samber/lo"

	"gorm.io/gorm"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"

	"sync/atomic"
)

var TotalShouldDeleteHistoryLogsCount int64 = 0
var TotalAffectedHistoryLogsCount int64 = 0

var (
	ignoredErrorCodesCache atomic.Value
	lastIgnoredErrorCodes  string
	samplingCounter        uint64
)

type Log struct {
	Id                        int    `json:"id"`
	Uuid                      string `json:"uuid" gorm:"-"` // ClickHouse专用UUID字段，MySQL不使用，不参与GORM操作
	RequestId                 string `json:"request_id" gorm:"index"`
	UserId                    int    `json:"user_id" gorm:"index"`
	CreatedAt                 int64  `json:"created_at" gorm:"bigint;index:idx_created_at_type"`
	Type                      int    `json:"type" gorm:"index:idx_created_at_type;index"`
	Content                   string `json:"content"`
	Username                  string `json:"username" gorm:"index;index:index_username_model_name,priority:2;default:''"` // 添加单独的username索引
	TokenName                 string `json:"token_name" gorm:"index;default:''"`
	TokenGroup                string `json:"token_group" gorm:"default:''"` // token_group 用于记录token的分组信息 有索引
	ModelName                 string `json:"model_name" gorm:"index;index:index_username_model_name,priority:1;default:''"`
	ChannelName               string `json:"channel_name" gorm:"index;default:''"`
	Quota                     int    `json:"quota" gorm:"default:0"`
	CostQuota                 int    `json:"cost_quota" gorm:"default:0"`
	PromptTokens              int    `json:"prompt_tokens" gorm:"default:0"`
	CompletionTokens          int    `json:"completion_tokens" gorm:"default:0"`
	ChannelId                 int    `json:"channel" gorm:"index"`
	TokenKey                  string `json:"token_key" gorm:"default:'';index"`
	RequestDuration           int64  `json:"request_duration" gorm:"index;default:0"`
	ResponseFirstByteDuration int64  `json:"response_first_byte_duration" gorm:"index;default:0"`
	TotalDuration             int64  `json:"total_duration" gorm:"index;default:0"`
	DurationForView           int64  `json:"duration_for_view" gorm:"-"`
	ElapsedTime               int64  `json:"elapsed_time" gorm:"default:0"` // unit is ms
	IsStream                  bool   `json:"is_stream" gorm:"default:false"`
	SystemPromptReset         bool   `json:"system_prompt_reset" gorm:"default:false"`
	Ip                        string `json:"ip" gorm:"index"`                    // 记录请求者ip (通过GetClientRealIp获取的最终IP)
	RemoteIp                  string `json:"remote_ip" gorm:"index"`             // 记录请求者ip (gin.Context.RemoteIP())
	XForwardedFor             string `json:"x_forwarded_for" gorm:"index"`       // X-Forwarded-For头
	XRealIp                   string `json:"x_real_ip" gorm:"index"`             // X-Real-IP头
	CfConnectingIp            string `json:"cf_connecting_ip" gorm:"index"`      // CloudFlare CF-Connecting-IP头
	Other                     string `json:"other"`                              // 格式 {"admin_info":{"use_channel":["1"]},"completion_ratio":3,"frt":-1000,"group_ratio":1,"model_price":-1,"model_ratio":0.25}
	ErrorCode                 string `json:"error_code" gorm:"index;default:''"` // 添加error_code字段并创建索引
}

const (
	LogTypeUnknown = iota
	LogTypeTopup
	LogTypeConsume
	LogTypeManage
	LogTypeSystem
	LogTypeInviteBonus
	LogTypeSystemInfo
	LogTypeSystemWarn
	LogTypeSystemErr
	LogTypeUserCheckin
	LogTypeOperation //操作记录日志
	LogTypeRefund
	LogTypeInviteBonusTransfer
	LogTypeAgencyBonus
	_
	LogTypeDownstreamError // 记录抛给下游客户的错误信息
	LogTypeTest
)

func GetLogByKey(key string) (logs []*Log, err error) {
	err = LOG_DB.Joins("left join tokens on tokens.id = logs.token_id").Where("tokens.key = ?", strings.Split(key, "-")[1]).Find(&logs).Error
	return logs, err
}

func recordLogHelper(ctx context.Context, log *Log) {
	requestId := helper.GetRequestID(ctx)
	if log.RequestId == "" {
		log.RequestId = requestId
	}

	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		err := logManager.RecordLog(ctx, log)
		if err != nil {
			logger.Error(ctx, "failed to record log via LogManager: "+err.Error())
			return
		}
	} else {
		// 使用原有的直接数据库写入方式
		err := LOG_DB.Create(log).Error
		if err != nil {
			logger.Error(ctx, "failed to record log: "+err.Error())
			return
		}
	}
	logger.Infof(ctx, "record log: %+v", log)
}

func RecordLog(ctx context.Context, userId int, logType int, content string) {
	// 根据日志类型检查对应的开关
	if (logType == LogTypeConsume && !config.LogConsumeEnabled) ||
		((logType == LogTypeDownstreamError || logType == LogTypeSystemErr) && !config.LogErrorEnabled) ||
		(logType == LogTypeSystemInfo && !config.LogSysInfoEnabled) {
		return
	}
	log := &Log{
		UserId:    userId,
		Username:  CacheGetUsernameById(userId),
		CreatedAt: helper.GetTimestamp(),
		Type:      logType,
		Content:   content,
	}
	recordLogHelper(ctx, log)
}

// 比上面的RecordLog记录更详细的日志
func RecordLogByDetail(ctx context.Context, userId int, logType int, ip string, remoteIp string, channelId int, channelName string,
	tokenName string, tokenKey string, promptTokens int, completionTokens int, modelName string, quota int,
	requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string, errorCode string) *Log {
	// 根据日志类型检查对应的开关
	if (logType == LogTypeConsume && !config.LogConsumeEnabled) ||
		((logType == LogTypeDownstreamError || logType == LogTypeSystemErr) && !config.LogErrorEnabled) ||
		(logType == LogTypeSystemInfo && !config.LogSysInfoEnabled) {
		return nil
	}

	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}

	log := &Log{
		RequestId:                 requestId,
		UserId:                    userId,
		Username:                  CacheGetUsernameById(userId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		ChannelName:               channelName,
		ModelName:                 modelName,
		Quota:                     quota,
		ChannelId:                 channelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  isStream,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		ErrorCode:                 errorCode,
	}
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		err := logManager.RecordLog(ctx, log)
		if err != nil {
			logger.Error(ctx, "<RecordLogByDetail> failed to record log via LogManager: "+err.Error())
			return log
		}
	} else {
		// 使用原有的直接数据库写入方式
		err := LOG_DB.Create(log).Error
		if err != nil {
			logger.Error(ctx, "<RecordLogByDetail> failed to record log and not insert logExtend: "+err.Error())
			return log
		}
	}
	if config.DataExportEnabled {
		helper.SafeGoroutine(func() {
			LogQuotaData(userId, log.Username, modelName, quota,
				helper.GetTimestamp(), promptTokens+completionTokens,
				ip, remoteIp, channelId)
		})
	}
	return log
}

func RecordTopupLog(ctx context.Context, userId int, content string, quota int) {
	log := &Log{
		UserId:    userId,
		Username:  CacheGetUsernameById(userId),
		CreatedAt: helper.GetTimestamp(),
		Type:      LogTypeTopup,
		Content:   content,
		Quota:     quota,
	}
	recordLogHelper(ctx, log)
}

func RecordTestLog(ctx context.Context, log *Log) {
	log.CreatedAt = helper.GetTimestamp()
	log.Type = LogTypeTest
	recordLogHelper(ctx, log)
}
func RecordConsumeLog(ctx context.Context, userId int, channelId int, promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string, quota int, requestDuration int64, isStream bool, content string) *Log {
	username := CacheGetUsernameById(userId)
	logger.Info(ctx, fmt.Sprintf("record consume log: userId=%d, username=%s, channelId=%d, channelName=%s, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d, requestDuration=%d, responseFirstByteDuration=N/A, isStream=%t, ip=N/A, content=%s",
		userId, username, channelId, channelName, promptTokens, completionTokens, modelName, tokenName, quota, requestDuration, isStream, content))
	if !config.LogConsumeEnabled {
		return nil
	}
	log := &Log{
		UserId:           userId,
		Username:         CacheGetUsernameById(userId),
		CreatedAt:        helper.GetTimestamp(),
		Type:             LogTypeConsume,
		Content:          content,
		PromptTokens:     promptTokens,
		CompletionTokens: completionTokens,
		TokenName:        tokenName,
		TokenKey:         tokenKey,
		ChannelName:      channelName,
		ModelName:        modelName,
		Quota:            quota,
		ChannelId:        channelId,
		RequestDuration:  requestDuration,
		IsStream:         isStream,
	}

	// 新增Redis统计
	if common.RedisEnabled && config.NewRPMEnabled {
		helper.SafeGoroutine(func() {
			err := StoreRedisStats(
				userId, modelName, channelId, tokenName, tokenKey, "", "",
				quota, promptTokens+completionTokens,
				requestDuration, 0, 0,
				"",
			)
			if err != nil {
				logger.SysError("Failed to store Redis stats: " + err.Error())
			}
		})
	}
	recordLogHelper(ctx, log)
	if config.DataExportEnabled {
		helper.SafeGoroutine(func() {
			LogQuotaData(userId, log.Username, modelName, quota,
				helper.GetTimestamp(), promptTokens+completionTokens,
				"", "", channelId)
		})
	}
	return log
}
func RecordConsumeLogByDetailAndMeta(ctx context.Context, toFile bool, meta Meta, content string, other string) *Log {
	if ctx == nil {
		ctx = context.Background()
	}
	if meta.RequestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, meta.RequestId)
	}
	username := CacheGetUsernameById(meta.UserId)
	logger.Info(ctx, fmt.Sprintf("record consume log: userId=%d, username=%s, channelId=%d, channelName=%s, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d, totalDuration=%d, responseFirstByteDuration=%d, isStream=%t, ip=%s, content=%s",
		meta.UserId, username, meta.ChannelId, meta.ChannelName, meta.PromptTokens, meta.CompletionTokens, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
		meta.TokenName, meta.Quota, meta.TotalDuration, meta.ResponseFirstByteDuration, meta.IsStream, meta.Ip, content))
	if !config.LogConsumeEnabled {
		return nil
	}
	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}
	log := &Log{
		UserId:                    meta.UserId,
		Username:                  CacheGetUsernameById(meta.UserId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeConsume,
		Content:                   content,
		PromptTokens:              meta.PromptTokens,
		CompletionTokens:          meta.CompletionTokens,
		TokenName:                 meta.TokenName,
		TokenGroup:                meta.TokenGroup,
		TokenKey:                  meta.TokenKey,
		ChannelName:               meta.ChannelName,
		ModelName:                 lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
		Quota:                     int(meta.Quota),
		ChannelId:                 meta.ChannelId,
		TotalDuration:             meta.TotalDuration,
		RequestDuration:           meta.RequestDuration,
		ResponseFirstByteDuration: meta.ResponseFirstByteDuration,
		IsStream:                  meta.IsStream,
		RequestId:                 requestId,
		Ip:                        meta.Ip,
		RemoteIp:                  meta.RemoteIp,
		Other:                     other,
	}

	// 新增Redis统计
	if common.RedisEnabled && config.NewRPMEnabled {
		helper.SafeGoroutine(func() {
			err := StoreRedisStats(
				meta.UserId, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
				meta.ChannelId, meta.TokenName, meta.TokenKey, meta.TokenGroup, meta.Ip,
				int(meta.Quota), meta.PromptTokens+meta.CompletionTokens,
				meta.RequestDuration, meta.ResponseFirstByteDuration, meta.TotalDuration,
				other)
			if err != nil {
				logger.SysError("Failed to store Redis stats: " + err.Error())
			}
		})
	}

	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordConsumeLogByDetailIfZeroQuota> failed to record log and not insert logExtend: "+err.Error())
		return log
	}
	if config.DataExportEnabled {
		helper.SafeGoroutine(func() {
			LogQuotaData(meta.UserId, meta.UserName, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName), int(meta.Quota),
				helper.GetTimestamp(), meta.PromptTokens+meta.CompletionTokens,
				meta.Ip, meta.RemoteIp, meta.ChannelId)
		})
	}
	return log
}

func RecordConsumeLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, xForwardedFor string, xRealIp string, cfConnectingIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, quota int, costQuota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64,
	isStream bool, content string, other string) *Log {
	if ctx == nil {
		ctx = context.Background()
	}
	if requestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, requestId)
	}
	username := CacheGetUsernameById(userId)
	logger.Info(ctx, fmt.Sprintf("record consume log: userId=%d, username=%s, channelId=%d, channelName=%s, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d, totalDuration=%d, responseFirstByteDuration=%d, isStream=%t, ip=%s, content=%s",
		userId, username, channelId, channelName, promptTokens, completionTokens, modelName, tokenName, quota, totalDuration, responseFirstByteDuration, isStream, ip, content))
	if !config.LogConsumeEnabled {
		return nil
	}
	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}
	log := &Log{
		UserId:                    userId,
		Username:                  CacheGetUsernameById(userId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeConsume,
		Content:                   content,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		TokenName:                 tokenName,
		TokenGroup:                tokenGroup,
		TokenKey:                  tokenKey,
		ChannelName:               channelName,
		ModelName:                 modelName,
		Quota:                     quota,
		CostQuota:                 costQuota,
		ChannelId:                 channelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		XForwardedFor:             xForwardedFor,
		XRealIp:                   xRealIp,
		CfConnectingIp:            cfConnectingIp,
		Other:                     other,
	}

	// 新增Redis统计
	if common.RedisEnabled && config.NewRPMEnabled {
		helper.SafeGoroutine(func() {
			err := StoreRedisStats(
				userId, modelName, channelId, tokenName, tokenKey, tokenGroup, ip,
				quota, promptTokens+completionTokens,
				requestDuration, responseFirstByteDuration, totalDuration,
				other,
			)
			if err != nil {
				logger.SysError("Failed to store Redis stats: " + err.Error())
			}
		})
	}

	// 使用 LogManager 记录日志
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		if logManager != nil {
			createdLog, err := logManager.RecordConsumeLogByDetailIfZeroQuota(ctx, requestId, ip, remoteIp, xForwardedFor, xRealIp, cfConnectingIp, userId, channelId,
				promptTokens, completionTokens, modelName, tokenName, tokenKey, tokenGroup, channelName, quota, costQuota,
				requestDuration, responseFirstByteDuration, totalDuration, isStream, content, other)
			if err != nil {
				logger.Error(ctx, "<RecordConsumeLogByDetailIfZeroQuota> failed to record log via LogManager: "+err.Error())
			} else {
				log = createdLog
			}
		} else {
			// LogManager 不可用，使用直接数据库写入
			err := LOG_DB.Create(log).Error
			if err != nil {
				logger.Error(ctx, "<RecordConsumeLogByDetailIfZeroQuota> failed to record log via direct DB: "+err.Error())
			}
		}
	} else {
		// 日志存储未启用，使用传统方式
		err := LOG_DB.Create(log).Error
		if err != nil {
			logger.Error(ctx, "<RecordConsumeLogByDetailIfZeroQuota> failed to record log and not insert logExtend: "+err.Error())
		}
	}
	if config.DataExportEnabled {
		helper.SafeGoroutine(func() {
			LogQuotaData(userId, log.Username, modelName, quota,
				helper.GetTimestamp(), promptTokens+completionTokens,
				ip, remoteIp, channelId)
		})
	}
	return log
}

func RecordRefundLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, xForwardedFor string, xRealIp string, cfConnectingIp string, userId int, channelId int, promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string) *Log {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		if logManager != nil {
			createdLog, err := logManager.RecordRefundLogByDetailIfZeroQuota(ctx, requestId, ip, remoteIp, xForwardedFor, xRealIp, cfConnectingIp, userId, channelId,
				promptTokens, completionTokens, modelName, tokenName, tokenKey, channelName, quota, requestDuration,
				responseFirstByteDuration, totalDuration, isStream, content)
			if err != nil {
				logger.Error(ctx, "<RecordRefundLogByDetailIfZeroQuota> failed to record log via LogManager: "+err.Error())
				// 回退到直接数据库写入
			} else {
				// 成功使用LogManager，处理数据导出
				if config.DataExportEnabled && createdLog != nil {
					helper.SafeGoroutine(func() {
						LogQuotaData(userId, createdLog.Username, modelName, quota,
							helper.GetTimestamp(), promptTokens+completionTokens,
							ip, remoteIp, channelId)
					})
				}
				return createdLog
			}
		}
	}

	// 使用原有的直接数据库写入方式
	if ctx == nil {
		ctx = context.Background()
	}
	if requestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, requestId)
	}
	username := CacheGetUsernameById(userId)
	logger.Info(ctx, fmt.Sprintf("record Refund log: userId=%d, username=%s, channelId=%d, channelName=%s, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d, totalDuration=%d, responseFirstByteDuration=%d, isStream=%t, ip=%s, content=%s",
		userId, username, channelId, channelName, promptTokens, completionTokens, modelName, tokenName, quota, totalDuration, responseFirstByteDuration, isStream, ip, content))
	if !config.LogConsumeEnabled {
		return nil
	}
	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}
	log := &Log{
		UserId:                    userId,
		Username:                  CacheGetUsernameById(userId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeRefund,
		Content:                   content,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		ChannelName:               channelName,
		ModelName:                 modelName,
		Quota:                     quota,
		ChannelId:                 channelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		XForwardedFor:             xForwardedFor,
		XRealIp:                   xRealIp,
		CfConnectingIp:            cfConnectingIp,
	}
	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordRefundLogByDetailIfZeroQuota> failed to record log and not insert logExtend: "+err.Error())
		return log
	}
	if config.DataExportEnabled {
		helper.SafeGoroutine(func() {
			LogQuotaData(userId, log.Username, modelName, quota,
				helper.GetTimestamp(), promptTokens+completionTokens,
				ip, remoteIp, channelId)
		})
	}
	return log
}

func RecordSysLog(ctx context.Context, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) {
	// 根据日志类型检查对应的开关
	if (logType == LogTypeConsume && !config.LogConsumeEnabled) ||
		((logType == LogTypeDownstreamError || logType == LogTypeSystemErr) && !config.LogErrorEnabled) ||
		(logType == LogTypeSystemInfo && !config.LogSysInfoEnabled) {
		return
	}

	log := &Log{
		UserId:      userId,
		Username:    CacheGetUsernameById(userId),
		CreatedAt:   helper.GetTimestamp(),
		Type:        logType,
		Content:     content,
		TokenName:   tokenName,
		ChannelName: channelName,
		ModelName:   modelName,
		ChannelId:   channelId,
	}
	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordSysLog> failed to record log and not insert logExtend: "+err.Error())
		return
	}
	if prompt == "" {
		// 提示词为空不存extend
		return
	}

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, maxPromptLogLength, modelName)
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(ctx, "<RecordSysLog> failed to record logExtend: "+err.Error())
		return
	}
}

// TruncateOptimized 优化版截断函数，能够处理普通文本和二进制内容
func TruncateOptimized(s string, maxLength int64, modelName string) string {
	// 如果长度限制为0或负数，表示不限制
	if maxLength <= 0 {
		return s
	}

	if isBinaryModel(modelName) {
		// 如果是二进制模型，直接显示[二进制模型数据，已跳过详细内容保存]
		return "[二进制模型数据，已跳过详细内容保存]"
	}
	// 快速路径：如果字节长度小于限制，直接返回
	if int64(len(s)) <= maxLength {
		return s
	}

	// 检查是否为多部分表单数据（通常包含二进制文件）
	if len(s) > 30 && strings.HasPrefix(s, "--") {
		boundaryEnd := strings.Index(s, "\r\n")
		if boundaryEnd == -1 {
			boundaryEnd = strings.Index(s, "\n")
		}

		if boundaryEnd > 2 && boundaryEnd < 100 {
			//boundary := s[:boundaryEnd]

			// 如果内容中包含表单文件特征
			if strings.Contains(s, "Content-Disposition: form-data;") &&
				strings.Contains(s, "filename=") {

				// 只保留表单的前1KB内容，包含了大部分元数据
				maxMetaSize := int64(1024)
				if len(s) > int(maxMetaSize) {
					return "[多部分表单数据，包含文件，已跳过保存]"
				}
			}
		}
	}

	// 对于普通文本的截断，使用rune处理
	runes := []rune(s)
	if len(runes) <= int(maxLength) {
		return s
	}
	return string(runes[:maxLength]) + "...(truncated)"
}

// 判断模型是否处理二进制数据
func isBinaryModel(modelName string) bool {
	if modelName == "" {
		return false
	}

	modelNameLower := strings.ToLower(modelName)
	binaryModelKeywords := []string{
		"whisper", "tts", "speech", "audio", "voice", "image", "图像", "语音", "音频", "视觉",
	}

	for _, keyword := range binaryModelKeywords {
		if strings.Contains(modelNameLower, keyword) {
			return true
		}
	}

	return false
}

// 判断内容是否为二进制数据
func isBinaryContent(content string) bool {
	// 对于短内容，不做特殊处理
	if len(content) < 100 {
		return false
	}

	// 如果明确包含二进制文件标识，直接判定为二进制
	if strings.Contains(content, "Content-Type: application/octet-stream") ||
		strings.Contains(content, "filename=") && strings.Contains(content, "Content-Type:") {
		return true
	}

	// 取样前1000个字符（或全部）检测不可打印字符的比例
	sampleSize := 1000
	if len(content) < sampleSize {
		sampleSize = len(content)
	}

	sample := content[:sampleSize]
	nonPrintableCount := 0

	for _, r := range sample {
		// 检查是否为不可打印字符或控制字符（ASCII 0-31，除了常见的\n,\r,\t）
		if (r < 32 && r != '\n' && r != '\r' && r != '\t') || r > 126 {
			nonPrintableCount++
		}
	}

	// 如果不可打印字符比例超过15%，认为是二进制内容
	return (float64(nonPrintableCount) / float64(sampleSize)) > 0.15
}

// 从表单数据中提取元数据部分
func extractFormMetadata(content string) string {
	result := ""

	// 检查是否为多部分表单数据
	boundaryMatch := regexp.MustCompile(`-{2,}[a-zA-Z0-9]+`).FindString(content)
	if boundaryMatch == "" {
		return result
	}

	// 提取各部分表单数据
	parts := strings.Split(content, boundaryMatch)
	for i, part := range parts {
		if i == 0 && len(part) == 0 {
			continue // 跳过空的第一部分
		}

		// 检查这部分是否包含文件数据
		if strings.Contains(part, "Content-Disposition: form-data;") &&
			strings.Contains(part, "filename=") {
			// 找到文件内容开始的位置
			headerEnd := strings.Index(part, "\r\n\r\n")
			if headerEnd == -1 {
				headerEnd = strings.Index(part, "\n\n")
			}

			if headerEnd != -1 {
				// 只保留头部信息
				headerPart := part[:headerEnd+4] // +4 保留分隔符 \r\n\r\n
				result += boundaryMatch + headerPart + "[二进制文件内容已省略]\r\n"
			} else {
				result += boundaryMatch + part
			}
		} else {
			// 非文件部分完整保留
			result += boundaryMatch + part
		}
	}

	return result
}

// 检查是否需要忽略该错误码
func shouldIgnoreErrorCode(errorCode string) bool {
	if errorCode == "" {
		return false
	}
	_, exists := getIgnoredErrorCodes()[errorCode]
	return exists
}

// 检查是否需要记录该错误（基于采样率）
func shouldSampleError(errorCode string) bool {
	if config.LogErrorSamplingRate <= 0 {
		return false
	}
	if config.LogErrorSamplingRate >= 1 {
		return true
	}

	// 使用原子计数器进行采样
	counter := atomic.AddUint64(&samplingCounter, 1)
	return counter%uint64(1/config.LogErrorSamplingRate) == 0
}

// 修改现有的错误日志记录函数
func RecordSysLogToDBAndFile(ctx context.Context, requestId string, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		err := logManager.RecordSysLogToDBAndFile(ctx, requestId, logType, userId, channelId, modelName, tokenName, channelName, content, prompt)
		if err != nil {
			logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record log via LogManager: "+err.Error())
		}
		return
	}

	// 使用原有的直接数据库写入方式
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return
		}

		// 获取错误码
		errorCode := getErrorCodeFromContext(ctx)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return
		}

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, modelName)
		if prompt != "" {
			prompt = TruncateOptimized(prompt, maxPromptLogLength, modelName)
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}
	if !config.LogConsumeEnabled {
		return
	}
	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}
	if requestId == "" {
		requestId1, ok := ctx.Value(helper.RequestIdKey).(string)
		if ok {
			requestId = requestId1
		}
	}
	log := &Log{
		RequestId:   requestId,
		UserId:      userId,
		Username:    CacheGetUsernameById(userId),
		CreatedAt:   helper.GetTimestamp(),
		Type:        logType,
		Content:     content,
		TokenName:   tokenName,
		ChannelName: channelName,
		ModelName:   modelName,
		ChannelId:   channelId,
		ErrorCode:   getErrorCodeFromContext(ctx), // 从context中获取错误码
	}
	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record log and not insert logExtend: "+err.Error())
		return
	}

	if prompt == "" {
		// 提示词为空不存extend
		return
	}

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, maxPromptLogLength, modelName)
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record logExtend: "+err.Error())
		return
	}
}

func RecordSysLogToDBAndFileByGinContext(c *gin.Context, logType int, content string, prompt string) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		err := logManager.RecordSysLogToDBAndFileByGinContext(c, logType, content, prompt)
		if err != nil {
			logger.Error(nil, "<RecordSysLogToDBAndFileByGinContext> failed to record log via LogManager: "+err.Error())
		}
		return
	}

	// 使用原有的直接数据库写入方式
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return
		}

		// 获取错误码
		errorCode := c.GetString(ctxkey.ErrorCode)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return
		}

		// 获取用户ID
		userId := c.GetInt(ctxkey.Id)

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, c.GetString(ctxkey.RequestModel))
		if prompt != "" {
			prompt = TruncateOptimized(prompt, maxPromptLogLength, c.GetString(ctxkey.RequestModel))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}
	if !config.LogConsumeEnabled {
		return
	}
	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}
	failedDuration := c.GetInt64(ctxkey.FailedDuration)
	var requestDuration int64
	var responseFirstByteDuration int64
	var totalDuration int64
	if failedDuration > 0 {
		requestDuration = failedDuration
		responseFirstByteDuration = failedDuration
		totalDuration = failedDuration
	}

	log := &Log{
		RequestId:                 c.GetString(helper.RequestIdKey),
		UserId:                    c.GetInt(ctxkey.Id),
		Username:                  CacheGetUsernameById(c.GetInt(ctxkey.Id)),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		TokenName:                 c.GetString(ctxkey.TokenName),
		TokenKey:                  c.GetString(ctxkey.TokenKey),
		TokenGroup:                c.GetString(ctxkey.TokenGroup),
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		ChannelName:               c.GetString(ctxkey.ChannelName),
		ModelName:                 c.GetString(ctxkey.RequestModel),
		ChannelId:                 c.GetInt(ctxkey.ChannelId),
		Ip:                        c.ClientIP(),                    // 直接使用Gin的ClientIP
		RemoteIp:                  c.RemoteIP(),                    // Gin的RemoteIP
		XForwardedFor:             c.GetHeader("X-Forwarded-For"),  // X-Forwarded-For头
		XRealIp:                   c.GetHeader("X-Real-IP"),        // X-Real-IP头
		CfConnectingIp:            c.GetHeader("CF-Connecting-IP"), // CloudFlare IP头
		ErrorCode:                 c.GetString(ctxkey.ErrorCode),   // 添加错误码
	}
	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(nil, "<RecordLogToDBAndFileByGinContext> failed to record log and not insert logExtend: "+err.Error())
		return
	}

	if prompt == "" {
		// 提示词为空不存extend
		return
	}

	// 获取用户ID
	userId := c.GetInt(ctxkey.Id)

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, maxPromptLogLength, c.GetString(ctxkey.RequestModel))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(nil, "<RecordLogToDBAndFileByGinContext> failed to record logExtend: "+err.Error())
		return
	}
}

func RecordLogToDBAndFileByMeta(ctx context.Context, logType int, toFile bool, meta Meta, content string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		err := logManager.RecordLogToDBAndFileByMeta(ctx, logType, toFile, meta, content, quota, requestDuration, responseFirstByteDuration, totalDuration)
		if err != nil {
			logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record log via LogManager: "+err.Error())
		}
		return
	}

	// 使用原有的直接数据库写入方式
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return
		}

		// 获取错误码
		errorCode := meta.ErrorCode

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return
		}

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		if meta.DetailPrompt != "" {
			meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return
	}

	// 根据日志类型判断记录什么类型的日志文件
	if toFile {
		switch logType {
		case LogTypeSystemInfo:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		case LogTypeSystemErr:
			logger.SysError(fmt.Sprintf("系统错误: %s", content))
		default:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		}
	}
	if !config.LogConsumeEnabled {
		return
	}
	if ctx == nil {
		ctx = context.Background()
	}
	if meta.RequestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, meta.RequestId)
	}
	log := &Log{
		UserId:                    meta.UserId,
		Username:                  CacheGetUsernameById(meta.UserId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		PromptTokens:              meta.PromptTokens,
		CompletionTokens:          meta.CompletionTokens,
		TokenName:                 meta.TokenName,
		TokenKey:                  meta.TokenKey,
		TokenGroup:                meta.TokenGroup,
		ChannelName:               meta.ChannelName,
		ModelName:                 lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
		Quota:                     quota,
		ChannelId:                 meta.ChannelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  meta.IsStream,
		RequestId:                 meta.RequestId,
		Ip:                        meta.Ip,
		RemoteIp:                  meta.RemoteIp,
		ErrorCode:                 meta.ErrorCode,
	}
	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordSysLog> failed to record log and not insert logExtend: "+err.Error())
		return
	}
	if meta.DetailPrompt == "" {
		// 提示词为空不存extend
		return
	}

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    meta.DetailPrompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(ctx, "<RecordSysLog> failed to record logExtend: "+err.Error())
		return
	}
}

func GetAllLogs(userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64, modelName string, username string,
	tokenName string, tokenKey string, tokenGroup string, channelName string, startIdx int, num int, channel int, isStream string,
	requestId string, ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64, requestDurationMax *float64,
	responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64, excludeModels []string, errorCode string,
	excludeErrorCodes []string, quotaMin *int, quotaMax *int) (logs []*Log, err error) {

	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.GetAllLogs(userId, timezone, logType, startTimestamp, endTimestamp,
			modelName, username, tokenName, tokenKey, tokenGroup, channelName, startIdx, num,
			channel, isStream, requestId, ip, promptTokensMin, promptTokensMax, completionTokensMin,
			completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
			requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
			excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)
	}

	// 使用原有的直接数据库查询方式
	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = LOG_DB.Where("type in ?", logType)
	} else {
		tx = LOG_DB
	}

	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if username != "" {
		tx = tx.Where("username = ?", username)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		// 这个传入的是 sk-xxx，但是数据库储存的是 xxx，首先判断是不是，如果不是，那就不动，如果是，那就截取
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if tokenGroup != "" {
		tx = tx.Where("token_group = ?", tokenGroup)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if channelName != "" {
		tx = tx.Where("channel_name = ?", channelName)
	}
	if channel != 0 {
		tx = tx.Where("channel_id = ?", channel)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}
	if ip != "" {
		// 智能IP搜索：在所有IP相关字段中搜索
		tx = tx.Where("ip = ? OR remote_ip = ? OR x_forwarded_for LIKE ? OR x_real_ip = ? OR cf_connecting_ip = ?",
			ip, ip, "%"+ip+"%", ip, ip)
	}
	if promptTokensMin != nil {
		tx = tx.Where("prompt_tokens >= ?", *promptTokensMin)
	}
	if promptTokensMax != nil {
		tx = tx.Where("prompt_tokens <= ?", *promptTokensMax)
	}
	if completionTokensMin != nil {
		tx = tx.Where("completion_tokens >= ?", *completionTokensMin)
	}
	if completionTokensMax != nil {
		tx = tx.Where("completion_tokens <= ?", *completionTokensMax)
	}
	if totalDurationMin != nil {
		tx = tx.Where("total_duration >= ?", *totalDurationMin)
	}
	if totalDurationMax != nil {
		tx = tx.Where("total_duration <= ?", *totalDurationMax)
	}
	if requestDurationMin != nil {
		tx = tx.Where("request_duration >= ?", *requestDurationMin)
	}
	if requestDurationMax != nil {
		tx = tx.Where("request_duration <= ?", *requestDurationMax)
	}
	if responseFirstByteDurationMin != nil {
		tx = tx.Where("response_first_byte_duration >= ?", *responseFirstByteDurationMin)
	}
	if responseFirstByteDurationMax != nil {
		tx = tx.Where("response_first_byte_duration <= ?", *responseFirstByteDurationMax)
	}
	if errorCode != "" {
		tx = tx.Where("error_code = ?", errorCode)
	}
	if len(excludeErrorCodes) > 0 {
		tx = tx.Where("error_code NOT IN (?)", excludeErrorCodes)
	}
	// 只有admin才显示渠道和成本信息
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr())
		tx = tx.Omit(constants.LogFieldsExclude...)
	}

	// 添加排除模型的逻辑
	if len(excludeModels) > 0 {
		tx = tx.Where("model_name NOT IN (?)", excludeModels)
	}

	// 添加quota范围筛选
	if quotaMin != nil {
		tx = tx.Where("quota >= ?", *quotaMin)
	}
	if quotaMax != nil {
		tx = tx.Where("quota <= ?", *quotaMax)
	}

	err = tx.Order("id desc").Limit(num).Offset(startIdx).Find(&logs).Error
	return logs, err
}

func CountAllLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string,
	username string, tokenName string, tokenKey string, tokenGroup string, channelName string, channel int, isStream string,
	requestId string, ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (count int64, err error) {

	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.CountAllLogs(userId, logType, startTimestamp, endTimestamp,
			modelName, username, tokenName, tokenKey, tokenGroup, channelName, channel,
			isStream, requestId, ip, promptTokensMin, promptTokensMax, completionTokensMin,
			completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
			requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
			excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)
	}

	// 使用原有的直接数据库查询方式
	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = LOG_DB.Where("type in ?", logType)
	} else {
		tx = LOG_DB
	}
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if username != "" {
		tx = tx.Where("username = ?", username)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if tokenGroup != "" {
		tx = tx.Where("token_group = ?", tokenGroup)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if channelName != "" {
		tx = tx.Where("channel_name = ?", channelName)
	}
	if channel != 0 {
		tx = tx.Where("channel_id = ?", channel)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}
	if ip != "" {
		// 智能IP搜索：在所有IP相关字段中搜索
		tx = tx.Where("ip = ? OR remote_ip = ? OR x_forwarded_for LIKE ? OR x_real_ip = ? OR cf_connecting_ip = ?",
			ip, ip, "%"+ip+"%", ip, ip)
	}
	if promptTokensMin != nil {
		tx = tx.Where("prompt_tokens >= ?", *promptTokensMin)
	}
	if promptTokensMax != nil {
		tx = tx.Where("prompt_tokens <= ?", *promptTokensMax)
	}
	if completionTokensMin != nil {
		tx = tx.Where("completion_tokens >= ?", *completionTokensMin)
	}
	if completionTokensMax != nil {
		tx = tx.Where("completion_tokens <= ?", *completionTokensMax)
	}
	if totalDurationMin != nil {
		tx = tx.Where("total_duration >= ?", *totalDurationMin)
	}
	if totalDurationMax != nil {
		tx = tx.Where("total_duration <= ?", *totalDurationMax)
	}
	if requestDurationMin != nil {
		tx = tx.Where("request_duration >= ?", *requestDurationMin)
	}
	if requestDurationMax != nil {
		tx = tx.Where("request_duration <= ?", *requestDurationMax)
	}
	if responseFirstByteDurationMin != nil {
		tx = tx.Where("response_first_byte_duration >= ?", *responseFirstByteDurationMin)
	}
	if responseFirstByteDurationMax != nil {
		tx = tx.Where("response_first_byte_duration <= ?", *responseFirstByteDurationMax)
	}
	if errorCode != "" {
		tx = tx.Where("error_code = ?", errorCode)
	}
	if len(excludeErrorCodes) > 0 {
		tx = tx.Where("error_code NOT IN (?)", excludeErrorCodes)
	}
	// 只有admin才显示渠道
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr())
		tx = tx.Omit(constants.LogFieldsExclude...)
	}

	// 添加排除模型的逻辑
	if len(excludeModels) > 0 {
		tx = tx.Where("model_name NOT IN (?)", excludeModels)
	}

	// 添加quota范围筛选
	if quotaMin != nil {
		tx = tx.Where("quota >= ?", *quotaMin)
	}
	if quotaMax != nil {
		tx = tx.Where("quota <= ?", *quotaMax)
	}

	err = tx.Model(&Log{}).Count(&count).Error
	return count, err
}

func GetUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string, tokenName string, tokenKey string, startIdx int, num int, isStream string, requestId string) (logs []*Log, err error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.GetUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, startIdx, num, isStream, requestId)
	}

	// 使用原有的直接数据库查询方式
	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = LOG_DB.Where("type in ?", logType)
	} else {
		tx = LOG_DB
	}
	tx = tx.Where("user_id = ?", userId)
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	omitFields := []string{"id"}
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr()) // 添加了 LogTypeDownstreamError (15)
		omitFields = append(omitFields, constants.LogFieldsExclude...)
	}

	err = tx.Order("id desc").Limit(num).Offset(startIdx).Omit(omitFields...).Find(&logs).Error
	return logs, err
}

func SearchAllLogs(keyword string) (logs []*Log, err error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.SearchAllLogs(keyword)
	}

	// 使用原有的直接数据库查询方式
	err = LOG_DB.Where("type = ? or content LIKE ?", keyword, keyword+"%").Order("id desc").Limit(config.MaxRecentItems).Find(&logs).Error
	return logs, err
}

func SearchUserLogs(userId int, keyword string) (logs []*Log, err error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.SearchUserLogs(userId, keyword)
	}

	// 使用原有的直接数据库查询方式
	err = LOG_DB.Where("user_id = ? and type = ?", userId, keyword).Order("id desc").Limit(config.MaxRecentItems).Omit("id").Find(&logs).Error
	return logs, err
}

func SearchUserLogsByKey(key string, startIdx int, num int) (logs []*Log, err error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.SearchUserLogsByKey(key, startIdx, num)
	}

	// 使用原有的直接数据库查询方式
	err = LOG_DB.Where("token_key = ? and type = 2", key).Order("id desc").Limit(config.MaxRecentItems).
		Omit("id", "channel_name", "channel_id", "content", "user_id", "username", "token_name").
		Limit(num).Offset(startIdx).Find(&logs).Error
	return logs, err
}
func CountUserLogsByKey(key string) (count int64, err error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.CountUserLogsByKey(key)
	}

	// 使用原有的直接数据库查询方式
	err = LOG_DB.Model(&Log{}).Where("token_key = ?", key).Count(&count).Error
	return count, err
}

func CountUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string, tokenName string, tokenKey string, isStream string, requestId string) (count int64, err error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.CountUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, isStream, requestId)
	}

	// 使用原有的直接数据库查询方式
	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = LOG_DB.Where("type in ?", logType)
	} else {
		tx = LOG_DB
	}
	tx = tx.Where("user_id = ?", userId)
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr())
	}

	err = tx.Model(&Log{}).Count(&count).Error
	return count, err
}

type Stat struct {
	Quota          int     `json:"quota"`
	Rpm            int     `json:"rpm"`
	Tpm            int     `json:"tpm"`
	Mpm            float64 `json:"mpm"`
	IsRealtimeData bool    `json:"is_realtime_data"` // 新增字段，标识是否为实时数据
}
type ModelUsage struct {
	ModelName string `json:"modelName"`
	Cnt       int    `json:"cnt"`
}
type DailyModelUsageStats struct {
	ModelName string  `json:"modelName"`
	UserId    string  `json:"userId"`
	Username  string  `json:"username"`
	Ip        string  `json:"ip"`
	RemoteIp  string  `json:"remote_ip"`
	SumQuota  int     `json:"sumQuota"`  // 总积分
	SumUsd    float64 `json:"sumUsd"`    // 总积分对应的美元
	CostQuota int     `json:"costQuota"` // 成本积分
	CostUsd   float64 `json:"costUsd"`   // 成本美元
	Date      string  `json:"date"`
}

func SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, modelName string, username string, tokenName string, tokenKey string, tokenGroup string, channel int, useRedis bool) (stat Stat) {
	// 使用原有的直接数据库查询方式
	if common.RedisEnabled && config.NewRPMEnabled && useRedis {
		// 构建查询条件
		conditions := make(map[string]string)
		if modelName != "" {
			conditions["model"] = modelName
		}
		if channel != 0 {
			conditions["channel"] = strconv.Itoa(channel)
		}
		if username != "" {
			// 根据 username 获取 userId
			user, err := GetUserByName(username)
			if err != nil {
				logger.Error(nil, "Failed to get userId by username: "+err.Error())
				conditions["user"] = "-1"
			} else {
				conditions["user"] = strconv.Itoa(user.Id)
			}
		}
		if tokenName != "" {
			conditions["token_name"] = tokenName
		}
		if tokenKey != "" {
			if strings.HasPrefix(tokenKey, "sk-") {
				tokenKey = strings.Split(tokenKey, "-")[1]
			}
			conditions["token_key"] = tokenKey
		}
		if tokenGroup != "" {
			conditions["token_group"] = tokenGroup
		}

		// 使用新版RPM统计
		stats, err := GetRedisStats(conditions)
		if err == nil {
			stat.Rpm = stats.Requests
			stat.Quota = stats.Quota
			stat.Tpm = stats.Tokens
			stat.Mpm = float64(stats.Quota) / 500000
			stat.IsRealtimeData = true // 标记为实时数据
			return stat
		}
		// 如果Redis查询失败，继续使用MySQL查询（降级处理）
		logger.Error(nil, "Failed to get Redis stats: "+err.Error())
	}

	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		result, err := logManager.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, useRedis)
		if err != nil {
			logger.Error(context.Background(), "LogManager SumUsedQuota failed: "+err.Error())
			// 继续使用原有逻辑作为fallback
		} else {
			return result
		}
	}

	// 降级到MySQL查询（保持原有逻辑）
	baseQuery := LOG_DB.Table("logs")
	logConsumedTypes := []int{LogTypeConsume, LogTypeRefund}
	// 添加共同条件
	if username != "" {
		baseQuery = baseQuery.Where("username = ?", username)
	}
	if tokenName != "" {
		baseQuery = baseQuery.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		baseQuery = baseQuery.Where("token_key = ?", tokenKey)
	}
	if tokenGroup != "" {
		baseQuery = baseQuery.Where("token_group = ?", tokenGroup)
	}
	if modelName != "" {
		baseQuery = baseQuery.Where("model_name = ?", modelName)
	}
	if channel != 0 {
		baseQuery = baseQuery.Where("channel_id = ?", channel)
	}
	// 计算 quota
	quotaQuery := baseQuery.Select("sum(quota) quota")
	if startTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at <= ?", endTimestamp)
	}
	quotaQuery.Where("type in (?)", logConsumedTypes).Scan(&stat)

	// 获取当前时间，并计算一分钟之前的时间
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute).Unix()

	// 计算 rpm 和 tpm
	rpmTpmQuery := baseQuery.Select("count(*) rpm, sum(prompt_tokens) + sum(completion_tokens) tpm, (sum(quota) / 500000) mpm")
	rpmTpmQuery = rpmTpmQuery.Where("type in (?)", logConsumedTypes).Where("created_at >= ?", oneMinuteAgo).Where("created_at <= ?", now.Unix())

	var rpmTpmStat Stat
	rpmTpmQuery.Scan(&rpmTpmStat)

	// 合并结果
	stat.Rpm = rpmTpmStat.Rpm
	stat.Tpm = rpmTpmStat.Tpm
	stat.Mpm = rpmTpmStat.Mpm
	stat.IsRealtimeData = false // 标记为历史数据

	return stat
}

func SumUsedQuotaByKey(key string, startTimestamp int64, endTimestamp int64) (stat Stat) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		result, err := logManager.SumUsedQuotaByKey(key, startTimestamp, endTimestamp)
		if err != nil {
			logger.Error(context.Background(), "LogManager SumUsedQuotaByKey failed: "+err.Error())
			// 继续使用原有逻辑作为fallback
		} else {
			return result
		}
	}

	// 使用原有的直接数据库查询方式
	logConsumedTypes := []int{LogTypeConsume, LogTypeRefund}

	baseQuery := LOG_DB.Table("logs")
	// 添加共同条件
	baseQuery = baseQuery.Where("token_key = ?", key)

	// 计算 quota
	quotaQuery := baseQuery.Select("sum(quota) quota")
	if startTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at <= ?", endTimestamp)
	}
	quotaQuery.Where("type in (?)", logConsumedTypes).Scan(&stat)

	// 获取当前时间，并计算一分钟之前的时间
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute).Unix()

	// 计算 rpm 和 tpm
	rpmTpmQuery := baseQuery.Select("count(*) rpm, sum(prompt_tokens) + sum(completion_tokens) tpm , (sum(quota) / 500000) mpm")
	rpmTpmQuery = rpmTpmQuery.Where("created_at >= ?", oneMinuteAgo).Where("created_at <= ?", now.Unix())

	var rpmTpmStat Stat
	rpmTpmQuery.Scan(&rpmTpmStat)

	// 合并结果
	stat.Rpm = rpmTpmStat.Rpm
	stat.Tpm = rpmTpmStat.Tpm
	stat.Mpm = rpmTpmStat.Mpm

	return stat
}

func SumAllDailyUsageStatsByDimension(userId int, timezone string, tokenName string, username string, channel int, channelName string, modelName string, startTimestamp int64, endTimestamp int64, dimension string, granularity string) (results []*DailyModelUsageStats) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		result, err := logManager.SumAllDailyUsageStatsByDimension(userId, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)
		if err != nil {
			logger.Error(context.Background(), "LogManager SumAllDailyUsageStatsByDimension failed: "+err.Error())
			// 继续使用原有逻辑作为fallback
		} else {
			return result
		}
	}

	// 使用原有的直接数据库查询方式
	query := LOG_DB.Table("logs")
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}
	if tokenName != "" {
		query = query.Where("token_name = ?", tokenName)
	}
	if username != "" {
		query = query.Where("username = ?", username)
	}
	if channel != 0 {
		query = query.Where("channel_id = ?", channel)
	}
	if channelName != "" {
		query = query.Where("channel_name = ?", channelName)
	}
	if modelName != "" {
		query = query.Where("model_name = ?", modelName)
	}

	var sqlGroupDimension, sqlSelectDimension string
	switch dimension {
	case "model":
		sqlGroupDimension = "model_name"
		sqlSelectDimension = "model_name"
	case "user":
		sqlGroupDimension = "user_id, username"
		sqlSelectDimension = "user_id, username"
	case "ip":
		sqlGroupDimension = "ip"
		sqlSelectDimension = "ip"
	case "cost_by_user":
		sqlGroupDimension = "user_id, username"
		sqlSelectDimension = "user_id, username"
	case "cost_by_ip":
		sqlGroupDimension = "ip"
		sqlSelectDimension = "ip"
	default:
		sqlGroupDimension = "model_name"
		sqlSelectDimension = "model_name"
	}

	var dateFormat string
	var whereClause string

	if timezone == "" {
		timezone = "UTC"
	}

	if IsLogDBSQLite() {
		switch granularity {
		case "hour":
			dateFormat = "strftime('%Y-%m-%d %H:00', datetime(created_at, 'unixepoch'))"
		case "week":
			dateFormat = "strftime('%Y-W%W', datetime(created_at, 'unixepoch'))"
		case "month":
			dateFormat = "strftime('%Y-%m', datetime(created_at, 'unixepoch'))"
		default: // day
			dateFormat = "date(created_at, 'unixepoch')"
		}
		whereClause = "model_name != '' AND created_at >= strftime('%s', 'now', '-14 days')"
	} else if IsLogDBPostgreSQL() {
		switch granularity {
		case "hour":
			dateFormat = fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM-DD HH24:00')", timezone)
		case "week":
			dateFormat = fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-WW')", timezone)
		case "month":
			dateFormat = fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM')", timezone)
		default: // day
			dateFormat = fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM-DD')", timezone)
		}
		whereClause = "model_name != '' AND created_at >= extract(epoch from (now() AT TIME ZONE 'UTC' - INTERVAL '14 days'))"
	} else {
		// MySQL: 尝试使用 CONVERT_TZ，如果失败则回退到时间戳偏移方法
		var testResult string
		testQuery := fmt.Sprintf("SELECT CONVERT_TZ(NOW(), '+00:00', '%s')", timezone)
		err := LOG_DB.Raw(testQuery).Scan(&testResult).Error
		if err != nil || testResult == "" {
			// CONVERT_TZ 不可用，使用时间戳偏移方法
			timezoneOffset := getTimezoneOffset(timezone)
			switch granularity {
			case "hour":
				dateFormat = fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%m-%%d %%H:00')", timezoneOffset)
			case "week":
				dateFormat = fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%u')", timezoneOffset)
			case "month":
				dateFormat = fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%m')", timezoneOffset)
			default: // day
				dateFormat = fmt.Sprintf("DATE(FROM_UNIXTIME(created_at + %d))", timezoneOffset)
			}
			whereClause = fmt.Sprintf("model_name != '' AND created_at >= UNIX_TIMESTAMP(NOW() - INTERVAL 14 DAY) - %d", timezoneOffset)
		} else {
			// CONVERT_TZ 可用
			switch granularity {
			case "hour":
				dateFormat = fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%m-%%d %%H:00')", timezone)
			case "week":
				dateFormat = fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%u')", timezone)
			case "month":
				dateFormat = fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%m')", timezone)
			default: // day
				dateFormat = fmt.Sprintf("DATE(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'))", timezone)
			}
			whereClause = fmt.Sprintf("model_name != '' AND created_at >= UNIX_TIMESTAMP(CONVERT_TZ(NOW() - INTERVAL 14 DAY, '+00:00', '%s'))", timezone)
		}
	}

	// 根据维度选择不同的统计字段
	var selectFields string
	if strings.HasPrefix(dimension, "cost_") {
		selectFields = fmt.Sprintf(`
			%s, 
			%s as date, 
			SUM(cost_quota) as cost_quota,
			SUM(cost_quota)/500000 as cost_usd
		`, sqlSelectDimension, dateFormat)
	} else {
		selectFields = fmt.Sprintf(`
			%s, 
			%s as date, 
			SUM(quota) as sum_quota,
			SUM(quota)/500000 as sum_usd
		`, sqlSelectDimension, dateFormat)
	}

	query = query.Select(selectFields).Where(whereClause)

	if startTimestamp != 0 {
		query = query.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		query = query.Where("created_at <= ?", endTimestamp)
	}

	query = query.Group(fmt.Sprintf("%s, %s", sqlGroupDimension, dateFormat)).
		Order(fmt.Sprintf("%s ASC, SUM(quota) DESC", dateFormat))

	err := query.Find(&results).Error

	if err != nil {
		logger.SysError("failed to get model usage stats: " + err.Error())
		return results
	}

	// 如果使用 SQLite，在应用层处理时区转换
	if IsLogDBSQLite() {
		loc, _ := time.LoadLocation(timezone)
		for i, result := range results {
			t, _ := time.Parse("2006-01-02", result.Date)
			localT := t.In(loc)
			results[i].Date = localT.Format("2006-01-02")
		}
	}

	return results
}

// 获取时区偏移量（秒）
func getTimezoneOffset(timezone string) int {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return 0
	}
	now := time.Now().In(loc)
	_, offset := now.Zone()
	return offset
}

func SumAllModelUsage(logType int, timezone string, startTimestamp int64, endTimestamp int64, modelName string, username string, tokenName string, channel int, channelName string) (modelUsage []*ModelUsage) {
	baseQuery := LOG_DB.Table("logs")
	baseQuery = baseQuery.Select("model_name,COUNT(1) cnt")
	// 添加共同条件
	if username != "" {
		baseQuery = baseQuery.Where("username = ?", username)
	}
	if tokenName != "" {
		baseQuery = baseQuery.Where("token_name = ?", tokenName)
	}
	if modelName != "" {
		baseQuery = baseQuery.Where("model_name = ?", modelName)
	}
	if channel != 0 {
		baseQuery = baseQuery.Where("channel_id = ?", channel)
	}
	if channelName != "" {
		baseQuery = baseQuery.Where("channel_name = ?", channelName)
	}
	if startTimestamp != 0 {
		baseQuery = baseQuery.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		baseQuery = baseQuery.Where("created_at <= ?", endTimestamp)
	}

	// 如果开始时间和结束时间都没有传,则默认查询最近一个月的数据
	if startTimestamp == 0 && endTimestamp == 0 {
		baseQuery = baseQuery.Where("created_at >= ?", time.Now().AddDate(0, -1, 0).Unix())
	}

	baseQuery.Where("type = ?", LogTypeConsume).Group("model_name").Find(&modelUsage)

	return modelUsage
}

func SumUserModelUsage(userId int, logType int, startTimestamp int64, endTimestamp int64, modelName string, tokenName string) (modelUsage []*ModelUsage) {
	baseQuery := LOG_DB.Table("logs")
	baseQuery = baseQuery.Select("model_name,COUNT(1) cnt")
	// 添加共同条件
	baseQuery = baseQuery.Where("user_id = ?", userId)
	if tokenName != "" {
		baseQuery = baseQuery.Where("token_name = ?", tokenName)
	}
	if modelName != "" {
		baseQuery = baseQuery.Where("model_name = ?", modelName)
	}
	if startTimestamp != 0 {
		baseQuery = baseQuery.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		baseQuery = baseQuery.Where("created_at <= ?", endTimestamp)
	}

	baseQuery.Where("type = ?", LogTypeConsume).Group("model_name").Find(&modelUsage)

	return modelUsage
}

func SumUsedToken(logType int, startTimestamp int64, endTimestamp int64, modelName string, username string, tokenName string) (token int) {
	ifnull := "ifnull"
	if IsLogDBPostgreSQL() {
		ifnull = "COALESCE"
	}
	tx := LOG_DB.Table("logs").Select(fmt.Sprintf("%s(sum(prompt_tokens),0) + %s(sum(completion_tokens),0)", ifnull, ifnull))
	if username != "" {
		tx = tx.Where("username = ?", username)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	tx.Where("type = ?", LogTypeConsume).Scan(&token)
	return token
}

func DeleteOldLog(targetTimestamp int64, logType []int) (int64, error) {
	var totalDeleted int64
	batchSize := 2500 // 每批删除的数据量
	// 构建删除条件
	conditions := []string{"created_at < ?"}
	args := []interface{}{targetTimestamp}
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		conditions = append(conditions, "type IN (?)")
		args = append(args, logType)
	}

	// 统计需要删除的总条数
	var count int64
	if len(conditions) > 0 {
		query := fmt.Sprintf("SELECT COUNT(1) FROM logs WHERE %s", strings.Join(conditions, " AND "))
		err := LOG_DB.Raw(query, args...).Scan(&count).Error
		if err != nil {
			return 0, err
		}
	} else {
		err := LOG_DB.Model(&Log{}).Where("created_at < ?", targetTimestamp).Count(&count).Error
		if err != nil {
			return 0, err
		}
	}
	TotalShouldDeleteHistoryLogsCount = count
	// 分批删除数据
	for {
		var result *gorm.DB
		if IsLogDBPostgreSQL() {
			// PostgreSQL 不支持 DELETE ... LIMIT，使用子查询
			if len(conditions) > 0 {
				query := fmt.Sprintf("DELETE FROM logs WHERE id IN (SELECT id FROM logs WHERE %s ORDER BY id LIMIT %d)", strings.Join(conditions, " AND "), batchSize)
				result = LOG_DB.Exec(query, args...)
			} else {
				result = LOG_DB.Exec("DELETE FROM logs WHERE id IN (SELECT id FROM logs ORDER BY id LIMIT ?)", batchSize)
			}
		} else {
			// MySQL 和 SQLite 支持 DELETE ... LIMIT
			if len(conditions) > 0 {
				query := fmt.Sprintf("DELETE FROM logs WHERE %s LIMIT ?", strings.Join(conditions, " AND "))
				result = LOG_DB.Exec(query, append(args, batchSize)...)
			} else {
				result = LOG_DB.Exec("DELETE FROM logs LIMIT ?", batchSize)
			}
		}

		if result.Error != nil {
			return totalDeleted, result.Error
		}

		rowsAffected := result.RowsAffected
		totalDeleted += rowsAffected
		TotalAffectedHistoryLogsCount = totalDeleted

		if rowsAffected < int64(batchSize) {
			break
		}
	}
	return totalDeleted, nil
}

type LogStatistic struct {
	Day              string `gorm:"column:day"`
	ModelName        string `gorm:"column:model_name"`
	RequestCount     int    `gorm:"column:request_count"`
	Quota            int    `gorm:"column:quota"`
	PromptTokens     int    `gorm:"column:prompt_tokens"`
	CompletionTokens int    `gorm:"column:completion_tokens"`
}

func SearchLogsByDayAndModel(userId, start, end int) (LogStatistics []*LogStatistic, err error) {
	groupSelect := "DATE_FORMAT(FROM_UNIXTIME(created_at), '%Y-%m-%d') as day"

	if IsLogDBPostgreSQL() {
		groupSelect = "TO_CHAR(date_trunc('day', to_timestamp(created_at)), 'YYYY-MM-DD') as day"
	}

	if IsLogDBSQLite() {
		groupSelect = "strftime('%Y-%m-%d', datetime(created_at, 'unixepoch')) as day"
	}

	err = LOG_DB.Raw(`
		SELECT `+groupSelect+`,
		model_name, count(1) as request_count,
		sum(quota) as quota,
		sum(prompt_tokens) as prompt_tokens,
		sum(completion_tokens) as completion_tokens
		FROM logs
		WHERE type=2
		AND user_id= ?
		AND created_at BETWEEN ? AND ?
		GROUP BY day, model_name
		ORDER BY day, model_name
	`, userId, start, end).Scan(&LogStatistics).Error

	return LogStatistics, err
}

func RecordConsumeLogRealtime(ctx context.Context, userId int, channelId int, promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string, quota int, totalDuration int64, isStream bool, content string, other string) *Log {
	username := CacheGetUsernameById(userId)
	logger.Info(ctx, fmt.Sprintf("record consume log: userId=%d, username=%s, channelId=%d, channelName=%s, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d, totalDuration=%d, responseFirstByteDuration=N/A, isStream=%t, ip=N/A, content=%s",
		userId, username, channelId, channelName, promptTokens, completionTokens, modelName, tokenName, quota, totalDuration, isStream, content))

	// 根据日志类型检查对应的开关
	logType := LogTypeConsume
	if (logType == LogTypeConsume && !config.LogConsumeEnabled) ||
		((logType == LogTypeDownstreamError || logType == LogTypeSystemErr) && !config.LogErrorEnabled) {
		return nil
	}

	requestId, _ := ctx.Value(helper.RequestIdKey).(string)
	log := &Log{
		UserId:           userId,
		Username:         CacheGetUsernameById(userId),
		CreatedAt:        helper.GetTimestamp(),
		Type:             logType,
		Content:          content,
		PromptTokens:     promptTokens,
		CompletionTokens: completionTokens,
		TokenName:        tokenName,
		TokenKey:         tokenKey,
		ChannelName:      channelName,
		ModelName:        modelName,
		Quota:            quota,
		ChannelId:        channelId,
		TotalDuration:    totalDuration,
		IsStream:         isStream,
		RequestId:        requestId,
		Other:            other,
	}
	// 新增Redis统计
	if common.RedisEnabled && config.NewRPMEnabled {
		go func() {
			err := StoreRedisStats(
				userId, modelName, channelId, tokenName, tokenKey, "", "",
				quota, promptTokens+completionTokens,
				0, 0, totalDuration,
				other)
			if err != nil {
				logger.SysError("Failed to store Redis stats: " + err.Error())
			}
		}()
	}
	err := LOG_DB.Create(log).Error
	if err != nil {
		logger.Error(ctx, "failed to record log: "+err.Error())
	}
	return log
}

// 辅助函数，从context中获取错误码
func getErrorCodeFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	val := ctx.Value(ctxkey.ErrorCode)
	if val == nil {
		return ""
	}
	if errorCode, ok := val.(string); ok {
		return errorCode
	}
	return ""
}

func getIgnoredErrorCodes() map[string]struct{} {
	currentCodes := config.LogIgnoredErrorCodes
	if cached := ignoredErrorCodesCache.Load(); cached != nil {
		if lastIgnoredErrorCodes == currentCodes {
			return cached.(map[string]struct{})
		}
	}

	codes := strings.Split(currentCodes, ",")
	codesMap := make(map[string]struct{}, len(codes))
	for _, code := range codes {
		codesMap[strings.TrimSpace(code)] = struct{}{}
	}

	ignoredErrorCodesCache.Store(codesMap)
	lastIgnoredErrorCodes = currentCodes
	return codesMap
}
