package model

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/constants"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

// MySQLLogStorage MySQL日志存储实现
type MySQLLogStorage struct {
	db *gorm.DB
}

// NewMySQLLogStorage 创建MySQL日志存储实例
func NewMySQLLogStorage(db *gorm.DB) LogStorage {
	return &MySQLLogStorage{
		db: db,
	}
}

// RecordLog 记录单条日志
func (m *MySQLLogStorage) RecordLog(ctx context.Context, log *Log) error {
	requestId := helper.GetRequestID(ctx)
	if log.RequestId == "" {
		log.RequestId = requestId
	}

	err := m.db.Create(log).Error
	if err != nil {
		logger.Error(ctx, "failed to record log: "+err.Error())
		return err
	}

	logger.Infof(ctx, "record log: %+v", log)
	return nil
}

// RecordLogBatch 批量记录日志
func (m *MySQLLogStorage) RecordLogBatch(ctx context.Context, logs []*Log) error {
	if len(logs) == 0 {
		return nil
	}

	// 设置RequestId
	requestId := helper.GetRequestID(ctx)
	for _, log := range logs {
		if log.RequestId == "" {
			log.RequestId = requestId
		}
	}

	err := m.db.CreateInBatches(logs, config.LogStorageBatchSize).Error
	if err != nil {
		logger.Error(ctx, "failed to record logs batch: "+err.Error())
		return err
	}

	logger.Infof(ctx, "record logs batch: %d logs", len(logs))
	return nil
}

// RecordConsumeLogByDetailIfZeroQuota 记录消费日志（如果配额为零）
func (m *MySQLLogStorage) RecordConsumeLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, xForwardedFor string, xRealIp string, cfConnectingIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, quota int, costQuota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64,
	isStream bool, content string, other string) (*Log, error) {

	if ctx == nil {
		ctx = context.Background()
	}

	// 获取用户信息
	user, _ := GetUserById(userId, false)
	var username string
	if user != nil {
		username = user.Username
	}

	log := &Log{
		UserId:                    userId,
		Username:                  username,
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeConsume,
		Content:                   content,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		TokenGroup:                tokenGroup,
		ModelName:                 modelName,
		Quota:                     quota,
		CostQuota:                 costQuota,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		ChannelId:                 channelId,
		ChannelName:               channelName,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		XForwardedFor:             xForwardedFor,
		XRealIp:                   xRealIp,
		CfConnectingIp:            cfConnectingIp,
		Other:                     other,
	}

	err := m.db.WithContext(ctx).Create(log).Error
	return log, err
}

// GetAllLogs 获取所有日志（管理员视图）
func (m *MySQLLogStorage) GetAllLogs(userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, startIdx int, num int, channel int, isStream string, requestId string,
	ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64,
	requestDurationMin *float64, requestDurationMax *float64, responseFirstByteDurationMin *float64,
	responseFirstByteDurationMax *float64, excludeModels []string, errorCode string,
	excludeErrorCodes []string, quotaMin *int, quotaMax *int) ([]*Log, error) {

	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = m.db.Where("type in ?", logType)
	} else {
		tx = m.db
	}

	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if username != "" {
		tx = tx.Where("username = ?", username)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		// 这个传入的是 sk-xxx，但是数据库储存的是 xxx，首先判断是不是，如果不是，那就不动，如果是，那就截取
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if tokenGroup != "" {
		tx = tx.Where("token_group = ?", tokenGroup)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if channelName != "" {
		tx = tx.Where("channel_name = ?", channelName)
	}
	if channel != 0 {
		tx = tx.Where("channel_id = ?", channel)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}
	if ip != "" {
		// 智能IP搜索：在所有IP相关字段中搜索
		tx = tx.Where("ip = ? OR remote_ip = ? OR x_forwarded_for LIKE ? OR x_real_ip = ? OR cf_connecting_ip = ?",
			ip, ip, "%"+ip+"%", ip, ip)
	}
	if promptTokensMin != nil {
		tx = tx.Where("prompt_tokens >= ?", *promptTokensMin)
	}
	if promptTokensMax != nil {
		tx = tx.Where("prompt_tokens <= ?", *promptTokensMax)
	}
	if completionTokensMin != nil {
		tx = tx.Where("completion_tokens >= ?", *completionTokensMin)
	}
	if completionTokensMax != nil {
		tx = tx.Where("completion_tokens <= ?", *completionTokensMax)
	}
	if totalDurationMin != nil {
		tx = tx.Where("total_duration >= ?", *totalDurationMin)
	}
	if totalDurationMax != nil {
		tx = tx.Where("total_duration <= ?", *totalDurationMax)
	}
	if requestDurationMin != nil {
		tx = tx.Where("request_duration >= ?", *requestDurationMin)
	}
	if requestDurationMax != nil {
		tx = tx.Where("request_duration <= ?", *requestDurationMax)
	}
	if responseFirstByteDurationMin != nil {
		tx = tx.Where("response_first_byte_duration >= ?", *responseFirstByteDurationMin)
	}
	if responseFirstByteDurationMax != nil {
		tx = tx.Where("response_first_byte_duration <= ?", *responseFirstByteDurationMax)
	}
	if errorCode != "" {
		tx = tx.Where("error_code = ?", errorCode)
	}
	if len(excludeErrorCodes) > 0 {
		tx = tx.Where("error_code NOT IN (?)", excludeErrorCodes)
	}
	// 只有admin才显示渠道和成本信息
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr())
		tx = tx.Omit(constants.LogFieldsExclude...)
	}

	// 添加排除模型的逻辑
	if len(excludeModels) > 0 {
		tx = tx.Where("model_name NOT IN (?)", excludeModels)
	}

	// 添加quota范围筛选
	if quotaMin != nil {
		tx = tx.Where("quota >= ?", *quotaMin)
	}
	if quotaMax != nil {
		tx = tx.Where("quota <= ?", *quotaMax)
	}

	var logs []*Log
	err := tx.Order("id desc").Limit(num).Offset(startIdx).Find(&logs).Error
	return logs, err
}

// CountAllLogs 统计所有日志数量
func (m *MySQLLogStorage) CountAllLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (int64, error) {

	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = m.db.Where("type in ?", logType)
	} else {
		tx = m.db
	}

	// 应用与GetAllLogs相同的过滤条件
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if username != "" {
		tx = tx.Where("username = ?", username)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if tokenGroup != "" {
		tx = tx.Where("token_group = ?", tokenGroup)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if channelName != "" {
		tx = tx.Where("channel_name = ?", channelName)
	}
	if channel != 0 {
		tx = tx.Where("channel_id = ?", channel)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}
	if ip != "" {
		// 智能IP搜索：在所有IP相关字段中搜索
		tx = tx.Where("ip = ? OR remote_ip = ? OR x_forwarded_for LIKE ? OR x_real_ip = ? OR cf_connecting_ip = ?",
			ip, ip, "%"+ip+"%", ip, ip)
	}
	if promptTokensMin != nil {
		tx = tx.Where("prompt_tokens >= ?", *promptTokensMin)
	}
	if promptTokensMax != nil {
		tx = tx.Where("prompt_tokens <= ?", *promptTokensMax)
	}
	if completionTokensMin != nil {
		tx = tx.Where("completion_tokens >= ?", *completionTokensMin)
	}
	if completionTokensMax != nil {
		tx = tx.Where("completion_tokens <= ?", *completionTokensMax)
	}
	if totalDurationMin != nil {
		tx = tx.Where("total_duration >= ?", *totalDurationMin)
	}
	if totalDurationMax != nil {
		tx = tx.Where("total_duration <= ?", *totalDurationMax)
	}
	if requestDurationMin != nil {
		tx = tx.Where("request_duration >= ?", *requestDurationMin)
	}
	if requestDurationMax != nil {
		tx = tx.Where("request_duration <= ?", *requestDurationMax)
	}
	if responseFirstByteDurationMin != nil {
		tx = tx.Where("response_first_byte_duration >= ?", *responseFirstByteDurationMin)
	}
	if responseFirstByteDurationMax != nil {
		tx = tx.Where("response_first_byte_duration <= ?", *responseFirstByteDurationMax)
	}
	if len(excludeModels) > 0 {
		tx = tx.Where("model_name NOT IN ?", excludeModels)
	}
	if errorCode != "" {
		tx = tx.Where("error_code = ?", errorCode)
	}
	if len(excludeErrorCodes) > 0 {
		tx = tx.Where("error_code NOT IN ?", excludeErrorCodes)
	}
	if quotaMin != nil {
		tx = tx.Where("quota >= ?", *quotaMin)
	}
	if quotaMax != nil {
		tx = tx.Where("quota <= ?", *quotaMax)
	}

	var count int64
	err := tx.Model(&Log{}).Count(&count).Error
	return count, err
}

// HealthCheck 健康检查
func (m *MySQLLogStorage) HealthCheck() error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// Close 关闭连接
func (m *MySQLLogStorage) Close() error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// RecordLogExtend 记录LogExtend
func (m *MySQLLogStorage) RecordLogExtend(ctx context.Context, logExtend *LogExtend) error {
	return logExtend.Insert()
}

// RecordLogExtendBatch 批量记录LogExtend
func (m *MySQLLogStorage) RecordLogExtendBatch(ctx context.Context, logExtends []*LogExtend) error {
	if len(logExtends) == 0 {
		return nil
	}

	// 使用事务批量插入
	tx := m.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer tx.Rollback()

	for _, logExtend := range logExtends {
		if err := logExtend.InsertByTx(tx); err != nil {
			return err
		}
	}

	return tx.Commit().Error
}

// GetLogExtendByLogId 根据LogId获取LogExtend
func (m *MySQLLogStorage) GetLogExtendByLogId(logId int) (*LogExtend, error) {
	var logExtend LogExtend
	err := m.db.Where("log_id = ?", logId).First(&logExtend).Error
	if err != nil {
		return nil, err
	}
	return &logExtend, nil
}

// DeleteLogExtendByLogId 根据LogId删除LogExtend
func (m *MySQLLogStorage) DeleteLogExtendByLogId(logId int) error {
	return m.db.Where("log_id = ?", logId).Delete(&LogExtend{}).Error
}

// DeleteLogExtendByTimestamp 根据时间戳删除LogExtend
func (m *MySQLLogStorage) DeleteLogExtendByTimestamp(targetTimestamp int64) (int64, error) {
	return DeleteLogExByTimestamp(targetTimestamp)
}

// DeleteInvalidLogExtend 删除无效的LogExtend记录
func (m *MySQLLogStorage) DeleteInvalidLogExtend() error {
	return DeleteInvalidLogExtend()
}

// GetStorageType 获取存储类型
func (m *MySQLLogStorage) GetStorageType() string {
	return "mysql"
}

// GetUserLogs 获取用户日志
func (m *MySQLLogStorage) GetUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, tokenName string, tokenKey string, startIdx int, num int,
	isStream string, requestId string) ([]*Log, error) {

	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = m.db.Where("type in ?", logType)
	} else {
		tx = m.db
	}
	tx = tx.Where("user_id = ?", userId)
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	omitFields := []string{"id"}
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr()) // 添加了 LogTypeDownstreamError (15)
		omitFields = append(omitFields, constants.LogFieldsExclude...)
	}

	var logs []*Log
	err := tx.Order("id desc").Limit(num).Offset(startIdx).Omit(omitFields...).Find(&logs).Error
	return logs, err
}

// CountUserLogs 统计用户日志数量
func (m *MySQLLogStorage) CountUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, tokenName string, tokenKey string, isStream string, requestId string) (int64, error) {

	var tx *gorm.DB
	if logType != nil && len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		tx = m.db.Where("type in ?", logType)
	} else {
		tx = m.db
	}
	tx = tx.Where("user_id = ?", userId)
	if modelName != "" {
		tx = tx.Where("model_name = ?", modelName)
	}
	if tokenName != "" {
		tx = tx.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		tx = tx.Where("token_key = ?", tokenKey)
	}
	if startTimestamp != 0 {
		tx = tx.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		tx = tx.Where("created_at <= ?", endTimestamp)
	}
	if isStream != "" {
		tx = tx.Where("is_stream = ?", isStream == "true")
	}
	if requestId != "" {
		tx = tx.Where("request_id = ?", requestId)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr())
	}

	var count int64
	err := tx.Model(&Log{}).Count(&count).Error
	return count, err
}

// SearchAllLogs 搜索所有日志
func (m *MySQLLogStorage) SearchAllLogs(keyword string) ([]*Log, error) {
	var logs []*Log
	tx := m.db

	// 搜索关键词在内容、模型名称、token名称或错误码中
	if keyword != "" {
		tx = tx.Where("(content LIKE ? OR model_name LIKE ? OR token_name LIKE ? OR error_code LIKE ?)",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	err := tx.Order("id desc").Limit(config.MaxRecentItems).Find(&logs).Error
	return logs, err
}

// SearchUserLogs 搜索用户日志
func (m *MySQLLogStorage) SearchUserLogs(userId int, keyword string) ([]*Log, error) {
	var logs []*Log
	tx := m.db.Where("user_id = ?", userId)

	// 搜索关键词在内容、模型名称、token名称或错误码中
	if keyword != "" {
		tx = tx.Where("(content LIKE ? OR model_name LIKE ? OR token_name LIKE ? OR error_code LIKE ?)",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	omitFields := []string{"id"}
	if !IsAdmin(userId) {
		tx = tx.Where("type not in " + constants.GetExcludeLogTypesStr())
		omitFields = append(omitFields, constants.LogFieldsExclude...)
	}

	err := tx.Order("id desc").Limit(config.MaxRecentItems).Omit(omitFields...).Find(&logs).Error
	return logs, err
}

// SearchUserLogsByKey 根据key搜索用户日志
func (m *MySQLLogStorage) SearchUserLogsByKey(key string, startIdx int, num int) ([]*Log, error) {
	// 处理token key格式
	if key != "" {
		if strings.HasPrefix(key, "sk-") && len(strings.Split(key, "-")[1]) > 1 {
			key = strings.Split(key, "-")[1]
		}
	}

	var logs []*Log
	err := m.db.Where("token_key = ? and type = 2", key).Order("id desc").Limit(config.MaxRecentItems).
		Omit("id", "channel_name", "channel_id", "content", "user_id", "username", "token_name").
		Limit(num).Offset(startIdx).Find(&logs).Error
	return logs, err
}

// CountUserLogsByKey 根据key统计用户日志数量
func (m *MySQLLogStorage) CountUserLogsByKey(key string) (int64, error) {
	// 处理token key格式
	if key != "" {
		if strings.HasPrefix(key, "sk-") && len(strings.Split(key, "-")[1]) > 1 {
			key = strings.Split(key, "-")[1]
		}
	}

	var count int64
	err := m.db.Model(&Log{}).Where("token_key = ?", key).Count(&count).Error
	return count, err
}

// SumUsedQuota 统计使用的配额
func (m *MySQLLogStorage) SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, modelName string,
	username string, tokenName string, tokenKey string, tokenGroup string, channel int, useRedis bool) (Stat, error) {

	var stat Stat

	// 如果启用Redis且配置了新RPM，优先使用Redis统计
	if common.RedisEnabled && config.NewRPMEnabled && useRedis {
		// 这里可以调用原有的Redis统计逻辑
		// 为了简化，这里直接使用MySQL统计
	}

	// 使用MySQL统计
	logConsumedTypes := []int{LogTypeConsume, LogTypeRefund}

	baseQuery := m.db.Table("logs")
	// 添加共同条件
	if username != "" {
		baseQuery = baseQuery.Where("username = ?", username)
	}
	if tokenName != "" {
		baseQuery = baseQuery.Where("token_name = ?", tokenName)
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		baseQuery = baseQuery.Where("token_key = ?", tokenKey)
	}
	if tokenGroup != "" {
		baseQuery = baseQuery.Where("token_group = ?", tokenGroup)
	}
	if modelName != "" {
		baseQuery = baseQuery.Where("model_name = ?", modelName)
	}
	if channel != 0 {
		baseQuery = baseQuery.Where("channel_id = ?", channel)
	}

	// 计算 quota
	quotaQuery := baseQuery.Select("sum(quota) quota")
	if startTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at <= ?", endTimestamp)
	}
	quotaQuery.Where("type in (?)", logConsumedTypes).Scan(&stat)

	// 获取当前时间，并计算一分钟之前的时间
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute).Unix()

	// 计算 rpm 和 tpm
	rpmTpmQuery := baseQuery.Select("count(*) rpm, sum(prompt_tokens) + sum(completion_tokens) tpm, (sum(quota) / 500000) mpm")
	rpmTpmQuery = rpmTpmQuery.Where("type in (?)", logConsumedTypes).Where("created_at >= ?", oneMinuteAgo).Where("created_at <= ?", now.Unix())

	var rpmTpmStat Stat
	rpmTpmQuery.Scan(&rpmTpmStat)

	// 合并结果
	stat.Rpm = rpmTpmStat.Rpm
	stat.Tpm = rpmTpmStat.Tpm
	stat.Mpm = rpmTpmStat.Mpm
	stat.IsRealtimeData = false // 标记为历史数据

	return stat, nil
}

// SumUsedQuotaByKey 根据key统计使用的配额
func (m *MySQLLogStorage) SumUsedQuotaByKey(key string, startTimestamp int64, endTimestamp int64) (Stat, error) {
	// 处理token key格式
	if key != "" {
		if strings.HasPrefix(key, "sk-") && len(strings.Split(key, "-")[1]) > 1 {
			key = strings.Split(key, "-")[1]
		}
	}

	var stat Stat
	logConsumedTypes := []int{LogTypeConsume, LogTypeRefund}

	baseQuery := m.db.Table("logs")
	// 添加共同条件
	baseQuery = baseQuery.Where("token_key = ?", key)

	// 计算 quota
	quotaQuery := baseQuery.Select("sum(quota) quota")
	if startTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at >= ?", startTimestamp)
	}
	if endTimestamp != 0 {
		quotaQuery = quotaQuery.Where("created_at <= ?", endTimestamp)
	}
	quotaQuery.Where("type in (?)", logConsumedTypes).Scan(&stat)

	// 获取当前时间，并计算一分钟之前的时间
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute).Unix()

	// 计算 rpm 和 tpm
	rpmTpmQuery := baseQuery.Select("count(*) rpm, sum(prompt_tokens) + sum(completion_tokens) tpm , (sum(quota) / 500000) mpm")
	rpmTpmQuery = rpmTpmQuery.Where("created_at >= ?", oneMinuteAgo).Where("created_at <= ?", now.Unix())

	var rpmTpmStat Stat
	rpmTpmQuery.Scan(&rpmTpmStat)

	// 合并结果
	stat.Rpm = rpmTpmStat.Rpm
	stat.Tpm = rpmTpmStat.Tpm
	stat.Mpm = rpmTpmStat.Mpm

	return stat, nil
}

// SumAllDailyUsageStatsByDimension 按维度统计每日使用情况
func (m *MySQLLogStorage) SumAllDailyUsageStatsByDimension(userId int, timezone string, tokenName string, username string,
	channel int, channelName string, modelName string, startTimestamp int64, endTimestamp int64,
	dimension string, granularity string) ([]*DailyModelUsageStats, error) {

	// 这里需要实现复杂的统计逻辑，由于代码较长，暂时返回空结果
	// 实际实现中需要根据不同的数据库类型（MySQL/PostgreSQL/SQLite）使用不同的SQL语法
	var results []*DailyModelUsageStats

	query := m.db.Table("logs")
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}
	if tokenName != "" {
		query = query.Where("token_name = ?", tokenName)
	}
	if username != "" {
		query = query.Where("username = ?", username)
	}
	if channel != 0 {
		query = query.Where("channel_id = ?", channel)
	}
	if channelName != "" {
		query = query.Where("channel_name = ?", channelName)
	}
	if modelName != "" {
		query = query.Where("model_name = ?", modelName)
	}

	// 这里需要根据dimension和granularity构建复杂的GROUP BY查询
	// 为了简化，暂时返回空结果
	// 实际实现需要参考原有的SumAllDailyUsageStatsByDimension函数

	return results, nil
}

// RecordRefundLogByDetailIfZeroQuota 记录退款日志（如果配额为零）
func (m *MySQLLogStorage) RecordRefundLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, xForwardedFor string, xRealIp string, cfConnectingIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string,
	quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string) (*Log, error) {

	if ctx == nil {
		ctx = context.Background()
	}

	if requestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, requestId)
	}

	logger.Info(ctx, fmt.Sprintf("record Refund log: userId=%d, channelId=%d, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d,totalDuration=%d, content=%s", userId, channelId, promptTokens, completionTokens, modelName, tokenName, quota, totalDuration, content))

	if !config.LogConsumeEnabled {
		return nil, nil
	}

	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}

	log := &Log{
		UserId:                    userId,
		Username:                  CacheGetUsernameById(userId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeRefund,
		Content:                   content,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		ChannelName:               channelName,
		ModelName:                 modelName,
		Quota:                     quota,
		ChannelId:                 channelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		XForwardedFor:             xForwardedFor,
		XRealIp:                   xRealIp,
		CfConnectingIp:            cfConnectingIp,
	}

	err := m.db.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordRefundLogByDetailIfZeroQuota> failed to record log: "+err.Error())
		return log, err
	}

	return log, nil
}

// RecordSysLogToDBAndFileByGinContext 记录系统日志到数据库和文件（通过Gin Context）
func (m *MySQLLogStorage) RecordSysLogToDBAndFileByGinContext(c interface{}, logType int, content string, prompt string) error {
	// 类型断言获取gin.Context
	ginCtx, ok := c.(*gin.Context)
	if !ok {
		return fmt.Errorf("invalid context type, expected *gin.Context")
	}

	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := ginCtx.GetString(ctxkey.ErrorCode)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return nil
		}

		// 获取用户ID
		userId := ginCtx.GetInt(ctxkey.Id)

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, ginCtx.GetString(ctxkey.RequestModel))
		if prompt != "" {
			prompt = TruncateOptimized(prompt, maxPromptLogLength, ginCtx.GetString(ctxkey.RequestModel))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}

	failedDuration := ginCtx.GetInt64(ctxkey.FailedDuration)
	var requestDuration int64
	var responseFirstByteDuration int64
	var totalDuration int64
	if failedDuration > 0 {
		requestDuration = failedDuration
		responseFirstByteDuration = failedDuration
		totalDuration = failedDuration
	}

	log := &Log{
		RequestId:                 ginCtx.GetString(helper.RequestIdKey),
		UserId:                    ginCtx.GetInt(ctxkey.Id),
		Username:                  CacheGetUsernameById(ginCtx.GetInt(ctxkey.Id)),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		TokenName:                 ginCtx.GetString(ctxkey.TokenName),
		TokenKey:                  ginCtx.GetString(ctxkey.TokenKey),
		TokenGroup:                ginCtx.GetString(ctxkey.TokenGroup),
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		ChannelName:               ginCtx.GetString(ctxkey.ChannelName),
		ModelName:                 ginCtx.GetString(ctxkey.RequestModel),
		ChannelId:                 ginCtx.GetInt(ctxkey.ChannelId),
		Ip:                        ginCtx.ClientIP(),                    // 直接使用Gin的ClientIP
		RemoteIp:                  ginCtx.RemoteIP(),                    // Gin的RemoteIP
		XForwardedFor:             ginCtx.GetHeader("X-Forwarded-For"),  // X-Forwarded-For头
		XRealIp:                   ginCtx.GetHeader("X-Real-IP"),        // X-Real-IP头
		CfConnectingIp:            ginCtx.GetHeader("CF-Connecting-IP"), // CloudFlare IP头
		ErrorCode:                 ginCtx.GetString(ctxkey.ErrorCode),   // 添加错误码
	}

	err := m.db.Create(log).Error
	if err != nil {
		logger.Error(context.TODO(), "<RecordSysLogToDBAndFileByGinContext> failed to record log and not insert logExtend: "+err.Error())
		return err
	}

	if prompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 获取用户ID
	userId := ginCtx.GetInt(ctxkey.Id)

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, maxPromptLogLength, ginCtx.GetString(ctxkey.RequestModel))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(context.TODO(), "<RecordSysLogToDBAndFileByGinContext> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// RecordSysLogToDBAndFile 记录系统日志到数据库和文件
func (m *MySQLLogStorage) RecordSysLogToDBAndFile(ctx context.Context, requestId string, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) error {
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := getErrorCodeFromContext(ctx)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return nil
		}

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, modelName)
		if prompt != "" {
			prompt = TruncateOptimized(prompt, maxPromptLogLength, modelName)
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}

	if requestId == "" {
		requestId1, ok := ctx.Value(helper.RequestIdKey).(string)
		if ok {
			requestId = requestId1
		}
	}

	log := &Log{
		RequestId:   requestId,
		UserId:      userId,
		Username:    CacheGetUsernameById(userId),
		CreatedAt:   helper.GetTimestamp(),
		Type:        logType,
		Content:     content,
		TokenName:   tokenName,
		ChannelName: channelName,
		ModelName:   modelName,
		ChannelId:   channelId,
		ErrorCode:   getErrorCodeFromContext(ctx), // 从context中获取错误码
	}

	err := m.db.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record log and not insert logExtend: "+err.Error())
		return err
	}

	if prompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, config.MaxPromptLogLength, modelName)
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// RecordLogToDBAndFileByMeta 记录日志到数据库和文件（通过Meta）
func (m *MySQLLogStorage) RecordLogToDBAndFileByMeta(ctx context.Context, logType int, toFile bool, meta Meta, content string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64) error {
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := meta.ErrorCode

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		if meta.DetailPrompt != "" {
			meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	if toFile {
		switch logType {
		case LogTypeSystemInfo:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		case LogTypeSystemErr:
			logger.SysError(fmt.Sprintf("系统错误: %s", content))
		default:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		}
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	if ctx == nil {
		ctx = context.Background()
	}

	if meta.RequestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, meta.RequestId)
	}

	log := &Log{
		UserId:                    meta.UserId,
		Username:                  CacheGetUsernameById(meta.UserId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		PromptTokens:              meta.PromptTokens,
		CompletionTokens:          meta.CompletionTokens,
		TokenName:                 meta.TokenName,
		TokenKey:                  meta.TokenKey,
		TokenGroup:                meta.TokenGroup,
		ChannelName:               meta.ChannelName,
		ModelName:                 lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
		Quota:                     quota,
		ChannelId:                 meta.ChannelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  meta.IsStream,
		RequestId:                 meta.RequestId,
		Ip:                        meta.Ip,
		RemoteIp:                  meta.RemoteIp,
		ErrorCode:                 meta.ErrorCode,
	}

	err := m.db.Create(log).Error
	if err != nil {
		logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record log and not insert logExtend: "+err.Error())
		return err
	}

	if meta.DetailPrompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    meta.DetailPrompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = logExtend.Insert()
	if err != nil {
		logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// TruncateLogExtendTable 清空LogExtend表
func (m *MySQLLogStorage) TruncateLogExtendTable(ctx context.Context) error {
	result := m.db.Exec("TRUNCATE TABLE log_extends")
	if result.Error != nil {
		logger.Error(ctx, "<TruncateLogExtendTable> failed to truncate log_extends table: "+result.Error.Error())
		return result.Error
	}

	logger.Info(ctx, "<TruncateLogExtendTable> successfully truncated log_extends table")
	return nil
}
