package model

import (
	"context"
	"errors"
	"math/rand"
	"sort"
	"strconv"
	"strings"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

type Ability struct {
	Group               string `json:"group" bson:"group" gorm:"type:varchar(64);primaryKey;autoIncrement:false"`
	Model               string `json:"model" bson:"model" gorm:"primaryKey;autoIncrement:false"`
	ChannelId           int    `json:"channel_id" bson:"channel_id" gorm:"primaryKey;autoIncrement:false;index"`
	Enabled             *bool  `json:"enabled" bson:"enabled" gorm:"default:1;index"`
	Sort                *int   `json:"sort" bson:"sort" gorm:"default:0;index"` // 新增排序字段,默认为0
	Weight              *uint  `json:"weight" bson:"weight" gorm:"type:smallint;default:0;index"`
	Priority            *int64 `json:"priority" bson:"priority" gorm:"bigint;default:0;index"`
	BillingType         int    `json:"billing_type" bson:"billing_type" gorm:"default:1;index"`             // 计费类型: 1 - 按量计费, 2 - 按次计费
	FunctionCallEnabled *bool  `json:"function_call_enabled" bson:"function_call_enabled" gorm:"default:1"` // 是否有能力使用function call 默认为1支持
	ImageSupported      *bool  `json:"image_supported" bson:"image_supported" gorm:"default:1"`             // 是否支持图片输入 默认为1支持
}

func GetAllAbilities(startIdx int, num int, orderBy string, group string, model string, channelId int, enabled bool) ([]*Ability, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAllAbilities(startIdx, num, orderBy, group, model, channelId, enabled)
	}

	// 回退到原来的SQL实现
	var abilities []*Ability
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	tx := DB
	if group != "" {
		// 按照逗号分隔的字符串进行查询
		groups_ := strings.Split(group, ",")
		// in
		tx = tx.Where(groupCol+" IN ?", groups_)
	}
	if model != "" {
		tx = tx.Where("model = ?", model)
	}
	if channelId != 0 {
		tx = tx.Where("channel_id = ?", channelId)
	}
	if enabled {
		tx = tx.Where("enabled = ?", enabled)
	}
	if orderBy != "" {
		tx = tx.Order(orderBy)
	} else {
		// 确保在 ORDER BY 子句中也正确地使用字段引用
		tx = tx.Order(groupCol + ", model, channel_id")
	}
	err := tx.Limit(num).Offset(startIdx).Find(&abilities).Error
	return abilities, err
}

func CountAbilities(group string, model string, channelId int, enabled bool) (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.CountAbilities(group, model, channelId, enabled)
	}

	// 回退到原来的SQL实现
	var count int64
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	tx := DB.Model(&Ability{})
	if group != "" {
		// 按照逗号分隔的字符串进行查询
		groups_ := strings.Split(group, ",")
		// in
		tx = tx.Where(groupCol+" IN ?", groups_)
	}
	if model != "" {
		tx = tx.Where("model = ?", model)
	}
	if channelId != 0 {
		tx = tx.Where("channel_id = ?", channelId)
	}
	if enabled {
		tx = tx.Where("enabled = ?", enabled)
	}
	err := tx.Count(&count).Error
	return count, err
}

func (ability *Ability) Insert() error {
	return ability.InsertWithSync(false)
}

func (ability *Ability) InsertWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.InsertAbility(ability)
		if nosqlErr != nil {
			logger.SysError("failed to insert ability in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Create(ability).Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func (ability *Ability) Update() error {
	return ability.UpdateWithSync(false)
}

func (ability *Ability) UpdateWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.UpdateAbility(ability)
		if nosqlErr != nil {
			logger.SysError("failed to update ability in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Save(ability).Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func DeleteAbilitiesByIds(ids []int) (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteAbilitiesByIds(ids)
	}

	// 回退到原来的SQL实现
	result := DB.Where("id IN ?", ids).Delete(&Ability{})
	return result.RowsAffected, result.Error
}

func DeleteAbilitiesByIdsWithSync(ids []int, syncBothDB bool) (int64, error) {
	var nosqlErr error
	var nosqlRows int64

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlRows, nosqlErr = db.DeleteAbilitiesByIds(ids)
		if nosqlErr != nil {
			logger.SysError("failed to delete abilities by ids in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlRows, nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	result := DB.Where("id IN ?", ids).Delete(&Ability{})
	rows := result.RowsAffected
	err := result.Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return rows, nosqlErr
	}

	return rows, err
}

func GetAbilityByGroupModelChannel(group string, model string, channelId int) (*Ability, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAbilityByGroupModelChannel(group, model, channelId)
	}

	// 回退到原来的SQL实现
	var ability Ability
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	err := DB.Where(groupCol+" = ? AND model = ? AND channel_id = ?", group, model, channelId).First(&ability).Error
	if err != nil {
		return nil, err
	}
	return &ability, nil
}

func UpdateAbilityEnabledStatus(group string, model string, channelId int, enabled bool) error {
	return UpdateAbilityEnabledStatusWithSync(group, model, channelId, enabled, false)
}

func UpdateAbilityEnabledStatusWithSync(group string, model string, channelId int, enabled bool, syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.UpdateAbilityEnabledStatus(group, model, channelId, enabled)
		if nosqlErr != nil {
			logger.SysError("failed to update ability enabled status in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	err := DB.Model(&Ability{}).Where(groupCol+" = ? AND model = ? AND channel_id = ?", group, model, channelId).Update("enabled", enabled).Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func GetAbilitiesByChannelId(channelId int) ([]*Ability, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAbilitiesByChannelId(channelId)
	}

	// 回退到原来的SQL实现
	var abilities []*Ability
	err := DB.Where("channel_id = ?", channelId).Find(&abilities).Error
	return abilities, err
}

func (ability *Ability) Delete() error {
	return ability.DeleteWithSync(false)
}

func (ability *Ability) DeleteWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.DeleteAbility(ability)
		if nosqlErr != nil {
			logger.SysError("failed to delete ability in NoSQL: " + nosqlErr.Error())
		}
		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作
	err := DB.Delete(ability).Error

	// 如果同步模式下，优先返回SQL的错误，如果SQL成功但NoSQL失败，返回NoSQL错误
	if syncBothDB && err == nil && nosqlErr != nil {
		return nosqlErr
	}

	return err
}

func GetRandomSatisfiedChannel(group string, model string, tokenBillingType int, inputHasFunctionCall bool, inputHasImage bool, excludeIds []int, ignoreFirstPriority bool, isV1MessagesPath bool) (*Channel, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetRandomSatisfiedChannel(group, model, tokenBillingType, inputHasFunctionCall, inputHasImage, excludeIds, ignoreFirstPriority, isV1MessagesPath)
	}

	// 回退到原来的SQL实现
	ability := Ability{}
	groupCol := "`group`"
	trueVal := "1"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
		trueVal = "true"
	}
	excludeMainCondition := ""
	subQueryWhere := groupCol + " = ? and ? like REPLACE(model, '*', '%') and billing_type = ? and enabled = " + trueVal
	if tokenBillingType == common.BillingTypeMixed {
		// 混合模式不过滤
		subQueryWhere = groupCol + " = ? and ? like REPLACE(model, '*', '%')  and enabled = " + trueVal
	}
	if inputHasFunctionCall {
		subQueryWhere += " and function_call_enabled = " + trueVal
	}
	if inputHasImage {
		subQueryWhere += " and image_supported = " + trueVal
	}
	if excludeIds != nil && len(excludeIds) > 0 {
		excludeIdsStr := make([]string, len(excludeIds))
		for i, id := range excludeIds {
			excludeIdsStr[i] = strconv.Itoa(id)
		}
		subQueryWhere += " and channel_id not in (" + strings.Join(excludeIdsStr, ",") + ")"
		excludeMainCondition = " and channel_id not in (" + strings.Join(excludeIdsStr, ",") + ")"
	}

	var err error = nil
	var maxSortSubQuery *gorm.DB
	var channelQuery *gorm.DB
	if tokenBillingType == common.BillingTypeMixed {
		// 混合模式不过滤
		maxSortSubQuery = DB.Model(&Ability{}).Select("MAX(sort)").Where(subQueryWhere, group, model)
		channelQuery = DB.Where(groupCol+" = ? and ? like REPLACE(model, '*', '%') and enabled = "+trueVal+" and sort = (?) "+excludeMainCondition, group, model, maxSortSubQuery)
	} else {
		maxSortSubQuery = DB.Model(&Ability{}).Select("MAX(sort)").Where(subQueryWhere, group, model, tokenBillingType)
		channelQuery = DB.Where(groupCol+" = ? and ? like REPLACE(model, '*', '%') and billing_type = ? and enabled = "+trueVal+" and sort = (?) "+excludeMainCondition, group, model, tokenBillingType, maxSortSubQuery)
	}
	if inputHasFunctionCall {
		channelQuery = channelQuery.Where("function_call_enabled = " + trueVal)
	}
	if inputHasImage {
		channelQuery = channelQuery.Where("image_supported = " + trueVal)
	}

	// 添加对 /v1/messages 路径的支持检查
	if isV1MessagesPath {
		// 使用子查询获取支持 v1/messages 的渠道 ID
		subQuery := DB.Model(&ChannelExtend{}).
			Select("channel_id").
			Where("transparent_proxy_enabled = ?", true)
		channelQuery = channelQuery.Where("channel_id IN (?)", subQuery)
	}

	// 获取所有符合条件的能力
	var abilities []*Ability
	err = channelQuery.Order("weight DESC").Find(&abilities).Error
	if err != nil {
		return nil, err
	}
	if len(abilities) == 0 {
		return nil, errors.New("no available channel")
	}

	// 如果启用了全局忽略优先级，直接随机选择一个渠道能力
	if config.GlobalIgnorePriorityEnabled {
		// 当启用全局忽略优先级时，自动也忽略权重计算
		randomAbility := abilities[rand.Intn(len(abilities))]
		ability = *randomAbility
		channel := Channel{}
		channel.Id = ability.ChannelId
		err = DB.First(&channel, "id = ?", ability.ChannelId).Error
		return &channel, err
	}

	// 如果启用了全局忽略权重计算，直接随机选择一个渠道能力
	if config.GlobalIgnoreWeightCalculationEnabled {
		randomAbility := abilities[rand.Intn(len(abilities))]
		ability = *randomAbility
		channel := Channel{}
		channel.Id = ability.ChannelId
		err = DB.First(&channel, "id = ?", ability.ChannelId).Error
		return &channel, err
	}

	// 平滑系数，避免权重为0的渠道完全没有机会被选中
	smoothingFactor := 10

	// 计算总权重
	totalWeight := 0
	for _, ab := range abilities {
		totalWeight += int(ab.GetWeight()) + smoothingFactor
	}

	// 生成随机值
	randomWeight := rand.Intn(totalWeight)

	// 根据权重选择渠道
	for _, ab := range abilities {
		randomWeight -= int(ab.GetWeight()) + smoothingFactor
		if randomWeight <= 0 {
			ability = *ab
			break
		}
	}

	// 兜底：如果没有选中任何渠道，随机选择一个
	if ability.ChannelId == 0 && len(abilities) > 0 {
		ability = *abilities[rand.Intn(len(abilities))]
	}

	channel := Channel{}
	channel.Id = ability.ChannelId
	err = DB.First(&channel, "id = ?", ability.ChannelId).Error
	return &channel, err
}

func (channel *Channel) AddAbilities() error {
	models_ := strings.Split(channel.Models, ",")
	groups_ := strings.Split(channel.Group, ",")
	abilities := make([]Ability, 0, len(models_))
	for _, model := range models_ {
		for _, group := range groups_ {
			ability := Ability{
				Group:               group,
				Model:               model,
				ChannelId:           channel.Id,
				Enabled:             &[]bool{channel.Status == ChannelStatusEnabled}[0],
				Sort:                channel.Sort,
				Weight:              channel.Weight,
				Priority:            channel.Priority,
				BillingType:         channel.BillingType,
				FunctionCallEnabled: channel.FunctionCallEnabled,
				ImageSupported:      channel.ImageSupported,
			}
			abilities = append(abilities, ability)
		}
	}
	return DB.Create(&abilities).Error
}

func (channel *Channel) AddAbilitiesByTx(tx *gorm.DB) error {
	models_ := strings.Split(channel.Models, ",")
	groups_ := strings.Split(channel.Group, ",")
	abilities := make([]Ability, 0, len(models_))
	for _, model := range models_ {
		for _, group := range groups_ {
			ability := Ability{
				Group:               group,
				Model:               model,
				ChannelId:           channel.Id,
				Enabled:             &[]bool{channel.Status == common.ChannelStatusEnabled}[0],
				Sort:                channel.Sort,
				Weight:              channel.Weight,
				Priority:            channel.Priority,
				BillingType:         channel.BillingType,
				FunctionCallEnabled: channel.FunctionCallEnabled,
				ImageSupported:      channel.ImageSupported,
			}
			abilities = append(abilities, ability)
		}
	}
	return tx.Create(&abilities).Error
}

func (channel *Channel) DeleteAbilities() error {
	return DB.Where("channel_id = ?", channel.Id).Delete(&Ability{}).Error
}

// UpdateAbilities updates abilities of this channel.
// Make sure the channel is completed before calling this function.
func (channel *Channel) UpdateAbilities() error {
	// A quick and dirty way to update abilities
	// First delete all abilities of this channel
	err := channel.DeleteAbilities()
	if err != nil {
		return err
	}
	// Then add new abilities
	err = channel.AddAbilities()
	if err != nil {
		return err
	}
	return nil
}

func UpdateAbilityStatus(channelId int, status bool) error {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.UpdateAbilityStatus(channelId, status)
	}

	// 回退到原来的SQL实现
	return DB.Model(&Ability{}).Where("channel_id = ?", channelId).Select("enabled").Update("enabled", status).Error
}

func GetGroupModels(ctx context.Context, group string) ([]string, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetGroupModels(ctx, group)
	}

	// 回退到原来的SQL实现
	groupCol := "`group`"
	trueVal := "1"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
		trueVal = "true"
	}
	var models []string
	err := DB.Model(&Ability{}).Distinct("model").Where(groupCol+" = ? and enabled = "+trueVal, group).Pluck("model", &models).Error
	if err != nil {
		return nil, err
	}
	sort.Strings(models)
	return models, err
}

func GetAvailableModelByGroup(group string) []string {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAvailableModelByGroup(group)
	}

	// 回退到原来的SQL实现
	abilities := make([]Ability, 0)
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	// select distinct model from abilities where enabled = true and "group" = 'default'
	DB.Where("enabled = ? and "+groupCol+" = ?", true, group).Distinct("model").Find(&abilities)
	models := make([]string, len(abilities))
	for i, ability := range abilities {
		models[i] = ability.Model
	}
	return models
}

// 查询是否有search-serper这个模型
func HasSearchSerperModel() bool {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.HasSearchSerperModel()
	}

	// 回退到原来的SQL实现
	var count int64
	DB.Model(&Ability{}).Where("enabled = ? and model = ?", true, "search-serper").Count(&count)
	return count > 0
}

func (ability *Ability) GetWeight() uint {
	if ability.Weight == nil {
		return 0
	}
	return *ability.Weight
}
func (ability *Ability) GetWeightForCalculation() uint {
	// 真正计算权重时,权重为0或者空的默认为1
	if ability.Weight == nil {
		return 1
	}
	if *ability.Weight == 0 {
		return 1
	}
	return *ability.Weight
}

func DeleteAbilitiesNotExistsInChannel() (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteAbilitiesNotExistsInChannel()
	}

	// 回退到原来的SQL实现
	var rowsAffected int64
	err := DB.Transaction(func(tx *gorm.DB) error {
		result := DB.Where("not exists (SELECT 1 from channels where id = channel_id)").Delete(&Ability{})
		rowsAffected = result.RowsAffected
		if result.Error != nil {
			return result.Error
		}
		// 修改ability表中的Enabled!=true的字段为false
		result = DB.Model(&Ability{}).Where("enabled != ?", true).Update("enabled", false)
		if result.Error != nil {
			return result.Error
		}
		rowsAffected += result.RowsAffected
		return nil
	})
	return rowsAffected, err
}

// ability, err := model.GetAbilityByChannelIdAndModel(channelId, ctx.GetString("request_model"))
func GetFirstAbilityByChannelIdAndModel(channelId int, model string) (*Ability, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetFirstAbilityByChannelIdAndModel(channelId, model)
	}

	// 回退到原来的SQL实现
	ability := Ability{}
	err := DB.First(&ability, "channel_id = ? and model = ?", channelId, model).Error
	return &ability, err
}

// GetAllDistinctModels 获取所有不同的模型名称(包括已启用和未启用的)
func GetAllDistinctModels() ([]string, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAllDistinctModels()
	}

	// 回退到原来的SQL实现
	var models []string
	err := DB.Model(&Ability{}).Distinct("model").Pluck("model", &models).Error
	if err != nil {
		return nil, err
	}
	sort.Strings(models)
	return models, nil
}

// GetAllEnabledDistinctModels 获取所有已启用的不同模型名称
func GetAllEnabledDistinctModels() ([]string, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAllEnabledDistinctModels()
	}

	// 回退到原来的SQL实现
	var models []string
	err := DB.Model(&Ability{}).
		Where("enabled = ?", true).
		Distinct("model").
		Pluck("model", &models).Error
	if err != nil {
		return nil, err
	}
	sort.Strings(models)
	return models, nil
}
