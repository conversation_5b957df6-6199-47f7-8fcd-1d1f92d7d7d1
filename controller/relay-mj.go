package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/monitor"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/meta"
	relayModel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"

	"github.com/gin-gonic/gin"
)

type Midjourney struct {
	MjId        string `json:"id"`
	Action      string `json:"action"`
	Prompt      string `json:"prompt"`
	PromptEn    string `json:"promptEn"`
	Description string `json:"description"`
	State       string `json:"state"`
	SubmitTime  int64  `json:"submitTime"`
	StartTime   int64  `json:"startTime"`
	FinishTime  int64  `json:"finishTime"`
	ImageUrl    string `json:"imageUrl"`
	Status      string `json:"status"`
	Progress    string `json:"progress"`
	FailReason  string `json:"failReason"`
}

func RelayMidjourney(c *gin.Context) {
	// 记录初次请求时间
	_firstStartTime := helper.GetTimestamp()
	bodyCopy, _ := common.PreserveRequestBody(c)
	userId := c.GetInt(ctxkey.Id)
	channelId := c.GetInt(ctxkey.ChannelId)
	channelName := c.GetString(ctxkey.ChannelName)
	retryInterval := c.GetInt(ctxkey.RetryInterval)
	undeadModeEnabled := c.GetBool(ctxkey.UndeadModeEnabled)
	tokenBillingType := c.GetInt(ctxkey.TokenBillingType)
	inputHasFunctionCall := c.GetBool(ctxkey.InputHasFunctionCall)
	inputHasImage := c.GetBool(ctxkey.ImageSupported)
	group := c.GetString(ctxkey.Group)
	// 处理token group替换逻辑
	if config.TokenGroupChangeEnabled {
		tokenGroup := c.GetString(ctxkey.TokenGroup)
		if tokenGroup != "" {
			group = tokenGroup
		}
	}
	requestModel := c.GetString(ctxkey.RequestModel)
	if retryInterval == 0 {
		retryInterval = 300
	}
	err, _ := relayMjByDifferentMode(c)
	if err != nil {
		logger.SysError(fmt.Sprintf("relay mj error %v", err))
	}
	// 判断最大Prompt长度
	bodyForLog := string(bodyCopy)
	bodyForLog = model.TruncateOptimized(bodyForLog, config.MaxPromptLogLength, requestModel)
	if err != nil {
		if config.RootUserRelayErrorNotificationEnabled {
			// 通知管理员
			subject := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）请求失败提醒", userId, channelName, channelId)
			content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）请求失败，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s 导致报错的请求体为: %s", userId, channelName, channelId, err.Message, err.StatusCode, err.Error.Type, err.Error.Code, bodyForLog)
			message.NotifyRootUser(subject, content)
		}
		// 判断是否是特殊错误,如果是特殊错误则直接返回给用户,无需继续重试了
		if monitor.IsSpecialError(err, _firstStartTime) {
			if err.Error.LocalizedMessage == "" {
				// 判断err.Code这个字段是否是string类型 err.Code.(string)
				if str, ok := err.Error.Code.(string); ok {
					// 是字符串类型
					err.Error.LocalizedMessage = common.GetErrorMessage(str, "zh")
				} else {
					err.Error.LocalizedMessage = common.GetErrorMessage(err.Error.Type, "zh")
				}
			}
			if config.LogDownstreamErrorEnabled {
				model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, "downstream_error", fmt.Sprintf("抛出到下游错误%v;请求入参==>%s", err, bodyForLog))
			}
			c.JSON(err.StatusCode, gin.H{
				"error": err.Error,
			})
			return
		}

		logger.Error(c.Request.Context(), fmt.Sprintf("relay error (channel #%d),err.StatusCode (%d),err.Code(%s),err.Type(%s),err.Error.Code (%s),err.Error.Type (%s),err.Error.Message (%s): %s",
			channelId, err.StatusCode, err.Code, err.Type, err.Error.Code, err.Error.Type, err.Error.Message, err.Message))
		// https://platform.openai.com/docs/guides/error-codes/api-errors
		disableChannelErr := HandleDisableChannelReturnError(c, err, channelId, channelName, retryInterval, undeadModeEnabled, requestModel, bodyForLog)
		if disableChannelErr != nil {
			logger.Error(c.Request.Context(), fmt.Sprintf("disable channel error (channel #%d): %s", channelId, disableChannelErr.Error()))
		}
		if _, ok := c.Get("specific_channel_id"); ok {
			handleMaxRetryTimes(c, err, string(bodyCopy))
			return
		}
		// 重试逻辑挪到最下面,为了避免之前渠道未能关闭,导致重试时还是会报错
		// 不采用重定向方式重试
		retryTimes := config.RetryTimes
		if retryTimes > 0 {
			// 构造排除的id
			excludeIds := make([]int, 0)
			if config.RetryWithoutFailedChannelEnabled {
				excludeIds = append(excludeIds, channelId)
			}

			// 如果开启了保持计费类型一致性，则获取第一次使用的渠道计费类型
			retryTokenBillingType := tokenBillingType
			// 使用便捷函数判断是否应该保持重试计费类型一致性
			shouldKeepBillingType := model.ShouldKeepRetryBillingTypeFromContext(c)

			if shouldKeepBillingType {
				// 直接从context中获取第一次请求使用的渠道计费类型，避免数据库查询
				if firstChannelBillingType := c.GetInt(ctxkey.BillingType); firstChannelBillingType != 0 {
					retryTokenBillingType = firstChannelBillingType
				}
			}

			for i := 0; i < retryTimes; i++ {
				var nextRetryChannel *model.Channel
				var err2 error

				if retryTokenBillingType == common.BillingTypeByQuotaFirst {
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					if err2 != nil {
						nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					}
				} else if retryTokenBillingType == common.BillingTypeByCountFirst {
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					if err2 != nil {
						nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					}
				} else {
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, retryTokenBillingType, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
				}
				if nextRetryChannel == nil {
					handleMaxRetryTimes(c, err, string(bodyCopy))
					return
				}
				if err2 != nil {
					logger.SysError(fmt.Sprintf("failed to get next retry channel: %s", err2.Error()))
					if err != nil {
						if config.LogDownstreamErrorEnabled {
							model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, fmt.Sprintf("抛出到下游错误%v;请求入参==>%s", err, bodyForLog), bodyForLog)
						}
						c.JSON(err.StatusCode, gin.H{
							"error": err.Error,
						})
					} else {
						if config.LogDownstreamErrorEnabled {
							model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, fmt.Sprintf("抛出到下游错误%v;请求入参==>%s", err, bodyForLog), bodyForLog)
						}
						c.JSON(http.StatusInternalServerError, gin.H{
							"error": err2.Error(),
						})
					}
					return
				}
				excludeIds = append(excludeIds, nextRetryChannel.Id)
				c.Set(ctxkey.Channel, nextRetryChannel.Type)
				c.Set(ctxkey.ChannelId, nextRetryChannel.Id)
				c.Set(ctxkey.ChannelName, nextRetryChannel.Name)
				c.Set(ctxkey.BillingType, nextRetryChannel.GetModelMapping())
				c.Set(ctxkey.FunctionCallEnabled, nextRetryChannel.GetFunctionCallEnabled())
				c.Set(ctxkey.ImageSupported, nextRetryChannel.GetImageSupported())
				c.Set(ctxkey.ModelMapping, nextRetryChannel.GetModelMapping())
				c.Set(ctxkey.ModelMappingArr, nextRetryChannel.GetModelMappingArr())
				c.Set(ctxkey.OriginalModel, requestModel) // for retry
				c.Set(ctxkey.ExcludedFields, nextRetryChannel.GetExcludedFields())
				c.Set(ctxkey.ExcludedResponseFields, nextRetryChannel.GetExcludedResponseFields())
				c.Set(ctxkey.ExtraFields, nextRetryChannel.GetExtraFields())
				c.Set(ctxkey.BaseURL, nextRetryChannel.GetBaseURL())
				c.Set("retryInterval", nextRetryChannel.GetRetryInterval())
				c.Set("undeadModeEnabled", nextRetryChannel.GetUndeadModeEnabled())
				cfg, _ := nextRetryChannel.LoadConfig()

				if nextRetryChannel.Key != "" {
					c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", nextRetryChannel.Key))
				} else {
					c.Request.Header.Del("Authorization")
				}
				if nextRetryChannel.OpenAIOrganization != nil {
					c.Request.Header.Set("OpenAI-Organization", *nextRetryChannel.OpenAIOrganization)
				}
				// this is for backward compatibility
				switch nextRetryChannel.Type {
				case channeltype.Azure:
					if cfg.APIVersion == "" {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.Xunfei:
					if cfg.APIVersion == "" {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.Gemini:
					if cfg.APIVersion == "" {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.AIProxyLibrary:
					if cfg.LibraryID == "" {
						cfg.LibraryID = *nextRetryChannel.Other
					}
				case channeltype.Ali:
					if cfg.Plugin == "" {
						cfg.Plugin = *nextRetryChannel.Other
					}
				}
				c.Set(ctxkey.Config, cfg)
				// 根据channelId获取channelExtend
				nextRetryChannelExtend, _ := model.CacheGetChannelExByChannelId(nextRetryChannel.Id)
				if nextRetryChannelExtend != nil {
					c.Set("filter_stream_ad", nextRetryChannelExtend.FilterStreamAd)
					c.Set("filter_stream_ad_min_size", nextRetryChannelExtend.FilterStreamAdMinSize)
					c.Set("filter_non_stream_ad", nextRetryChannelExtend.FilterNonStreamAd)
					c.Set("filter_non_stream_ad_regex", nextRetryChannelExtend.FilterNonStreamAdRegex)
					c.Set("filter_system_prompt", nextRetryChannelExtend.FilterSystemPrompt)
					c.Set("custom_system_prompt", nextRetryChannelExtend.CustomSystemPrompt)
					c.Set("extra_headers", nextRetryChannelExtend.GetExtraHeaders())
					c.Set("platform_access_token", nextRetryChannelExtend.PlatformAccessToken)
					c.Set("parse_url_to_content", nextRetryChannelExtend.ParseUrlToContent)
					c.Set("parse_url_prefix_enabled", nextRetryChannelExtend.ParseUrlPrefixEnabled)
					c.Set("parse_url_prefix", nextRetryChannelExtend.ParseUrlPrefix)
					c.Set("custom_full_url_enabled", nextRetryChannelExtend.CustomFullUrlEnabled)
					c.Set("arrange_messages", nextRetryChannelExtend.ArrangeMessages)
					c.Set("original_model_pricing", nextRetryChannelExtend.OriginalModelPricing)
					c.Set("negative_optimization_enabled", nextRetryChannelExtend.NegativeOptimizationEnabled)
					c.Set("negative_optimization_time", nextRetryChannelExtend.NegativeOptimizationTime)
					c.Set("negative_random_offset", nextRetryChannelExtend.NegativeRandomOffset)
					c.Set("original_model_fake_resp_enabled", nextRetryChannelExtend.OriginalModelFakeRespEnabled)
					c.Set("fake_completion_id_enabled", nextRetryChannelExtend.FakeCompletionIdEnabled)
					c.Set("exclude_custom_prompt_cost_enabled", nextRetryChannelExtend.ExcludeCustomPromptCostEnabled)
					c.Set("force_chat_url_enabled", nextRetryChannelExtend.ForceChatUrlEnabled)
					c.Set("ignore_fc_tc_enabled", nextRetryChannelExtend.IgnoreFcTcEnabled)
					c.Set("channel_timeout_breaker_time", nextRetryChannelExtend.ChannelTimeoutBreakerTime)
					c.Set("usage_recalculation_enabled", nextRetryChannelExtend.UsageRecalculationEnabled)
					c.Set("empty_response_error_enabled", nextRetryChannelExtend.EmptyResponseErrorEnabled)
					c.Set("remove_image_download_error_enabled", nextRetryChannelExtend.RemoveImageDownloadErrorEnabled)
					c.Set(ctxkey.Base64ImagePrefixMapping, nextRetryChannelExtend.GetBase64ImagePrefixMapping())
					c.Set("request_token_limit_enabled", nextRetryChannelExtend.RequestTokenLimitEnabled)
					c.Set("min_request_token_count", nextRetryChannelExtend.MinRequestTokenCount)
					c.Set("max_request_token_count", nextRetryChannelExtend.MaxRequestTokenCount)
					c.Set("claude_stream_enabled", nextRetryChannelExtend.ClaudeStreamEnabled)
					c.Set("keyword_error_enabled", nextRetryChannelExtend.KeywordErrorEnabled)
					c.Set("keyword_error", nextRetryChannelExtend.KeywordError)
				}
				// 在重新请求之前，重新设置请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyCopy))
				logContent := fmt.Sprintf("渠道id[%d]名称[%s]失败后第[%d]次重试，失败原因是:[%v],当前实际请求渠道[%d]: %s", channelId, channelName, i+1, err, nextRetryChannel.Id, nextRetryChannel.Name)
				model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeSystemInfo, logContent, string(bodyCopy))
				err, _ = relayMjByDifferentMode(c)
				if err != nil {
					// 判断是否是特殊错误,如果是特殊错误则直接返回给用户,无需继续重试了
					if monitor.IsSpecialError(err, _firstStartTime) {
						if err.Error.LocalizedMessage == "" {
							// 判断err.Code这个字段是否是string类型 err.Code.(string)
							if str, ok := err.Error.Code.(string); ok {
								// 是字符串类型
								err.Error.LocalizedMessage = common.GetErrorMessage(str, "zh")
							} else {
								err.Error.LocalizedMessage = common.GetErrorMessage(err.Error.Type, "zh")
							}
						}
						if config.LogDownstreamErrorEnabled {
							model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, fmt.Sprintf("抛出到下游错误%v;请求入参==>%s", err, bodyForLog), bodyForLog)
						}
						c.JSON(err.StatusCode, gin.H{
							"error": err.Error,
						})
						return
					}
					disableChannelErr := HandleDisableChannelReturnError(c, err, channelId, channelName, retryInterval, undeadModeEnabled, requestModel, bodyForLog)
					if disableChannelErr != nil {
						logger.Error(c.Request.Context(), fmt.Sprintf("disable channel error (channel #%d): %s", channelId, disableChannelErr.Error()))
					}
				}
			}
			handleMaxRetryTimes(c, err, string(bodyCopy))
		} else {
			handleMaxRetryTimes(c, err, string(bodyCopy))
		}
	}
}
func relayMjByDifferentMode(c *gin.Context) (*relayModel.ErrorWithStatusCode, bool) {
	requestId := c.GetString(helper.RequestIdKey)
	relayMode := constant.RelayModeUnknown
	if strings.HasPrefix(c.Request.URL.Path, "/mj/submit/imagine") {
		relayMode = constant.RelayModeMidjourneyImagine
	} else if strings.HasPrefix(c.Request.URL.Path, "/mj/notify") {
		relayMode = constant.RelayModeMidjourneyNotify
	} else if strings.HasPrefix(c.Request.URL.Path, "/mj/submit/change") {
		relayMode = constant.RelayModeMidjourneyChange
	} else if strings.HasPrefix(c.Request.URL.Path, "/mj/submit/describe") {
		relayMode = constant.RelayModeMidjourneyDescribe
	} else if strings.HasPrefix(c.Request.URL.Path, "/mj/task") {
		relayMode = constant.RelayModeMidjourneyTaskFetch
	}
	var err *openai.MidjourneyResponse
	switch relayMode {
	case constant.RelayModeMidjourneyNotify:
		err = relayMidjourneyNotify(c)
	case constant.RelayModeMidjourneyTaskFetch:
		err = relayMidjourneyTask(c, relayMode)
		// 查询接口报错不重试
		return nil, false
	case constant.RelayModeMidjourneyDescribe:
		err = relayMidjourneyDescribe(c, relayMode)
	default:
		err = relayMidjourneySubmit(c, relayMode)
	}
	if err != nil {
		wrappedError := &relayModel.ErrorWithStatusCode{
			StatusCode: http.StatusInternalServerError,
			Error: relayModel.Error{
				Message: err.Description,
				Type:    err.Description,
				Param:   requestId,
				Code:    err.Code,
			},
		}
		return wrappedError, false
	}
	return nil, false
}

func RelayMidjourneyImage(c *gin.Context) {
	taskId := c.Param("id")
	//userId := c.GetInt("id")
	midjourneyTask := model.GetByOnlyMJId(taskId)
	if midjourneyTask == nil {
		if config.LogDownstreamErrorEnabled {
			model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, "midjourney_task_not_found", "尚未解析")
		}
		c.JSON(400, gin.H{
			"error": "midjourney_task_not_found",
		})
		return
	}

	resp, err := client.UserContentRequestHTTPClient.Get(midjourneyTask.ImageUrl)
	if err != nil {
		if config.LogDownstreamErrorEnabled {
			model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, "http_get_image_failed", "尚未解析")
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "http_get_image_failed",
		})
		return
	}
	defer resp.Body.Close()

	c.Header("Content-Type", "image/jpeg")
	// 如果图片服务提供了内容长度，您也可以设置这个头部。
	// c.Header("Content-Length", resp.Header.Get("Content-Length"))

	// 直接将响应体复制到客户端，而不是先全部读取到内存中。
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		// 处理复制过程中可能出现的错误
		logger.SysError("copy image to client failed: " + err.Error())
	}
}

func relayMidjourneyNotify(c *gin.Context) *openai.MidjourneyResponse {
	var midjRequest Midjourney
	err := common.UnmarshalBodyReusable(c, &midjRequest)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "bind_request_body_failed",
			Properties:  nil,
			Result:      "",
		}
	}
	//userId := c.GetInt("id")
	midjourneyTask := model.GetByOnlyMJId(midjRequest.MjId)
	if midjourneyTask == nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "midjourney_task_not_found",
			Properties:  nil,
			Result:      "",
		}
	}
	midjourneyTask.Progress = midjRequest.Progress
	if midjRequest.Action == "DESCRIBE" {
		midjourneyTask.Prompt = midjRequest.Prompt
	}
	midjourneyTask.PromptEn = midjRequest.PromptEn
	midjourneyTask.State = midjRequest.State
	midjourneyTask.SubmitTime = midjRequest.SubmitTime
	midjourneyTask.StartTime = midjRequest.StartTime
	midjourneyTask.FinishTime = midjRequest.FinishTime
	midjourneyTask.ImageUrl = midjRequest.ImageUrl
	midjourneyTask.Status = midjRequest.Status
	midjourneyTask.FailReason = midjRequest.FailReason
	err = midjourneyTask.Update()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "update_midjourney_task_failed",
		}
	}

	return nil
}

func relayMidjourneyTask(c *gin.Context, relayMode int) *openai.MidjourneyResponse {
	userId := c.GetInt(ctxkey.Id)
	taskId := c.Param("id")
	mjDiscordProxyUrl := c.GetString("mj_discord_proxy_url")

	originTask := model.GetByMJId(userId, taskId)
	if originTask == nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "task_no_found",
		}
	}
	var midjourneyTask Midjourney
	midjourneyTask.MjId = originTask.MjId
	midjourneyTask.Progress = originTask.Progress
	midjourneyTask.PromptEn = originTask.PromptEn
	midjourneyTask.State = originTask.State
	midjourneyTask.SubmitTime = originTask.SubmitTime
	midjourneyTask.StartTime = originTask.StartTime
	midjourneyTask.FinishTime = originTask.FinishTime
	midjourneyTask.ImageUrl = config.GetMjUrlByTaskIdDefaultOriginUrl(originTask.MjId, originTask.ImageUrl)
	if mjDiscordProxyUrl != "" {
		midjourneyTask.ImageUrl = strings.Replace(midjourneyTask.ImageUrl, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
	}
	midjourneyTask.Status = originTask.Status
	midjourneyTask.FailReason = originTask.FailReason
	midjourneyTask.Action = originTask.Action
	midjourneyTask.Description = originTask.Description
	midjourneyTask.Prompt = originTask.Prompt
	jsonMap, err := json.Marshal(midjourneyTask)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "unmarshal_response_body_failed",
		}
	}
	_, err = io.Copy(c.Writer, bytes.NewBuffer(jsonMap))
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
	}
	return nil
}

func relayMidjourneySubmit(c *gin.Context, relayMode int) *openai.MidjourneyResponse {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	imageModel := "midjourney"

	consumeQuota := true
	_startTime := helper.GetTimestamp()

	var midjRequest openai.MidjourneyRequest
	err := common.UnmarshalBodyReusable(c, &midjRequest)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "bind_request_body_failed",
		}
	}
	meta.DetailPrompt = midjRequest.Prompt
	if relayMode == constant.RelayModeMidjourneyImagine {
		if midjRequest.Prompt == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "prompt_is_required",
			}
		}
		midjRequest.Action = "IMAGINE"
	} else if midjRequest.TaskId != "" {
		originTask := model.GetByMJId(meta.UserId, midjRequest.TaskId)
		if originTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_no_found",
			}
		} else if originTask.Action == "UPSCALE" {
			//return errorWrapper(errors.New("upscale task can not be change"), "request_params_error", http.StatusBadRequest).
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "upscale_task_can_not_be_change",
			}
		} else if originTask.Status != "SUCCESS" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_status_is_not_success",
			}

		}
		midjRequest.Prompt = originTask.Prompt
	} else if relayMode == constant.RelayModeMidjourneyChange {
		if midjRequest.TaskId == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "taskId_is_required",
			}
		} else if midjRequest.Action == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "action_is_required",
			}
		} else if midjRequest.Index == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "index_can_only_be_1_2_3_4",
			}
		}
	}

	// map model name
	isModelMapped := false
	imageModel, isModelMapped = util.GetMappedModelName(imageModel, meta.ModelMapping, meta.ModelMappingArr)
	meta.ActualModelName = imageModel

	baseURL := channeltype.ChannelBaseURLs[meta.ChannelType]
	requestURL := c.Request.URL.String()

	if c.GetString(ctxkey.BaseURL) != "" {
		baseURL = c.GetString(ctxkey.BaseURL)
	}

	//midjRequest.NotifyHook = "http://127.0.0.1:3000/mj/notify"

	fullRequestURL := fmt.Sprintf("%s%s", baseURL, requestURL)

	var requestBody io.Reader
	if isModelMapped {
		jsonStr, err := json.Marshal(midjRequest)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "marshal_text_request_failed",
			}
		}
		requestBody = bytes.NewBuffer(jsonStr)
	} else {
		requestBody = c.Request.Body
	}

	modelRatio := billingratio.GetModelRatio(imageModel, 1)
	meta.UpstreamModelRatio = modelRatio
	groupRatio := billingratio.GetGroupRatio(meta.Group)
	ratio := modelRatio * groupRatio
	//userQuota, err := model.CacheGetUserQuota(userId)
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, meta.UserId)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "get_user_quota_failed",
		}
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "user_quota_expired",
		}
	}
	sizeRatio := 1.0
	if midjRequest.Action == "UPSCALE" {
		sizeRatio = 0.2
	}
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	quota := int64(ratio * sizeRatio * 1000 * topupConvertRatio * userDiscount)
	costQuota := int64(meta.UpstreamModelRatio * meta.CostPerUnit * sizeRatio * 1000)

	if userQuota-quota < 0 {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_not_enough",
		}
	}

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "create_request_failed",
		}
	}
	//req.Header.Set("Authorization", c.Request.Header.Get("Authorization"))

	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	//mjToken := ""
	//if c.Request.Header.Get("Authorization") != "" {
	//	mjToken = strings.Split(c.Request.Header.Get("Authorization"), " ")[1]
	//}
	mjSecret := c.Request.Header.Get("Authorization")
	if mjSecret != "" {
		req.Header.Set("mj-api-secret", strings.Split(mjSecret, " ")[1])
		req.Header.Set("Authorization", mjSecret)
	}
	if mjSecret == "" {
		req.Header.Set("mj-api-secret", c.Request.Header.Get("mj-api-secret"))
		req.Header.Set("Authorization", c.Request.Header.Get("mj-api-secret"))
	}
	// print request header
	logger.SysLog(fmt.Sprintf("request header: %s", req.Header))
	logger.SysLog(fmt.Sprintf("request body: %s", midjRequest.Prompt))

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "do_request_failed",
		}
	}

	err = req.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_request_body_failed",
		}
	}
	err = c.Request.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_request_body_failed",
		}
	}
	var midjResponse openai.MidjourneyResponse

	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}
	responseBody, err := io.ReadAll(resp.Body)

	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "read_response_body_failed",
		}
	}
	err = resp.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_response_body_failed",
		}
	}

	err = json.Unmarshal(responseBody, &midjResponse)
	logger.SysLog(fmt.Sprintf("responseBody: %s", string(responseBody)))
	logger.SysLog(fmt.Sprintf("midjResponse: %v", midjResponse))
	if resp.StatusCode != 200 {
		quota = 0
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "fail_to_fetch_midjourney status_code: " + strconv.Itoa(resp.StatusCode),
		}
	}
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "unmarshal_response_body_failed",
		}
	}
	if midjResponse.Code == 24 || midjResponse.Code == 21 || midjResponse.Code == 4 {
		consumeQuota = false
	}

	midjourneyTask := &model.Midjourney{
		RequestId:   requestId,
		UserId:      meta.UserId,
		Code:        midjResponse.Code,
		Action:      midjRequest.Action,
		MjId:        midjResponse.Result,
		Prompt:      midjRequest.Prompt,
		PromptEn:    "",
		Description: midjResponse.Description,
		State:       "",
		SubmitTime:  0,
		StartTime:   0,
		FinishTime:  0,
		ImageUrl:    "",
		Status:      "",
		Progress:    "0%",
		FailReason:  "",
		ChannelId:   c.GetInt(ctxkey.ChannelId),
		Quota:       int(quota),
	}
	if midjResponse.Code == 4 || midjResponse.Code == 24 {
		midjourneyTask.FailReason = midjResponse.Description
		midjourneyTask.Status = "FAILURE"
	}
	err = midjourneyTask.Insert()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "insert_midjourney_task_failed",
		}
	}
	defer func(ctx context.Context, task *model.Midjourney) {
		if consumeQuota {
			err := model.PostConsumeTokenQuota(meta.TokenId, quota)
			if err != nil {
				logger.SysError("error consuming token remain quota: " + err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, meta.UserId)
			if err != nil {
				logger.SysError("error update user quota cache: " + err.Error())
			}
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，用时 %d秒", modelRatio, groupRatio, topupConvertRatio, userDiscount, requestDuration)
			model.UpdateUserUsedQuotaAndRequestCount(meta.UserId, quota)
			channelId := c.GetInt(ctxkey.ChannelId)
			model.UpdateChannelUsedQuota(channelId, quota)
			createdLog := model.RecordConsumeLogByDetailIfZeroQuota(ctx, "", meta.Ip, meta.RemoteIp, meta.XForwardedFor, meta.XRealIp, meta.CfConnectingIp, meta.UserId,
				meta.ChannelId, meta.PromptTokens, 0, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(imageModel),
				meta.TokenName, meta.TokenKey, meta.TokenGroup, meta.ChannelName, int(quota), int(costQuota), 0, 0, 0, meta.IsStream, logContent, "")
			helper.SafeGoroutine(func() {
				model.RecordLogExtend(ctx, createdLog, meta.DetailPrompt, meta.DetailCompletion, meta.CompletionId, meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
				// 同步推送到日志优化器
				optimizer.RecordConsumeLog(createdLog)
			})
			if task.Status == "FAILURE" {
				// code 24 敏感词补偿 不是24为渠道问题补偿
				if task.Code != 24 || model.ShouldRefundMJSensitiveWordsError(task.UserId) {
					task.Progress = "100%"
					err := model.IncreaseUserQuotaAndRedis(task.UserId, quota)
					if err != nil {
						logger.SysError("fail to increase user quota: " + err.Error())
					}
					// 抵消渠道消耗和用户消耗
					model.UpdateUserUsedQuotaAndRequestCount(task.UserId, -quota)
					model.UpdateChannelUsedQuota(task.ChannelId, -quota)
					logContent = fmt.Sprintf("%s 构图失败，补偿 %s", task.MjId, common.LogQuota(quota))
					model.RecordRefundLogByDetailIfZeroQuota(context.Background(), task.RequestId, "", "", "", "", "", task.UserId, task.ChannelId, 0, 0, fmt.Sprintf("%s-%s", imageModel, task.Mode), "", "", "", int(-quota), 0, 0, 0, false, logContent)
					err = task.Update()
					if err != nil {
						logger.SysError("fail to update task status" + err.Error())
					}
				}
			}
		}
	}(c.Request.Context(), midjourneyTask)
	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))

	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	c.Writer.WriteHeader(resp.StatusCode)

	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
	}
	err = resp.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_response_body_failed",
		}
	}
	return nil
}

func relayMidjourneyDescribe(c *gin.Context, relayMode int) *openai.MidjourneyResponse {
	ctx := c.Request.Context()
	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}
	meta := meta.GetByContext(c)

	imageModel := "midjourney"

	tokenId := c.GetInt(ctxkey.TokenId)
	channelType := c.GetInt(ctxkey.Channel)
	userId := c.GetInt(ctxkey.Id)
	consumeQuota := true
	group := c.GetString(ctxkey.Group)
	// 处理token group替换逻辑
	if config.TokenGroupChangeEnabled {
		tokenGroup := c.GetString(ctxkey.TokenGroup)
		if tokenGroup != "" {
			group = tokenGroup
		}
	}
	channelId := c.GetInt(ctxkey.ChannelId)
	channelName := c.GetString(ctxkey.ChannelName)
	_startTime := helper.GetTimestamp()

	var midjRequest openai.MidjourneyDescribeRequest
	err := common.UnmarshalBodyReusable(c, &midjRequest)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "bind_request_body_failed",
		}
	}

	// map model name
	isModelMapped := false
	imageModel, isModelMapped = util.GetMappedModelName(imageModel, meta.ModelMapping, meta.ModelMappingArr)
	baseURL := channeltype.ChannelBaseURLs[channelType]
	requestURL := c.Request.URL.String()

	if c.GetString(ctxkey.BaseURL) != "" {
		baseURL = c.GetString(ctxkey.BaseURL)
	}

	//midjRequest.NotifyHook = "http://127.0.0.1:3000/mj/notify"

	fullRequestURL := fmt.Sprintf("%s%s", baseURL, requestURL)

	var requestBody io.Reader
	if isModelMapped {
		jsonStr, err := json.Marshal(midjRequest)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "marshal_text_request_failed",
			}
		}
		requestBody = bytes.NewBuffer(jsonStr)
	} else {
		requestBody = c.Request.Body
	}

	modelRatio := billingratio.GetModelRatio(imageModel, 1)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "get_user_quota_failed",
		}
	}
	if quotaExpireTime < helper.GetTimestamp() {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "user_quota_expired",
		}
	}

	sizeRatio := 1.0
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)
	quota := int64(ratio * sizeRatio * 1000 * topupConvertRatio * userDiscount)

	if userQuota-quota < 0 {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_not_enough",
		}
	}

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "create_request_failed",
		}
	}
	//req.Header.Set("Authorization", c.Request.Header.Get("Authorization"))

	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	mjSecret := c.Request.Header.Get("Authorization")
	if mjSecret != "" {
		req.Header.Set("mj-api-secret", strings.Split(mjSecret, " ")[1])
		req.Header.Set("Authorization", mjSecret)
	}
	if mjSecret == "" {
		req.Header.Set("mj-api-secret", c.Request.Header.Get("mj-api-secret"))
		req.Header.Set("Authorization", c.Request.Header.Get("mj-api-secret"))
	}
	// print request header
	logger.SysLog(fmt.Sprintf("request header: %s", req.Header))

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "do_request_failed",
		}
	}

	err = req.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_request_body_failed",
		}
	}
	err = c.Request.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_request_body_failed",
		}
	}
	var midjResponse openai.MidjourneyResponse

	responseBody, err := io.ReadAll(resp.Body)

	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "read_response_body_failed",
		}
	}
	err = resp.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_response_body_failed",
		}
	}

	err = json.Unmarshal(responseBody, &midjResponse)
	logger.SysLog(fmt.Sprintf("responseBody: %s", string(responseBody)))
	logger.SysLog(fmt.Sprintf("midjResponse: %v", midjResponse))
	if resp.StatusCode != 200 {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "fail_to_fetch_midjourney status_code: " + strconv.Itoa(resp.StatusCode),
		}
	}
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "unmarshal_response_body_failed",
		}
	}
	if midjResponse.Code == 24 || midjResponse.Code == 21 || midjResponse.Code == 4 {
		consumeQuota = false
	}

	midjourneyTask := &model.Midjourney{
		RequestId:   requestId,
		UserId:      userId,
		Code:        midjResponse.Code,
		Action:      "DESCRIBE",
		MjId:        midjResponse.Result,
		Prompt:      "",
		PromptEn:    "",
		Description: midjResponse.Description,
		State:       "",
		SubmitTime:  0,
		StartTime:   0,
		FinishTime:  0,
		ImageUrl:    "",
		Status:      "",
		Progress:    "0%",
		FailReason:  "",
		ChannelId:   c.GetInt(ctxkey.ChannelId),
	}
	if midjResponse.Code == 4 || midjResponse.Code == 24 {
		midjourneyTask.FailReason = midjResponse.Description
		midjourneyTask.Status = "FAILURE"
	}
	err = midjourneyTask.Insert()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "insert_midjourney_task_failed",
		}
	}
	defer func(ctx context.Context, task *model.Midjourney) {
		if consumeQuota {
			err := model.PostConsumeTokenQuota(tokenId, quota)
			if err != nil {
				logger.SysError("error consuming token remain quota: " + err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.SysError("error update user quota cache: " + err.Error())
			}
			tokenName := c.GetString(ctxkey.TokenName)
			tokenKey := c.GetString(ctxkey.TokenKey)
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，用时 %d秒", modelRatio, groupRatio, topupConvertRatio, userDiscount, requestDuration)
			model.RecordConsumeLog(ctx, userId, channelId, 0, 0, imageModel, tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)
			// 违禁词构图失败补偿
			if task.Status == "FAILURE" {
				// code 24 敏感词补偿 不是24为渠道问题补偿
				if task.Code != 24 || model.ShouldRefundMJSensitiveWordsError(task.UserId) {
					task.Progress = "100%"
					err := model.IncreaseUserQuotaAndRedis(task.UserId, quota)
					if err != nil {
						logger.SysError("fail to increase user quota: " + err.Error())
					}
					// 抵消渠道消耗和用户消耗
					model.UpdateUserUsedQuotaAndRequestCount(task.UserId, -quota)
					model.UpdateChannelUsedQuota(task.ChannelId, -quota)
					logContent = fmt.Sprintf("%s 构图失败，补偿 %s", task.MjId, common.LogQuota(quota))
					model.RecordRefundLogByDetailIfZeroQuota(context.Background(), task.RequestId, "", "", "", "", "", task.UserId, task.ChannelId, 0, 0, fmt.Sprintf("%s-%s", imageModel, task.Mode), "", "", "", int(-quota), 0, 0, 0, false, logContent)
					err = task.Update()
					if err != nil {
						logger.SysError("fail to update task status" + err.Error())
					}
				}
			}
		}
	}(c.Request.Context(), midjourneyTask)
	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))

	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	c.Writer.WriteHeader(resp.StatusCode)

	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
	}
	err = resp.Body.Close()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_response_body_failed",
		}
	}
	return nil
}
