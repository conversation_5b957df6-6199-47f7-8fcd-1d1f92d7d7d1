package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/constant"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"
)

// Suno 请求和响应结构体
type SunoMusicRequest struct {
	Prompt         string `json:"prompt"`
	MV             string `json:"mv,omitempty"` // 模型版本，如 "chirp-v4"
	Title          string `json:"title,omitempty"`
	Tags           string `json:"tags,omitempty"`
	ContinueAt     int    `json:"continue_at,omitempty"`
	ContinueClipID string `json:"continue_clip_id,omitempty"`
	Task           string `json:"task,omitempty"` // 任务类型，如 "extend"
}

type SunoResponse struct {
	Code    interface{} `json:"code"` // 可能是 int 或 string
	Data    interface{} `json:"data"` // 可能是 SunoData 对象或直接的 task_id 字符串
	Message string      `json:"message"`
}

type SunoData struct {
	// 任务级别字段
	TaskID     string     `json:"task_id"`
	NotifyHook string     `json:"notify_hook"`
	Action     string     `json:"action"`
	Status     string     `json:"status"`
	FailReason string     `json:"fail_reason"`
	SubmitTime int64      `json:"submit_time"`
	StartTime  int64      `json:"start_time"`
	FinishTime int64      `json:"finish_time"`
	Progress   string     `json:"progress"`
	SearchItem string     `json:"search_item"`
	Data       []SunoClip `json:"data"` // 音频片段数组

	// 兼容旧格式的字段
	AudioURL          string      `json:"audio_url"`
	VideoURL          string      `json:"video_url"`
	ImageURL          string      `json:"image_url"`
	ImageLargeURL     string      `json:"image_large_url"`
	MajorModelVersion string      `json:"major_model_version"`
	ModelName         string      `json:"model_name"`
	Prompt            string      `json:"prompt"`
	Type              string      `json:"type"`
	Tags              string      `json:"tags"`
	Duration          float64     `json:"duration"`
	ErrorType         string      `json:"error_type"`
	ErrorMessage      string      `json:"error_message"`
	Handle            string      `json:"handle"`
	IsHandleUpdated   bool        `json:"is_handle_updated"`
	IsTrashed         bool        `json:"is_trashed"`
	Reaction          interface{} `json:"reaction"`
	CreatedAt         string      `json:"created_at"`
	Title             string      `json:"title"`
}

type SunoClip struct {
	ID                string       `json:"id"`
	Title             string       `json:"title"`
	AudioURL          string       `json:"audio_url"`
	VideoURL          string       `json:"video_url"`
	ImageURL          string       `json:"image_url"`
	ImageLargeURL     string       `json:"image_large_url"`
	MajorModelVersion string       `json:"major_model_version"`
	ModelName         string       `json:"model_name"`
	Status            string       `json:"status"`
	CreatedAt         string       `json:"created_at"`
	Handle            string       `json:"handle"`
	DisplayName       string       `json:"display_name"`
	AvatarImageURL    string       `json:"avatar_image_url"`
	UserID            string       `json:"user_id"`
	EntityType        string       `json:"entity_type"`
	IsPublic          bool         `json:"is_public"`
	IsTrashed         bool         `json:"is_trashed"`
	IsLiked           bool         `json:"is_liked"`
	IsHandleUpdated   bool         `json:"is_handle_updated"`
	IsContestClip     bool         `json:"is_contest_clip"`
	AllowComments     bool         `json:"allow_comments"`
	HasHook           bool         `json:"has_hook"`
	PlayCount         int          `json:"play_count"`
	UpvoteCount       int          `json:"upvote_count"`
	FlagCount         int          `json:"flag_count"`
	Metadata          SunoMetadata `json:"metadata"`
}

type SunoMetadata struct {
	CanRemix     bool              `json:"can_remix"`
	EditedClipID string            `json:"edited_clip_id"`
	History      []SunoHistoryItem `json:"history"`
	Infill       bool              `json:"infill"`
	IsRemix      bool              `json:"is_remix"`
	Priority     int               `json:"priority"`
	Prompt       string            `json:"prompt"`
	Stream       bool              `json:"stream"`
	Tags         string            `json:"tags"`
	Task         string            `json:"task"`
	Type         string            `json:"type"`
}

type SunoHistoryItem struct {
	ContinueAt int    `json:"continue_at"`
	ID         string `json:"id"`
	Infill     bool   `json:"infill"`
	Source     string `json:"source"`
	Type       string `json:"type"`
}

type SunoFetchRequest struct {
	TaskID string `json:"task_id" binding:"required"`
}

// RelaySuno 主路由处理器
func RelaySuno(c *gin.Context) {
	action := c.Param("action")
	taskId := c.Param("id")

	switch c.Request.Method {
	case "POST":
		if action != "" {
			// POST /suno/submit/:action (如 music, extend, continue 等)
			relaySunoSubmit(c, action)
		} else if strings.HasSuffix(c.Request.URL.Path, "/fetch") {
			// POST /suno/fetch
			relaySunoFetchPost(c)
		}
	case "GET":
		if taskId != "" {
			// GET /suno/fetch/:id
			relaySunoFetchGet(c, taskId)
		}
	default:
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "Method not allowed"})
	}
}

// relaySunoSubmit 处理 Suno 任务提交
func relaySunoSubmit(c *gin.Context, action string) {
	// 检查是否是 chat completions 格式的请求
	contentType := c.GetHeader("Content-Type")
	if strings.Contains(contentType, "application/json") {
		// 尝试解析为 chat completions 格式
		var chatRequest relaymodel.GeneralOpenAIRequest
		bodyBytes, err := io.ReadAll(c.Request.Body)
		if err == nil {
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			if json.Unmarshal(bodyBytes, &chatRequest) == nil && len(chatRequest.Messages) > 0 {
				// 这是 chat completions 格式，使用现有的处理逻辑
				relaySunoChatCompletions(c)
				return
			}
			// 重置请求体
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}
	}

	// 处理原生 Suno API 格式
	relaySunoNativeSubmit(c, action)
}

// relaySunoChatCompletions 处理 chat completions 格式的 Suno 请求
func relaySunoChatCompletions(c *gin.Context) {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	var textRequest relaymodel.GeneralOpenAIRequest
	err := common.UnmarshalBodyReusable(c, &textRequest)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证请求
	if textRequest.GetLastMessageContent() == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Prompt is required"})
		return
	}

	// 校验敏感内容
	if strings.Contains(textRequest.GetLastMessageContent(), "直接返回") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Prompt contains invalid content"})
		return
	}

	// 获取基础信息
	userId := c.GetInt("id")
	channelId := c.GetInt("channel_id")
	channelType := c.GetInt("channel")
	group := c.GetString("group")
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	channelName := c.GetString("channel_name")

	// 计算配额
	promptTokens := len(textRequest.GetLastMessageContent())

	// 获取模型倍率 - 参考 text.go 的逻辑
	modelRatio := billingratio.GetModelRatio(textRequest.Model, meta.ChannelType)
	meta.UpstreamModelRatio = modelRatio

	// 获取用户个性化费率
	userModelRatio, ok, _, err := model.CacheGetUserModelRatio(meta.UserId, textRequest.Model)
	if ok {
		modelRatio = userModelRatio
	}

	// 获取分组倍率
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	// 按次计费处理
	if meta.BillingType == common.BillingTypeByCount {
		ratio = groupRatio
	}

	// 获取充值转换率和用户折扣
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// 按次计费的固定价格
	var modelFixedPrice float64
	if meta.BillingType == common.BillingTypeByCount {
		modelFixedPrice, err = billingratio.GetModelFixedPrice(textRequest.Model)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "Model fixed price not configured"})
			return
		}

		// 替换用户个性化费率
		userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(meta.UserId, textRequest.Model)
		if ok {
			modelFixedPrice = userModelFixedPrice
		}
	}
	meta.ModelFixedPrice = modelFixedPrice

	// 获取用户配额
	userQuota, _, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to get user quota for user %d: %s", userId, err.Error()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user quota",
			"message": err.Error(),
		})
		return
	}

	// 计算所需配额
	var quota int64

	if meta.BillingType == common.BillingTypeByCount {
		// 按次计费
		quota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	} else {
		// 按量计费 - Suno 基础配额
		quota = int64(ratio * 500000 * topupConvertRatio * userDiscount)
	}

	if userQuota <= 0 || userQuota-quota < 0 {
		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient quota"})
		return
	}

	// 预扣费
	err, _ = model.PreConsumeTokenQuota(ctx, c.GetInt("token_id"), quota)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Failed to consume quota"})
		return
	}

	// 获取基础URL
	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 模型映射处理
	meta.OriginModelName = textRequest.Model
	textRequest.Model, _ = util.GetMappedModelName(textRequest.Model, meta.ModelMapping, meta.ModelMappingArr)
	meta.ActualModelName = textRequest.Model

	// 获取对应的 Suno API 模型版本
	modelVersion := getSunoModelVersion(textRequest.Model)

	// 构建 Suno 请求
	musicRequest := SunoMusicRequest{
		Prompt:         textRequest.GetLastMessageContent(),
		MV:             modelVersion, // 使用映射后的模型版本
		Title:          helper.GenerateUUID(),
		Tags:           "pop",
		ContinueAt:     0,
		ContinueClipID: "",
		Task:           "generate", // 默认任务类型
	}

	// 提交任务
	sunoResponse, err := submitSunoTask(c, meta, musicRequest, baseURL, "music", quota)
	if err != nil {
		// 返还配额
		model.PostConsumeTokenQuota(c.GetInt("token_id"), -quota)
		logger.SysError(fmt.Sprintf("Failed to submit Suno task - Model: %s, BaseURL: %s, Error: %s", musicRequest.MV, baseURL, err.Error()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to submit music generation task",
			"message": err.Error(),
		})
		return
	}

	taskId, _, err := parseSunoResponseData(sunoResponse)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to parse Suno response data: %s", err.Error()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to parse response",
			"message": err.Error(),
		})
		return
	}

	// 记录日志和计费
	startTime := helper.GetTimestamp()
	defer func() {
		requestDuration := helper.GetTimestamp() - startTime
		var logContent string
		if meta.BillingType == common.BillingTypeByCount {
			logContent = fmt.Sprintf("Suno音乐生成(按次计费)，固定价格 %.2f，分组倍率 %.2f，用时 %d秒",
				modelFixedPrice, groupRatio, requestDuration)
		} else {
			logContent = fmt.Sprintf("Suno音乐生成(按量计费)，模型倍率 %.2f，分组倍率 %.2f，用时 %d秒",
				modelRatio, groupRatio, requestDuration)
		}

		// 更新使用量
		model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
		model.UpdateChannelUsedQuota(channelId, quota)

		// 记录日志
		createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, 0, textRequest.Model,
			tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)

		helper.SafeGoroutine(func() {
			model.RecordLogExtend(ctx, createdLog, textRequest.GetLastMessageContent(), "", "",
				meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
			optimizer.RecordConsumeLog(createdLog)
		})
	}()

	// 如果是流式请求，开始轮询状态
	if textRequest.Stream {
		pollSunoTaskStatusStream(c, taskId, baseURL, textRequest.Model)
	} else {
		// 非流式请求，返回任务ID
		c.JSON(http.StatusOK, gin.H{
			"task_id": taskId,
			"status":  "submitted",
		})
	}
}

// relaySunoNativeSubmit 处理原生 Suno API 格式的提交
func relaySunoNativeSubmit(c *gin.Context, action string) {
	var musicRequest SunoMusicRequest
	if err := c.ShouldBindJSON(&musicRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 获取基础信息
	ctx := c.Request.Context()
	userId := c.GetInt("id")
	channelId := c.GetInt("channel_id")
	channelType := c.GetInt("channel")
	group := c.GetString("group")

	// 获取 meta 对象
	meta := meta.GetByContext(c)

	// 计算配额
	promptTokens := len(musicRequest.Prompt)

	// 获取模型名称 - 直接使用请求中的 MV 字段作为模型名称
	modelName := "chirp-v3-5" // 默认模型名称
	if musicRequest.MV != "" {
		// 直接使用 MV 字段作为模型名称，不做映射
		modelName = musicRequest.MV
		logger.SysLog(fmt.Sprintf("Suno原生API: 使用模型=%s", modelName))
	}

	// 获取模型倍率
	modelRatio := billingratio.GetModelRatio(modelName, channelType)
	meta.UpstreamModelRatio = modelRatio

	// 获取用户个性化费率
	userModelRatio, ok, _, _ := model.CacheGetUserModelRatio(userId, modelName)
	if ok {
		modelRatio = userModelRatio
	}

	// 获取分组倍率
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	// 按次计费处理
	if meta.BillingType == common.BillingTypeByCount {
		ratio = groupRatio
	}

	// 获取充值转换率和用户折扣
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// 按次计费的固定价格
	var modelFixedPrice float64
	if meta.BillingType == common.BillingTypeByCount {
		var err error
		modelFixedPrice, err = billingratio.GetModelFixedPrice(modelName)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "Model fixed price not configured"})
			return
		}

		// 替换用户个性化费率
		userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(userId, modelName)
		if ok {
			modelFixedPrice = userModelFixedPrice
		}
	}

	// 获取用户配额
	userQuota, _, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to get user quota for user %d: %s", userId, err.Error()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user quota",
			"message": err.Error(),
		})
		return
	}

	// 计算所需配额
	var quota int64

	if meta.BillingType == common.BillingTypeByCount {
		// 按次计费
		quota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	} else {
		// 按量计费 - Suno 基础配额
		quota = int64(ratio * 500000 * topupConvertRatio * userDiscount)
	}

	if userQuota <= 0 || userQuota-quota < 0 {
		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient quota"})
		return
	}

	// 预扣费
	err, _ = model.PreConsumeTokenQuota(ctx, c.GetInt("token_id"), quota)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Failed to consume quota"})
		return
	}

	// 获取基础URL
	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 提交任务
	sunoResponse, err := submitSunoTask(c, meta, musicRequest, baseURL, action, quota)
	if err != nil {
		// 返还配额
		model.PostConsumeTokenQuota(c.GetInt("token_id"), -quota)
		logger.SysError(fmt.Sprintf("Failed to submit Suno task - Action: %s, Model: %s, BaseURL: %s, Error: %s", action, musicRequest.MV, baseURL, err.Error()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to submit music generation task",
			"message": err.Error(),
		})
		return
	}

	// 记录日志和计费
	startTime := helper.GetTimestamp()
	defer func() {
		requestDuration := helper.GetTimestamp() - startTime
		var logContent string
		if meta.BillingType == common.BillingTypeByCount {
			logContent = fmt.Sprintf("Suno音乐生成(按次计费)，固定价格 %.2f，分组倍率 %.2f，用时 %d秒",
				modelFixedPrice, groupRatio, requestDuration)
		} else {
			logContent = fmt.Sprintf("Suno音乐生成(按量计费)，模型倍率 %.2f，分组倍率 %.2f，用时 %d秒",
				modelRatio, groupRatio, requestDuration)
		}

		// 更新使用量
		model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
		model.UpdateChannelUsedQuota(channelId, quota)

		// 记录日志
		tokenName := c.GetString("token_name")
		tokenKey := c.GetString("token_key")
		channelName := c.GetString("channel_name")
		createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, 0, modelName,
			tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)

		helper.SafeGoroutine(func() {
			model.RecordLogExtend(ctx, createdLog, musicRequest.Prompt, "", "",
				meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
			optimizer.RecordConsumeLog(createdLog)
		})
	}()

	// 返回详细的响应信息
	c.JSON(http.StatusOK, sunoResponse)
}

// relaySunoFetchPost 处理 POST /suno/fetch
func relaySunoFetchPost(c *gin.Context) {
	var fetchRequest SunoFetchRequest
	if err := c.ShouldBindJSON(&fetchRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	relaySunoFetchGet(c, fetchRequest.TaskID)
}

// relaySunoFetchGet 处理 GET /suno/fetch/:id
func relaySunoFetchGet(c *gin.Context, taskId string) {
	// 获取基础URL
	channelType := c.GetInt("channel")
	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 获取任务状态
	response, err := fetchSunoTaskStatus(c, taskId, baseURL)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to fetch task status for task %s from %s: %s", taskId, baseURL, err.Error()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch task status",
			"message": err.Error(),
		})
		return
	}

	// 返回响应
	c.Header("Content-Type", "application/json")
	c.Data(http.StatusOK, "application/json", response)
}

// submitSunoTask 提交 Suno 任务
func submitSunoTask(c *gin.Context, meta *meta.Meta, musicRequest SunoMusicRequest, baseURL string, action string, quota int64) (*SunoResponse, error) {
	requestBody, err := json.Marshal(musicRequest)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to marshal Suno request: %s", err.Error()))
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 构建URL - 根据 action 动态构建路径，添加 carry=true 参数
	url := fmt.Sprintf("/suno/submit/%s?carry=true", action)
	fullURL := baseURL + url

	// 创建请求
	req, err := http.NewRequest("POST", fullURL, bytes.NewBuffer(requestBody))
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to create HTTP request to %s: %s", fullURL, err.Error()))
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to send HTTP request to %s: %s", fullURL, err.Error()))
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logger.SysError(fmt.Sprintf("Suno API returned HTTP %d for %s", resp.StatusCode, fullURL))
	}

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to read response body from %s: %s", fullURL, err.Error()))
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 记录响应内容用于调试（仅记录前200字符避免日志过长）
	if len(body) > 200 {
		logger.SysLog(fmt.Sprintf("Suno API response from %s: %s...(truncated)", fullURL, string(body[:200])))
	} else {
		logger.SysLog(fmt.Sprintf("Suno API response from %s: %s", fullURL, string(body)))
	}

	// 解析响应
	var sunoResponse SunoResponse
	err = json.Unmarshal(body, &sunoResponse)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to unmarshal Suno response from %s: %s, body: %s", fullURL, err.Error(), string(body)))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 检查响应状态 - 兼容 int 和 string 格式
	isSuccess := false
	var codeStr string
	switch code := sunoResponse.Code.(type) {
	case int:
		isSuccess = code == 0
		codeStr = fmt.Sprintf("%d", code)
	case string:
		isSuccess = code == "success"
		codeStr = code
	case float64:
		isSuccess = code == 0
		codeStr = fmt.Sprintf("%.0f", code)
	default:
		codeStr = fmt.Sprintf("%v", code)
	}

	if !isSuccess {
		logger.SysError(fmt.Sprintf("Suno API error - Code: %s, Message: %s, URL: %s", codeStr, sunoResponse.Message, fullURL))
		return nil, fmt.Errorf("suno api error (code: %s): %s", codeStr, sunoResponse.Message)
	}

	// 创建异步任务记录
	err = createSunoTask(c, &sunoResponse, action, musicRequest, quota)
	if err != nil {
		logger.SysError("Failed to create Suno task record: " + err.Error())
		// 不返回错误，因为任务已经提交成功
	}

	return &sunoResponse, nil
}

// createSunoTask 创建 Suno 异步任务记录
func createSunoTask(c *gin.Context, sunoResponse *SunoResponse, action string, musicRequest SunoMusicRequest, quota int64) error {
	userId := c.GetInt("id")
	channelId := c.GetInt("channel_id")

	// 解析响应数据
	taskID, sunoData, err := parseSunoResponseData(sunoResponse)
	if err != nil {
		return fmt.Errorf("failed to parse response data: %w", err)
	}

	// 创建任务记录
	task := &model.Task{
		TaskID:     taskID,
		Platform:   constant.TaskPlatformSuno,
		UserId:     userId,
		ChannelId:  channelId,
		Action:     "music_generation", // 使用标准的任务类型名称
		Status:     model.TaskStatusSubmitted,
		SubmitTime: time.Now().Unix(),
		Progress:   sunoData.Progress,
		Quota:      int(quota), // 记录消耗的配额
		Properties: model.Properties{
			Input: musicRequest.Prompt,
		},
	}

	// 设置任务数据
	task.SetData(sunoResponse)

	// 插入数据库
	return task.Insert()
}

// getSunoModelVersion 根据用户请求的模型名称获取对应的 Suno API 模型版本
func getSunoModelVersion(modelName string) string {
	// 用户模型名称 -> Suno API 模型版本的映射
	modelMap := map[string]string{
		// 最新版本 v4.5+
		"chirp-bluejay": "chirp-bluejay",
		"suno-v4.5+":    "chirp-bluejay",

		// v4.5 版本
		"chirp-auk": "chirp-auk",
		"suno-v4.5": "chirp-auk",

		// v4 版本
		"chirp-v4": "chirp-v4",
		"suno-v4":  "chirp-v4",

		// v3.5 版本
		"chirp-v3-5": "chirp-v3-5",
		"suno-v3":    "chirp-v3-5",
		"suno-v3.5":  "chirp-v3-5",

		// 兼容旧版本名称
		"chirp-v3":   "chirp-v3-5",
		"chirp-v3.5": "chirp-v3-5",
		"chirp-v3-0": "chirp-v3-0",

		// 通用别名
		"suno":       "chirp-v3-5",
		"suno-music": "chirp-v3-5",

		// 其他旧版本
		"chirp-v2-xxl-alpha": "chirp-v2-xxl-alpha",
	}

	// 查找对应的 API 模型版本
	if apiVersion, exists := modelMap[modelName]; exists {
		return apiVersion
	}

	// 默认返回 chirp-v3-5
	return "chirp-v3-5"
}

// parseSunoResponseData 解析 Suno 响应数据，兼容两种格式
func parseSunoResponseData(response *SunoResponse) (taskID string, sunoData *SunoData, err error) {
	switch data := response.Data.(type) {
	case string:
		// 简单格式：data 直接是 task_id 字符串
		taskID = data
		sunoData = &SunoData{
			TaskID: data,
			Status: "PENDING", // 默认状态
		}
		return taskID, sunoData, nil

	case map[string]interface{}:
		// 复杂格式：data 是对象，需要解析
		var parsedData SunoData
		dataBytes, err := json.Marshal(data)
		if err != nil {
			return "", nil, fmt.Errorf("failed to marshal data: %w", err)
		}

		err = json.Unmarshal(dataBytes, &parsedData)
		if err != nil {
			return "", nil, fmt.Errorf("failed to unmarshal data: %w", err)
		}

		return parsedData.TaskID, &parsedData, nil

	default:
		return "", nil, fmt.Errorf("unsupported data format: %T", data)
	}
}

// relayOpenaiSunoTextHelper 处理 Suno chat completion 请求
func relayOpenaiSunoTextHelper(c *gin.Context, relayMode int) *relaymodel.ErrorWithStatusCode {
	logger.SysLog("Processing Suno chat completion request")

	// Parse the incoming chat completion request
	var chatRequest relaymodel.GeneralOpenAIRequest
	err := common.UnmarshalBodyReusable(c, &chatRequest)
	if err != nil {
		logger.SysError("Failed to parse chat completion request: " + err.Error())
		return openai.ErrorWrapper(err, "invalid_request", http.StatusBadRequest)
	}

	// Store original request for response conversion
	c.Set("original_chat_request", chatRequest)

	// Check if this is a streaming request
	if chatRequest.Stream {
		return handleStreamingSunoRequest(c, chatRequest, relayMode)
	}

	// Extract prompt from messages
	prompt := chatRequest.GetLastMessageContent()
	if prompt == "" {
		return openai.ErrorWrapper(fmt.Errorf("no prompt found"), "invalid_request", http.StatusBadRequest)
	}

	// Submit Suno task and poll for result
	result, err := submitAndPollSunoTask(c, prompt, chatRequest.Model)
	if err != nil {
		logger.SysError("Failed to submit and poll Suno task: " + err.Error())
		return openai.ErrorWrapper(err, "suno_task_failed", http.StatusInternalServerError)
	}

	// Convert the Suno result to chat completion format
	err = convertSunoResultToChatCompletion(c, result, chatRequest)
	if err != nil {
		logger.SysError("Failed to convert response: " + err.Error())
		return openai.ErrorWrapper(err, "response_conversion_failed", http.StatusInternalServerError)
	}

	return nil
}

// handleStreamingSunoRequest 处理流式 Suno 请求
func handleStreamingSunoRequest(c *gin.Context, chatRequest relaymodel.GeneralOpenAIRequest, relayMode int) *relaymodel.ErrorWithStatusCode {
	logger.SysLog("Processing streaming Suno request")

	// Store original request path for logging purposes
	originalRequestPath := c.Request.URL.Path
	c.Set("original_request_path", originalRequestPath)

	// Set streaming headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Transfer-Encoding", "chunked")
	c.Header("X-Accel-Buffering", "no")

	// Generate chat completion ID
	chatID := "chatcmpl-" + helper.GenerateUUID()
	created := time.Now().Unix()

	// Send initial progress messages
	err := sendInitialSunoProgressMessages(c, chatID, created, chatRequest)
	if err != nil {
		logger.SysError("Failed to send initial progress messages: " + err.Error())
		return openai.ErrorWrapper(err, "streaming_failed", http.StatusInternalServerError)
	}

	// Extract prompt from messages
	prompt := chatRequest.GetLastMessageContent()
	if prompt == "" {
		return openai.ErrorWrapper(fmt.Errorf("no prompt found"), "invalid_request", http.StatusBadRequest)
	}

	// Submit Suno task in background
	resultChan := make(chan *SunoTaskResult, 1)
	errorChan := make(chan error, 1)
	doneChan := make(chan bool, 1)

	go func() {
		// Submit Suno task and poll for result
		result, err := submitAndPollSunoTask(c, prompt, chatRequest.Model)
		if err != nil {
			errorChan <- err
			return
		}
		resultChan <- result
	}()

	// Start dynamic progress dots in another goroutine
	go func() {
		sendDynamicSunoGeneratingProgress(c, chatID, created, doneChan)
	}()

	// Wait for result or timeout
	select {
	case result := <-resultChan:
		// Signal to stop the progress dots
		doneChan <- true

		// Send completion and result
		err = sendSunoCompletionResult(c, chatID, created, result, chatRequest)
		if err != nil {
			logger.SysError("Failed to send completion result: " + err.Error())
			return openai.ErrorWrapper(err, "send_result_failed", http.StatusInternalServerError)
		}

	case err := <-errorChan:
		// Signal to stop the progress dots
		doneChan <- true

		// Send error message
		errorChunk := SunoChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   chatRequest.Model,
			Choices: []SunoChatCompletionChoice{
				{
					Index: 0,
					Delta: &SunoChatCompletionMessage{
						Role:    "assistant",
						Content: fmt.Sprintf("❌ Music generation failed: %s", err.Error()),
					},
					FinishReason: stringPtr("stop"),
				},
			},
		}
		chunkData, _ := json.Marshal(errorChunk)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
		c.Writer.Write([]byte("data: [DONE]\n\n"))
		c.Writer.Flush()
		return openai.ErrorWrapper(err, "generation_failed", http.StatusInternalServerError)

	case <-time.After(1200 * time.Second): // 20 minute timeout
		// Signal to stop the progress dots
		doneChan <- true

		// Send timeout error
		timeoutChunk := SunoChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   chatRequest.Model,
			Choices: []SunoChatCompletionChoice{
				{
					Index: 0,
					Delta: &SunoChatCompletionMessage{
						Role:    "assistant",
						Content: "❌ Music generation timeout: Request took too long to complete",
					},
					FinishReason: stringPtr("stop"),
				},
			},
		}
		chunkData, _ := json.Marshal(timeoutChunk)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
		c.Writer.Write([]byte("data: [DONE]\n\n"))
		c.Writer.Flush()
		return openai.ErrorWrapper(fmt.Errorf("timeout"), "timeout", http.StatusRequestTimeout)
	}

	return nil
}

// SunoTaskStatusResponse Suno 任务状态响应结构
type SunoTaskStatusResponse struct {
	Code    string             `json:"code"`
	Data    SunoTaskStatusData `json:"data"`
	Message string             `json:"message"`
}

// SunoTaskStatusData Suno 任务状态数据
type SunoTaskStatusData struct {
	TaskID     string         `json:"task_id"`
	NotifyHook string         `json:"notify_hook"`
	Action     string         `json:"action"`
	Status     string         `json:"status"`
	FailReason string         `json:"fail_reason"`
	SubmitTime int64          `json:"submit_time"`
	StartTime  int64          `json:"start_time"`
	FinishTime int64          `json:"finish_time"`
	Progress   string         `json:"progress"`
	SearchItem string         `json:"search_item"`
	Data       []SunoClipData `json:"data"`
}

// SunoClipData Suno 音频片段数据
type SunoClipData struct {
	AllowComments      bool             `json:"allow_comments"`
	AudioURL           string           `json:"audio_url"`
	AvatarImageURL     string           `json:"avatar_image_url"`
	ClipID             string           `json:"clip_id"`
	CommentCount       int              `json:"comment_count"`
	CreatedAt          string           `json:"created_at"`
	DisplayName        string           `json:"display_name"`
	Duration           float64          `json:"duration"`
	EntityType         string           `json:"entity_type"`
	Explicit           bool             `json:"explicit"`
	FlagCount          int              `json:"flag_count"`
	Handle             string           `json:"handle"`
	HasHook            bool             `json:"has_hook"`
	ID                 string           `json:"id"`
	ImageLargeURL      string           `json:"image_large_url"`
	ImageURL           string           `json:"image_url"`
	IsContestClip      bool             `json:"is_contest_clip"`
	IsFollowingCreator bool             `json:"is_following_creator"`
	IsHandleUpdated    bool             `json:"is_handle_updated"`
	IsLiked            bool             `json:"is_liked"`
	IsPublic           bool             `json:"is_public"`
	IsTrashed          bool             `json:"is_trashed"`
	MajorModelVersion  string           `json:"major_model_version"`
	Metadata           SunoClipMetadata `json:"metadata"`
	ModelName          string           `json:"model_name"`
	PlayCount          int              `json:"play_count"`
	Prompt             string           `json:"prompt"`
	State              string           `json:"state"`
	Status             string           `json:"status"`
	Tags               string           `json:"tags"`
	Title              string           `json:"title"`
	UpvoteCount        int              `json:"upvote_count"`
	VideoURL           string           `json:"video_url"`
}

// SunoClipMetadata Suno 音频片段元数据
type SunoClipMetadata struct {
	CanRemix      bool    `json:"can_remix"`
	Duration      float64 `json:"duration"`
	IsRemix       bool    `json:"is_remix"`
	Priority      int     `json:"priority"`
	Prompt        string  `json:"prompt"`
	RefundCredits bool    `json:"refund_credits"`
	Stream        bool    `json:"stream"`
	Tags          string  `json:"tags"`
	Task          string  `json:"task"`
	Type          string  `json:"type"`
}

// SunoTaskResult Suno 任务结果结构（简化版，用于内部处理）
type SunoTaskResult struct {
	TaskID     string         `json:"task_id"`
	Status     string         `json:"status"`
	Progress   string         `json:"progress"`
	FailReason string         `json:"fail_reason,omitempty"`
	Clips      []SunoClipData `json:"clips,omitempty"`
}

// SunoChatCompletionStreamResponse 流式响应结构
type SunoChatCompletionStreamResponse struct {
	ID      string                     `json:"id"`
	Object  string                     `json:"object"`
	Created int64                      `json:"created"`
	Model   string                     `json:"model"`
	Choices []SunoChatCompletionChoice `json:"choices"`
}

// SunoChatCompletionChoice 流式响应选择
type SunoChatCompletionChoice struct {
	Index        int                        `json:"index"`
	Delta        *SunoChatCompletionMessage `json:"delta,omitempty"`
	FinishReason *string                    `json:"finish_reason,omitempty"`
}

// SunoChatCompletionMessage 流式响应消息
type SunoChatCompletionMessage struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

// submitAndPollSunoTask 提交 Suno 任务并轮询结果
func submitAndPollSunoTask(c *gin.Context, prompt, modelName string) (*SunoTaskResult, error) {
	// 构建 Suno 请求
	musicRequest := SunoMusicRequest{
		Prompt:         prompt,
		MV:             getSunoModelVersion(modelName),
		Title:          helper.GenerateUUID(),
		Tags:           "pop",
		ContinueAt:     0,
		ContinueClipID: "",
		Task:           "generate",
	}

	// 获取基础信息
	ctx := c.Request.Context()
	userId := c.GetInt("id")
	channelType := c.GetInt("channel")
	group := c.GetString("group")

	// 获取 meta 对象
	meta := meta.GetByContext(c)

	// 获取模型倍率
	modelRatio := billingratio.GetModelRatio(modelName, channelType)
	meta.UpstreamModelRatio = modelRatio

	// 获取用户个性化费率
	userModelRatio, ok, _, _ := model.CacheGetUserModelRatio(meta.UserId, modelName)
	if ok {
		modelRatio = userModelRatio
	}

	// 获取分组倍率
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	// 按次计费处理
	if meta.BillingType == common.BillingTypeByCount {
		ratio = groupRatio
	}

	// 获取充值转换率和用户折扣
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// 按次计费的固定价格
	var modelFixedPrice float64
	if meta.BillingType == common.BillingTypeByCount {
		var err error
		modelFixedPrice, err = billingratio.GetModelFixedPrice(modelName)
		if err != nil {
			return nil, fmt.Errorf("model fixed price not configured for %s", modelName)
		}

		// 替换用户个性化费率
		userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(meta.UserId, modelName)
		if ok {
			modelFixedPrice = userModelFixedPrice
		}
	}
	meta.ModelFixedPrice = modelFixedPrice

	// 获取用户配额
	userQuota, _, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user quota: %w", err)
	}

	// 计算所需配额
	var quota int64

	if meta.BillingType == common.BillingTypeByCount {
		// 按次计费
		quota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	} else {
		// 按量计费 - Suno 基础配额
		quota = int64(ratio * 500000 * topupConvertRatio * userDiscount)
	}

	if userQuota <= 0 || userQuota-quota < 0 {
		return nil, fmt.Errorf("insufficient quota")
	}

	// 预扣费
	err, _ = model.PreConsumeTokenQuota(ctx, c.GetInt("token_id"), quota)
	if err != nil {
		return nil, fmt.Errorf("failed to consume quota: %w", err)
	}

	// 获取基础URL
	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 提交任务
	sunoResponse, err := submitSunoTask(c, meta, musicRequest, baseURL, "music", quota)
	if err != nil {
		// 返还配额
		model.PostConsumeTokenQuota(c.GetInt("token_id"), -quota)
		return nil, fmt.Errorf("failed to submit Suno task: %w", err)
	}

	// 轮询任务状态直到完成
	taskID, _, err := parseSunoResponseData(sunoResponse)
	if err != nil {
		// 返还配额
		model.PostConsumeTokenQuota(c.GetInt("token_id"), -quota)
		return nil, fmt.Errorf("failed to parse response data: %w", err)
	}
	maxAttempts := 120 // 最多轮询 120 次，每次间隔 5 秒，总共 10 分钟

	for attempt := 0; attempt < maxAttempts; attempt++ {
		time.Sleep(5 * time.Second) // 等待 5 秒

		// 获取任务状态
		statusBytes, err := fetchSunoTaskStatus(c, taskID, baseURL)
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to fetch task status (attempt %d): %s", attempt+1, err.Error()))
			continue
		}

		var statusResponse SunoTaskStatusResponse
		err = json.Unmarshal(statusBytes, &statusResponse)
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to parse task status (attempt %d): %s", attempt+1, err.Error()))
			continue
		}

		// 检查响应是否成功
		if statusResponse.Code != "success" {
			logger.SysError(fmt.Sprintf("Suno API returned error: %s", statusResponse.Message))
			continue
		}

		// 检查任务状态
		taskData := statusResponse.Data
		if taskData.Status == "SUCCESS" {
			logger.SysLog(fmt.Sprintf("Suno task %s completed successfully", taskID))

			// 转换为简化的结果结构
			result := &SunoTaskResult{
				TaskID:     taskData.TaskID,
				Status:     "completed",
				Progress:   taskData.Progress,
				FailReason: taskData.FailReason,
				Clips:      taskData.Data,
			}
			return result, nil
		} else if taskData.Status == "FAILED" {
			// 任务失败，返还配额
			model.PostConsumeTokenQuota(c.GetInt("token_id"), -quota)
			return nil, fmt.Errorf("suno task failed: %s", taskData.FailReason)
		}

		// 任务仍在进行中，继续轮询
		logger.SysLog(fmt.Sprintf("Suno task %s in progress: %s (attempt %d/%d)", taskID, taskData.Progress, attempt+1, maxAttempts))
	}

	// 超时
	return nil, fmt.Errorf("suno task timeout after %d attempts", maxAttempts)
}

// convertSunoResultToChatCompletion 将 Suno 结果转换为 chat completion 格式
func convertSunoResultToChatCompletion(c *gin.Context, result *SunoTaskResult, originalRequest relaymodel.GeneralOpenAIRequest) error {
	// 构建响应内容
	var contentParts []string

	// 添加原始提示作为 JSON 格式
	prompt := originalRequest.GetLastMessageContent()
	jsonPrompt := map[string]interface{}{
		"prompt": prompt,
		"model":  originalRequest.Model,
		"task":   "music_generation",
	}
	jsonBytes, _ := json.Marshal(jsonPrompt)
	jsonContent := "```json\n" + string(jsonBytes) + "\n```"
	contentParts = append(contentParts, jsonContent)

	// 添加生成完成信息
	contentParts = append(contentParts, "\n\n> ✅ Music generation complete\n")

	// 添加音频链接
	if len(result.Clips) > 0 {
		contentParts = append(contentParts, "\n\n**Generated Music:**\n")
		for i, clip := range result.Clips {
			contentParts = append(contentParts, fmt.Sprintf("\n**Track %d: %s**", i+1, clip.Title))
			if clip.AudioURL != "" {
				contentParts = append(contentParts, fmt.Sprintf("\n🎵 [Listen to audio](%s)", clip.AudioURL))
			}
			if clip.VideoURL != "" {
				contentParts = append(contentParts, fmt.Sprintf("\n🎬 [Watch video](%s)", clip.VideoURL))
			}
			if clip.ImageURL != "" {
				contentParts = append(contentParts, fmt.Sprintf("\n�️ [View cover art](%s)", clip.ImageURL))
			}
			contentParts = append(contentParts, fmt.Sprintf("\n⏱️ Duration: %.1fs", clip.Duration))
			contentParts = append(contentParts, "\n")
		}
	}

	// 创建简单的 chat completion 响应
	response := map[string]interface{}{
		"id":      "chatcmpl-" + helper.GenerateUUID(),
		"object":  "chat.completion",
		"created": time.Now().Unix(),
		"model":   originalRequest.Model,
		"choices": []map[string]interface{}{
			{
				"index": 0,
				"message": map[string]interface{}{
					"role":    "assistant",
					"content": strings.Join(contentParts, ""),
				},
				"finish_reason": "stop",
			},
		},
		"usage": map[string]interface{}{
			"prompt_tokens":     len(prompt),
			"completion_tokens": len(strings.Join(contentParts, "")),
			"total_tokens":      len(prompt) + len(strings.Join(contentParts, "")),
		},
	}

	// 将响应转换为 JSON
	responseBytes, err := json.Marshal(response)
	if err != nil {
		return fmt.Errorf("failed to marshal chat response: %w", err)
	}

	// 设置响应头
	c.Header("Content-Type", "application/json")
	c.Writer.WriteHeader(http.StatusOK)

	// 写入响应
	_, err = c.Writer.Write(responseBytes)
	if err != nil {
		return fmt.Errorf("failed to write response: %w", err)
	}

	return nil
}

// fetchSunoTaskStatus 获取 Suno 任务状态
func fetchSunoTaskStatus(c *gin.Context, taskId string, baseURL string) ([]byte, error) {
	// 构建URL - 使用正确的 Suno API 路径，添加 carry=true 参数
	url := "/suno/fetch"
	fullURL := fmt.Sprintf(baseURL+url+"/%s?carry=true", taskId)

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to create HTTP request for task status %s: %s", taskId, err.Error()))
		return nil, fmt.Errorf("failed to create request for task %s: %w", taskId, err)
	}

	// 设置请求头
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to fetch task status for %s from %s: %s", taskId, fullURL, err.Error()))
		return nil, fmt.Errorf("failed to fetch task status for %s: %w", taskId, err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logger.SysError(fmt.Sprintf("Suno API returned HTTP %d for task status %s from %s", resp.StatusCode, taskId, fullURL))
	}

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to read task status response for %s: %s", taskId, err.Error()))
		return nil, fmt.Errorf("failed to read task status response for %s: %w", taskId, err)
	}

	// 记录响应内容用于调试（仅记录前200字符避免日志过长）
	if len(body) > 200 {
		logger.SysLog(fmt.Sprintf("Task status response for %s: %s...(truncated)", taskId, string(body[:200])))
	} else if len(body) > 0 {
		logger.SysLog(fmt.Sprintf("Task status response for %s: %s", taskId, string(body)))
	}

	return body, nil
}

// pollSunoTaskStatusStream 轮询 Suno 任务状态（流式）
func pollSunoTaskStatusStream(c *gin.Context, taskID string, baseURL string, modelName string) {
	var musicResponse SunoResponse
	common.SetEventStreamHeaders(c)
	c.Writer.Header().Set("Suno-Mode", "1")

	c.Stream(func(w io.Writer) bool {
		for {
			// 获取任务状态
			responseBytes, err := fetchSunoTaskStatus(c, taskID, baseURL)
			if err != nil {
				logger.SysError("Failed to fetch Suno task status: " + err.Error())
				return false
			}

			// 解析响应
			err = json.Unmarshal(responseBytes, &musicResponse)
			if err != nil {
				logger.SysError("Failed to unmarshal Suno response: " + err.Error())
				return false
			}

			// 解析响应数据
			_, sunoData, err := parseSunoResponseData(&musicResponse)
			if err != nil {
				logger.SysError("Failed to parse Suno response data: " + err.Error())
				return false
			}

			// 构建流式响应内容
			var rsContent string
			status := sunoData.Status

			// 处理新格式的响应
			if len(sunoData.Data) > 0 {
				// 新格式：有 data 数组
				clips := sunoData.Data
				if status == "completed" || status == "COMPLETED" {
					rsContent = "音乐生成完成！\n"
					for i, clip := range clips {
						rsContent += fmt.Sprintf("片段 %d:\n", i+1)
						rsContent += fmt.Sprintf("  标题: %s\n", clip.Title)
						if clip.AudioURL != "" {
							rsContent += fmt.Sprintf("  音频链接: %s\n", clip.AudioURL)
						}
						if clip.VideoURL != "" {
							rsContent += fmt.Sprintf("  视频链接: %s\n", clip.VideoURL)
						}
						if clip.ImageURL != "" {
							rsContent += fmt.Sprintf("  图片链接: %s\n", clip.ImageURL)
						}
						rsContent += "\n"
					}
				} else if status == "failed" || status == "FAILED" {
					rsContent = fmt.Sprintf("音乐生成失败: %s", sunoData.FailReason)
				} else {
					rsContent = fmt.Sprintf("音乐生成中，当前状态: %s，进度: %s", status, sunoData.Progress)
				}
			} else {
				// 兼容旧格式或简单格式
				if status == "completed" {
					rsContent = fmt.Sprintf("音乐生成完成！\n任务ID: %s", sunoData.TaskID)
				} else if status == "failed" {
					rsContent = fmt.Sprintf("音乐生成失败: %s", sunoData.FailReason)
				} else {
					rsContent = fmt.Sprintf("音乐生成中，当前状态: %s", status)
				}
			}

			// 创建流式响应
			streamResponse := openai.SunoChatCompletionsStreamResponse{
				Id:      sunoData.TaskID,
				Object:  "chat.completion.chunk",
				Created: helper.GetTimestamp(),
				Model:   modelName,
				Choices: []openai.SunoChatCompletionsStreamResponseChoice{
					{
						Delta: struct {
							Content string `json:"content"`
							Status  string `json:"status"`
						}{
							Content: rsContent,
							Status:  sunoData.Status,
						},
						FinishReason: nil,
					},
				},
			}

			// 发送流式数据
			jsonData, err := json.Marshal(streamResponse)
			if err != nil {
				logger.SysError("Failed to marshal stream response: " + err.Error())
				return false
			}

			c.Render(-1, common.CustomEvent{Data: "data: " + string(jsonData)})
			c.Writer.Flush()

			// 检查是否完成 - 兼容新旧格式
			finalStatus := strings.ToLower(sunoData.Status)
			if finalStatus == "completed" || finalStatus == "failed" {
				break
			}

			// 等待后继续轮询
			time.Sleep(5 * time.Second)
		}
		return false
	})
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}

// sendInitialSunoProgressMessages 发送初始进度消息
func sendInitialSunoProgressMessages(c *gin.Context, chatID string, created int64, originalRequest relaymodel.GeneralOpenAIRequest) error {
	// Send initial chunk with role
	initialChunk := SunoChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   originalRequest.Model,
		Choices: []SunoChatCompletionChoice{
			{
				Index: 0,
				Delta: &SunoChatCompletionMessage{
					Role: "assistant",
				},
			},
		},
	}
	chunkData, _ := json.Marshal(initialChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Send JSON prompt
	prompt := originalRequest.GetLastMessageContent()
	jsonPrompt := map[string]interface{}{
		"prompt": prompt,
		"model":  originalRequest.Model,
		"task":   "music_generation",
	}
	jsonBytes, _ := json.Marshal(jsonPrompt)
	jsonContent := "```json\n" + string(jsonBytes) + "\n```"

	jsonChunk := SunoChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   originalRequest.Model,
		Choices: []SunoChatCompletionChoice{
			{
				Index: 0,
				Delta: &SunoChatCompletionMessage{
					Content: jsonContent,
				},
			},
		},
	}
	chunkData, _ = json.Marshal(jsonChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Send queuing message
	queueChunk := SunoChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   originalRequest.Model,
		Choices: []SunoChatCompletionChoice{
			{
				Index: 0,
				Delta: &SunoChatCompletionMessage{
					Content: "\n\n>🎵 Queuing music generation task................................................................",
				},
			},
		},
	}
	chunkData, _ = json.Marshal(queueChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	return nil
}

// sendDynamicSunoGeneratingProgress 发送动态生成进度（带动画点）
func sendDynamicSunoGeneratingProgress(c *gin.Context, chatID string, created int64, doneChan <-chan bool) {
	// Send initial generating message
	generateChunk := SunoChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "suno",
		Choices: []SunoChatCompletionChoice{
			{
				Index: 0,
				Delta: &SunoChatCompletionMessage{
					Content: "\n\n>🎵 Generating music",
				},
			},
		},
	}
	chunkData, _ := json.Marshal(generateChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Animated dots
	ticker := time.NewTicker(3 * time.Second) // Every 3 seconds
	defer ticker.Stop()

	dotCount := 0
	maxDots := 6 // Maximum number of dots before cycling

	for {
		select {
		case <-doneChan:
			// Backend processing is done, stop sending dots
			return
		case <-ticker.C:
			// Add a dot
			dotCount++
			if dotCount > maxDots {
				// Reset dots and start over
				dotCount = 1
				// Send a "reset" message to clear the line
				resetChunk := SunoChatCompletionStreamResponse{
					ID:      chatID,
					Object:  "chat.completion.chunk",
					Created: created,
					Model:   "suno",
					Choices: []SunoChatCompletionChoice{
						{
							Index: 0,
							Delta: &SunoChatCompletionMessage{
								Content: "\r>🎵 Generating music",
							},
						},
					},
				}
				chunkData, _ := json.Marshal(resetChunk)
				c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
				c.Writer.Flush()
			}

			// Send the dot
			dotChunk := SunoChatCompletionStreamResponse{
				ID:      chatID,
				Object:  "chat.completion.chunk",
				Created: created,
				Model:   "suno",
				Choices: []SunoChatCompletionChoice{
					{
						Index: 0,
						Delta: &SunoChatCompletionMessage{
							Content: ".",
						},
					},
				},
			}
			chunkData, _ := json.Marshal(dotChunk)
			c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
			c.Writer.Flush()
		}
	}
}

// sendSunoCompletionResult 发送 Suno 完成结果
func sendSunoCompletionResult(c *gin.Context, chatID string, created int64, result *SunoTaskResult, originalRequest relaymodel.GeneralOpenAIRequest) error {
	// Send completion message
	completionChunk := SunoChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   originalRequest.Model,
		Choices: []SunoChatCompletionChoice{
			{
				Index: 0,
				Delta: &SunoChatCompletionMessage{
					Content: "\n\n> ✅ Music generation complete\n",
				},
			},
		},
	}
	chunkData, _ := json.Marshal(completionChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Send audio links if available
	if len(result.Clips) > 0 {
		// Send music results header
		headerChunk := SunoChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   originalRequest.Model,
			Choices: []SunoChatCompletionChoice{
				{
					Index: 0,
					Delta: &SunoChatCompletionMessage{
						Content: "\n\n**Generated Music:**\n",
					},
				},
			},
		}
		chunkData, _ = json.Marshal(headerChunk)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
		c.Writer.Flush()

		// Send each clip information
		for i, clip := range result.Clips {
			var clipContent strings.Builder
			clipContent.WriteString(fmt.Sprintf("\n**Track %d: %s**", i+1, clip.Title))
			if clip.AudioURL != "" {
				clipContent.WriteString(fmt.Sprintf("\n🎵 [Listen to audio](%s)", clip.AudioURL))
			}
			if clip.VideoURL != "" {
				clipContent.WriteString(fmt.Sprintf("\n🎬 [Watch video](%s)", clip.VideoURL))
			}
			if clip.ImageURL != "" {
				clipContent.WriteString(fmt.Sprintf("\n🖼️ [View cover art](%s)", clip.ImageURL))
			}
			clipContent.WriteString(fmt.Sprintf("\n⏱️ Duration: %.1fs\n", clip.Duration))

			clipChunk := SunoChatCompletionStreamResponse{
				ID:      chatID,
				Object:  "chat.completion.chunk",
				Created: created,
				Model:   originalRequest.Model,
				Choices: []SunoChatCompletionChoice{
					{
						Index: 0,
						Delta: &SunoChatCompletionMessage{
							Content: clipContent.String(),
						},
					},
				},
			}
			chunkData, _ = json.Marshal(clipChunk)
			c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
			c.Writer.Flush()
		}
	}

	// Send final chunk with finish_reason
	finalChunk := SunoChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   originalRequest.Model,
		Choices: []SunoChatCompletionChoice{
			{
				Index:        0,
				Delta:        &SunoChatCompletionMessage{},
				FinishReason: stringPtr("stop"),
			},
		},
	}
	chunkData, _ = json.Marshal(finalChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Write([]byte("data: [DONE]\n\n"))
	c.Writer.Flush()

	return nil
}
