package controller

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/gookit/goutil"
	"github.com/gookit/goutil/arrutil"
	"github.com/gookit/goutil/fsutil"
	jsoniter "github.com/json-iterator/go"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/meta"
	utils2 "github.com/songquanpeng/one-api/utils"
	"github.com/tidwall/gjson"
	"gorm.io/gorm/utils"
)

var (
	_appDir             string
	_dynamicGroups      []*dynamicGroup
	_dyReq              *resty.Client
	_dynamicRouterMutex sync.RWMutex
	_routerLastUpdated  time.Time
)

func init() {
	if config.DebugEnabled {
		rootDir, err := os.Getwd()
		if err != nil {
			panic(err)
		}
		_appDir = rootDir
	} else {
		e, _ := os.Executable()
		_appDir = filepath.Dir(e)
	}
}

type dynamicGroup struct {
	Id       string                  `json:"id"`
	Title    string                  `json:"title"`
	Base     string                  `json:"base"`
	Roles    []string                `json:"roles"`
	Upstream []*dynamicGroupUpstream `json:"upstream"`
	List     []*dynamicGroupListItem `json:"list"`
}

type dynamicGroupUpstream struct {
	Id         string            `json:"id"`
	Host       string            `json:"host"`
	Header     map[string]string `json:"header"`
	TrimPrefix string            `json:"trim_prefix"`
	Transform  *struct {
		TargetFormat string                 `json:"target_format"`
		PathMap      map[string]string      `json:"path_map"`
		RequestMap   map[string]interface{} `json:"request_map"`
	} `json:"transform"`
}

type dynamicMatchRule struct {
	And      []string `json:"and"`
	Or       []string `json:"or"`
	AllMatch bool     `json:"all_match"`
}

type QuotaFieldConfig struct {
	Field      string  `json:"field"`      // gjson 路径, 例如 "usage.prompt_tokens"
	Multiplier float64 `json:"multiplier"` // 倍率
	BaseUnit   float64 `json:"base_unit"`  // 计费基数, 默认 500000
}

type dynamicPriceMatchRule struct {
	Rule        *dynamicMatchRule  `json:"rule"`
	Price       float64            `json:"price"`        // 与旧版兼容, 当无 quota_fields 时使用
	QuotaFields []QuotaFieldConfig `json:"quota_fields"` // 新增: 按字段动态计费
}

type dynamicGroupListItem struct {
	Title               string                   `json:"title"`
	Method              string                   `json:"method"`
	Path                string                   `json:"path"`
	Sub                 float64                  `json:"sub"`
	SubPriceMatches     []*dynamicPriceMatchRule `json:"sub_price_matches"` // 与 sub 互斥, 且优先级最高
	SubMatch            *dynamicMatchRule        `json:"sub_match"`
	OkMatch             *dynamicMatchRule        `json:"ok_match"`
	QuotaFields         []QuotaFieldConfig       `json:"quota_fields"` // 整体计费字段(保留)
	Desc                string                   `json:"desc"`
	Header              map[string]string        `json:"header"`
	UseClientHeader     []string                 `json:"use_client_header"`
	UseClientHeaderOver bool                     `json:"use_client_header_over"`
	Sse                 bool                     `json:"sse"`
	EnableTransform     bool                     `json:"enable_transform"`
	Check               *struct {
		Method          string                   `json:"method"`
		Path            string                   `json:"path"`
		Body            map[string]string        `json:"body"`
		Query           map[string]string        `json:"query"`
		PathVar         map[string]string        `json:"path_var"`
		Interval        int                      `json:"interval"`
		ErrField        string                   `json:"err_field"`
		OkMatch         *dynamicMatchRule        `json:"ok_match"`
		ErrorMatch      *dynamicMatchRule        `json:"error_match"`
		Timeout         int                      `json:"timeout"`
		ErrTry          int                      `json:"err_try"`
		SubPriceMatches []*dynamicPriceMatchRule `json:"sub_price_matches"` // 新增: 异步结果动态计费
	} `json:"check"`
	Group *dynamicGroup `json:"-"`
}

var _dynamicRouterMap cmap.ConcurrentMap[string, *dynamicGroupListItem]

// 计算多字段费用
func calculateMultiFieldQuota(respBody []byte, quotaFields []QuotaFieldConfig) float64 {
	if len(quotaFields) == 0 {
		return 0
	}
	var total float64
	parsed := gjson.ParseBytes(respBody)
	for _, cfg := range quotaFields {
		if cfg.Field == "" {
			continue
		}
		fv := parsed.Get(cfg.Field)
		if !fv.Exists() {
			logger.SysError(fmt.Sprintf("配置字段路径不存在: %s", cfg.Field))
			continue
		}
		var num float64
		switch fv.Type {
		case gjson.Number:
			num = fv.Float()
		case gjson.String:
			v, err := strconv.ParseFloat(fv.String(), 64)
			if err != nil {
				logger.SysError(fmt.Sprintf("字段 %s 无法转为数字: %v", cfg.Field, fv.String()))
				continue
			}
			num = v
		default:
			logger.SysError(fmt.Sprintf("字段 %s 类型不支持: %v", cfg.Field, fv.Type))
			continue
		}
		base := cfg.BaseUnit
		if base <= 0 {
			base = 500000
		}
		total += num * cfg.Multiplier * base
	}
	logger.Debug(nil, fmt.Sprintf("多字段计费总额: %f", total))
	return total
}

// calculateMultiFieldQuotaWithDetails 计算多字段费用并返回详细信息
func calculateMultiFieldQuotaWithDetails(respBody []byte, quotaFields []QuotaFieldConfig) (float64, []model.FieldBillingDetail) {
	if len(quotaFields) == 0 {
		return 0, nil
	}

	var total float64
	var details []model.FieldBillingDetail
	parsed := gjson.ParseBytes(respBody)

	for _, cfg := range quotaFields {
		if cfg.Field == "" {
			continue
		}

		detail := model.FieldBillingDetail{
			Field:       cfg.Field,
			Multiplier:  cfg.Multiplier,
			BaseUnit:    cfg.BaseUnit,
			Description: getFieldDescription(cfg.Field),
		}

		fv := parsed.Get(cfg.Field)
		if !fv.Exists() {
			logger.SysError(fmt.Sprintf("配置字段路径不存在: %s", cfg.Field))
			detail.Description = "字段不存在"
			details = append(details, detail)
			continue
		}

		var num float64
		switch fv.Type {
		case gjson.Number:
			num = fv.Float()
		case gjson.String:
			v, err := strconv.ParseFloat(fv.String(), 64)
			if err != nil {
				logger.SysError(fmt.Sprintf("字段 %s 无法转为数字: %v", cfg.Field, fv.String()))
				detail.Description = "字段值无法转换为数字"
				details = append(details, detail)
				continue
			}
			num = v
		default:
			logger.SysError(fmt.Sprintf("字段 %s 类型不支持: %v", cfg.Field, fv.Type))
			detail.Description = "字段类型不支持"
			details = append(details, detail)
			continue
		}

		base := cfg.BaseUnit
		if base <= 0 {
			base = 500000
		}

		fieldQuota := num * cfg.Multiplier * base
		detail.Value = num
		detail.BaseUnit = base
		detail.FieldQuota = fieldQuota

		total += fieldQuota
		details = append(details, detail)
	}

	logger.Debug(nil, fmt.Sprintf("多字段计费总额: %f, 明细数量: %d", total, len(details)))
	return total, details
}

// getFieldDescription 获取字段描述
func getFieldDescription(field string) string {
	descriptions := map[string]string{
		"usageMetadata.promptTokenCount":                                         "提示词Token数量",
		"usageMetadata.candidatesTokenCount":                                     "候选Token数量",
		"usageMetadata.totalTokenCount":                                          "总Token数量",
		"usageMetadata.promptTokensDetails.#(modality==\"AUDIO\").tokenCount":    "音频Token数量",
		"usageMetadata.promptTokensDetails.#(modality==\"TEXT\").tokenCount":     "文本Token数量",
		"usageMetadata.candidatesTokensDetails.#(modality==\"TEXT\").tokenCount": "候选文本Token数量",
		"usage.prompt_tokens":                                                    "提示词Token",
		"usage.completion_tokens":                                                "完成Token",
		"usage.total_tokens":                                                     "总Token",
	}

	if desc, exists := descriptions[field]; exists {
		return desc
	}
	return field
}

/* ----------------------------- 以下加载/注册逻辑保持不变 (仅调整解析字段) ----------------------------- */

func LoadDynamicMap(router *gin.Engine) (err error) {
	_dyReq = resty.New()

	// 先尝试从数据库加载
	dbGroups, err := loadDynamicRouterFromDB()
	if err != nil {
		logger.SysError("从数据库加载动态路由失败: " + err.Error())
	}

	// 如果数据库中有数据，使用数据库中的配置
	if len(dbGroups) > 0 {
		_dynamicGroups = dbGroups
	} else {
		// 否则从文件加载
		fileAbsPath := filepath.Join(_appDir, "router.json")
		if !fsutil.FileExists(fileAbsPath) {
			return
		}
		routerContent := fsutil.ReadFile(fileAbsPath)
		if len(routerContent) > 0 {
			err = jsoniter.Unmarshal(routerContent, &_dynamicGroups)
			if err != nil {
				return
			}

			// 将文件中的配置同步到数据库
			err = syncRouterConfigToDB(_dynamicGroups)
			if err != nil {
				logger.SysError("同步路由配置到数据库失败: " + err.Error())
			}
		}
	}

	// 注册路由
	err = registerDynamicRoutes(router)
	return
}

// --- loadDynamicRouterFromDB 中仅需调整 Check.Unmarshalling & 解析 ---
func loadDynamicRouterFromDB() ([]*dynamicGroup, error) {
	var groups []model.DynamicRouterGroup
	err := model.DB.Where("enabled = ?", true).Find(&groups).Error
	if err != nil {
		return nil, err
	}
	result := make([]*dynamicGroup, 0, len(groups))
	for _, g := range groups {
		dg := &dynamicGroup{
			Id:    g.GroupID,
			Title: g.Title,
			Base:  g.Base,
		}
		// roles 解析保持不变
		if err := jsoniter.Unmarshal([]byte(g.Roles), &dg.Roles); err != nil {
			logger.SysError("解析角色失败: " + err.Error())
			continue
		}

		// 加载上游配置
		var upstreams []model.DynamicRouterUpstream
		err = model.DB.Where("group_id = ? AND enabled = ?", g.ID, true).
			Order("priority DESC, weight DESC").
			Find(&upstreams).Error
		if err != nil {
			logger.SysError("加载上游配置失败: " + err.Error())
			continue
		}

		dg.Upstream = make([]*dynamicGroupUpstream, 0, len(upstreams))
		for _, up := range upstreams {
			dup := &dynamicGroupUpstream{
				Id:         up.UpstreamID,
				Host:       up.Host,
				TrimPrefix: up.TrimPrefix,
			}

			// 解析header
			err = jsoniter.Unmarshal([]byte(up.Header), &dup.Header)
			if err != nil {
				logger.SysError("解析header失败: " + err.Error())
				continue
			}

			// 解析transform
			if up.Transform != "" {
				dup.Transform = &struct {
					TargetFormat string                 `json:"target_format"`
					PathMap      map[string]string      `json:"path_map"`
					RequestMap   map[string]interface{} `json:"request_map"`
				}{}
				err = jsoniter.Unmarshal([]byte(up.Transform), dup.Transform)
				if err != nil {
					logger.SysError("解析transform失败: " + err.Error())
				}
			}

			dg.Upstream = append(dg.Upstream, dup)
		}

		// 加载端点配置
		var endpoints []model.DynamicRouterEndpoint
		if err := model.DB.Where("group_id = ? AND enabled = ?", g.ID, true).Find(&endpoints).Error; err != nil {
			logger.SysError("加载端点失败: " + err.Error())
			continue
		}
		dg.List = make([]*dynamicGroupListItem, 0, len(endpoints))
		for _, ep := range endpoints {
			dep := &dynamicGroupListItem{
				Title:               ep.Title,
				Method:              ep.Method,
				Path:                ep.Path,
				Sub:                 ep.Sub,
				Desc:                ep.Desc,
				Sse:                 ep.Sse,
				EnableTransform:     ep.EnableTransform,
				UseClientHeaderOver: ep.UseClientHeaderOver,
			}
			if ep.SubMatch != "" {
				dep.SubMatch = &dynamicMatchRule{}
				_ = jsoniter.Unmarshal([]byte(ep.SubMatch), dep.SubMatch)
			}
			if ep.OkMatch != "" {
				dep.OkMatch = &dynamicMatchRule{}
				_ = jsoniter.Unmarshal([]byte(ep.OkMatch), dep.OkMatch)
			}
			if ep.Header != "" {
				_ = jsoniter.Unmarshal([]byte(ep.Header), &dep.Header)
			}
			if ep.UseClientHeader != "" {
				_ = jsoniter.Unmarshal([]byte(ep.UseClientHeader), &dep.UseClientHeader)
			}
			if ep.SubPriceMatches != "" {
				_ = jsoniter.Unmarshal([]byte(ep.SubPriceMatches), &dep.SubPriceMatches)
			}
			if ep.QuotaFields != "" {
				_ = jsoniter.Unmarshal([]byte(ep.QuotaFields), &dep.QuotaFields)
			}
			if ep.Check != "" {
				dep.Check = &struct {
					Method          string                   `json:"method"`
					Path            string                   `json:"path"`
					Body            map[string]string        `json:"body"`
					Query           map[string]string        `json:"query"`
					PathVar         map[string]string        `json:"path_var"`
					Interval        int                      `json:"interval"`
					ErrField        string                   `json:"err_field"`
					OkMatch         *dynamicMatchRule        `json:"ok_match"`
					ErrorMatch      *dynamicMatchRule        `json:"error_match"`
					Timeout         int                      `json:"timeout"`
					ErrTry          int                      `json:"err_try"`
					SubPriceMatches []*dynamicPriceMatchRule `json:"sub_price_matches"`
				}{}
				_ = jsoniter.Unmarshal([]byte(ep.Check), dep.Check)
			}
			dg.List = append(dg.List, dep)
		}
		result = append(result, dg)
	}
	return result, nil
}

// 将配置同步到数据库
func syncRouterConfigToDB(groups []*dynamicGroup) error {
	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 清空现有数据
	if err := tx.Exec("DELETE FROM dynamic_router_endpoints").Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("DELETE FROM dynamic_router_upstreams").Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("DELETE FROM dynamic_router_groups").Error; err != nil {
		tx.Rollback()
		return err
	}

	// 插入新数据
	for _, g := range groups {
		rolesJson, _ := jsoniter.Marshal(g.Roles)

		group := model.DynamicRouterGroup{
			GroupID: g.Id,
			Title:   g.Title,
			Base:    g.Base,
			Roles:   string(rolesJson),
			Enabled: true,
		}

		if err := tx.Create(&group).Error; err != nil {
			tx.Rollback()
			return err
		}

		// 插入上游配置
		for i, up := range g.Upstream {
			headerJson, _ := jsoniter.Marshal(up.Header)
			var transformJson string
			if up.Transform != nil {
				transformBytes, _ := jsoniter.Marshal(up.Transform)
				transformJson = string(transformBytes)
			}

			upstream := model.DynamicRouterUpstream{
				GroupID:    group.ID,
				UpstreamID: up.Id,
				Host:       up.Host,
				TrimPrefix: up.TrimPrefix,
				Header:     string(headerJson),
				Transform:  transformJson,
				Priority:   len(g.Upstream) - i, // 默认按照配置顺序设置优先级
				Weight:     1,                   // 默认权重为1
				Enabled:    true,
			}

			if err := tx.Create(&upstream).Error; err != nil {
				tx.Rollback()
				return err
			}
		}

		// 插入端点配置
		for _, item := range g.List {
			var subMatchJson, okMatchJson, headerJson, useClientHeaderJson, checkJson, subPriceMatchesJson, quotaFieldsJson string

			if item.SubMatch != nil {
				subMatchBytes, _ := jsoniter.Marshal(item.SubMatch)
				subMatchJson = string(subMatchBytes)
			}

			if item.OkMatch != nil {
				okMatchBytes, _ := jsoniter.Marshal(item.OkMatch)
				okMatchJson = string(okMatchBytes)
			}

			if item.Header != nil {
				headerBytes, _ := jsoniter.Marshal(item.Header)
				headerJson = string(headerBytes)
			}

			if item.UseClientHeader != nil {
				useClientHeaderBytes, _ := jsoniter.Marshal(item.UseClientHeader)
				useClientHeaderJson = string(useClientHeaderBytes)
			}

			if item.Check != nil {
				checkBytes, _ := jsoniter.Marshal(item.Check)
				checkJson = string(checkBytes)
			}

			if item.SubPriceMatches != nil {
				subPriceMatchesBytes, _ := jsoniter.Marshal(item.SubPriceMatches)
				subPriceMatchesJson = string(subPriceMatchesBytes)
			}

			// 序列化 QuotaFields
			if item.QuotaFields != nil {
				quotaFieldsBytes, _ := jsoniter.Marshal(item.QuotaFields)
				quotaFieldsJson = string(quotaFieldsBytes)
			}

			endpoint := model.DynamicRouterEndpoint{
				GroupID:             group.ID,
				Title:               item.Title,
				Path:                item.Path,
				Method:              item.Method,
				Sub:                 item.Sub,
				SubMatch:            subMatchJson,
				SubPriceMatches:     subPriceMatchesJson,
				OkMatch:             okMatchJson,
				QuotaFields:         quotaFieldsJson,
				Desc:                item.Desc,
				Header:              headerJson,
				UseClientHeader:     useClientHeaderJson,
				UseClientHeaderOver: item.UseClientHeaderOver,
				Sse:                 item.Sse,
				EnableTransform:     item.EnableTransform,
				Check:               checkJson,
				Enabled:             true,
			}

			if err := tx.Create(&endpoint).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	return tx.Commit().Error
}

// 注册动态路由
func registerDynamicRoutes(router *gin.Engine) error {
	_dynamicRouterMap = cmap.New[*dynamicGroupListItem]()

	if len(_dynamicGroups) > 0 {
		for i := range _dynamicGroups {
			group := _dynamicGroups[i]
			// 添加id到dynamicRouterList
			if len(group.List) > 0 {
				for j := range group.List {
					item := group.List[j]
					item.Group = group
					modelId := group.Id + "-" + item.Title
					config.DynamicRouterModelMap[modelId] = item.Sub

					// 保存模型对应的角色列表
					config.DynamicRouterModelRoles[modelId] = group.Roles
				}
			}

			gr := router.Group(group.Base)
			gr.Use(middleware.LicenseAuth(), middleware.JWTAuth(), middleware.DynamicRouterTokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), func(c *gin.Context) {
				if len(group.Roles) > 0 {
					// 获取令牌分组，而不是用户分组，因为用户可以切换令牌
					tokenGroup := c.GetString(ctxkey.TokenGroup)
					if tokenGroup == "" {
						// 如果令牌没有分组，则使用用户分组作为兜底
						userId := c.GetInt(ctxkey.Id)
						tokenGroup, _ = model.CacheGetUserGroup(userId)
					}
					if !utils.Contains(group.Roles, tokenGroup) {
						c.JSON(http.StatusForbidden, gin.H{"code": -1, "message": "current token group no permission use this model"})
						c.Abort()
						return
					}
				}
			})

			for j := range group.List {
				item := group.List[j]
				item.Group = group
				pathId := item.Method + ":" + group.Base + item.Path
				_dynamicRouterMap.Set(pathId, item)
				gr.Use(func(context *gin.Context) {
					context.Set("pid", pathId)
				}).Handle(item.Method, item.Path, RelayDynamicRouter)
			}
		}
	}

	_routerLastUpdated = time.Now()
	return nil
}

// 辅助函数：记录动态路由错误日志
func recordDynamicRouterError(c *gin.Context, logType int, lastErr string, upstreamInfo map[string]interface{}, bodyBytes []byte) {
	logData := map[string]interface{}{
		"error": lastErr,
		"code":  -1,
	}
	// 合并上游信息
	for k, v := range upstreamInfo {
		logData[k] = v
	}

	logJson, _ := jsoniter.Marshal(logData)
	model.RecordSysLogToDBAndFileByGinContext(c, logType, string(logJson), string(bodyBytes))
}

func RelayDynamicRouter(c *gin.Context) {
	startTime := helper.GetTimestamp()
	reqPath := c.Request.URL.Path
	pid, _ := c.Get("pid")
	router, ok := _dynamicRouterMap.Get(utils.ToString(pid))
	if !ok {
		c.Status(http.StatusNotAcceptable)
		c.Abort()
		return
	}

	bodyBytes, readErr := io.ReadAll(c.Request.Body)
	if readErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error reading request body"})
		return
	}

	/* ------------------- 根据 SubPriceMatches 计算 subNum ------------------- */
	subNum := router.Sub // 默认使用配置的 Sub
	if len(router.SubPriceMatches) > 0 {
		for _, match := range router.SubPriceMatches {
			c.Request.Body = io.NopCloser(bytes.NewReader(bodyBytes)) // 还原 body 给 match 判断
			if dynamicRouterReqMatched(match.Rule, c) {
				// 若匹配, 优先按 quota_fields 计算, 否则用固定 price
				if len(match.QuotaFields) > 0 {
					quotaVal := calculateMultiFieldQuota(bodyBytes, match.QuotaFields)
					subNum = quotaVal / 500000.0
				} else {
					subNum = match.Price
				}
				break
			}
		}
	}

	/* ------------------- 获取用户信息和各种倍率 ------------------- */
	userId := c.GetInt("id")

	// 获取用户分组信息
	userGroup, err := model.CacheGetUserGroup(userId)
	if err != nil {
		logger.SysError("获取用户分组失败: " + err.Error())
		userGroup = "default"
	}

	// 获取各种倍率
	groupRatio := billingratio.GetGroupRatio(userGroup)
	tempMeta := &meta.Meta{
		UserId:     userId,
		TokenId:    c.GetInt(ctxkey.TokenId),
		TokenGroup: c.GetString(ctxkey.TokenGroup),
		Group:      userGroup,
		ChannelId:  c.GetInt(ctxkey.ChannelId),
	}
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(tempMeta)

	// 获取用户的动态路由折扣
	routeDiscountRatio := 1.0 // 默认无折扣
	userRouteDiscounts, err := model.CacheGetUserRouteDiscounts(userId)
	if err == nil && userRouteDiscounts != nil {
		if discount, exists := userRouteDiscounts[reqPath]; exists {
			routeDiscountRatio = discount
			logger.Info(c, fmt.Sprintf("用户 %d 在路由 %s 应用路由折扣: %.2f", userId, reqPath, routeDiscountRatio))
		}
	}

	/* ------------------- 预扣费检查 ------------------- */
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(c, userId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, openai.ErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError))
		return
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		c.JSON(http.StatusForbidden, openai.ErrorWrapper(errors.New("user quota expired"), "user_quota_expired", http.StatusForbidden))
		return
	}

	// 计算考虑各种倍率的预扣费金额
	provisionalQuota := int64(subNum * 500000 * groupRatio * topupConvertRatio * userDiscount * routeDiscountRatio)

	if userQuota <= 0 || userQuota-provisionalQuota < 0 {
		c.JSON(http.StatusForbidden, openai.ErrorWrapper(errors.New(fmt.Sprintf("user [%d] quota [%d] quota [%d] is not enough", userId, userQuota, provisionalQuota)), "insufficient_user_quota", http.StatusForbidden))
		return
	}
	if len(bodyBytes) > 0 {
		c.Request.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		logger.Debug(c, fmt.Sprintf("[%s] dynamic router req body: %v", pid, string(bodyBytes)))
	}
	var lastErr string

	// 获取可用的上游列表
	upstreams := router.Group.Upstream

	// 尝试所有上游
	for i := 0; i < len(upstreams); i++ {
		up := upstreams[i]
		tmpPath := reqPath

		// 保持原有的 TrimPrefix 逻辑
		if up.TrimPrefix != "" {
			tmpPath = strings.TrimPrefix(tmpPath, up.TrimPrefix)
		}

		// 处理路径转换 - 检查 router.EnableTransform
		if router.EnableTransform && up.Transform != nil {
			if newPath, ok := up.Transform.PathMap[tmpPath]; ok {
				tmpPath = newPath
			}
		}

		req := _dyReq.R()
		if len(router.UseClientHeader) > 0 {
			headers := c.Request.Header
			newHeaders := make(map[string]string, len(headers))
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router req headers: %v", pid, headers))
			for name, values := range headers {
				if arrutil.Contains(router.UseClientHeader, name) {
					newHeaders[name] = values[0]
				}
			}
			if router.UseClientHeaderOver && len(router.Header) > 0 {
				for s, s2 := range router.Header {
					newHeaders[s] = s2
				}
			}
			req.SetHeaders(newHeaders)
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router req final send headers: %v", pid, newHeaders))
		} else if len(router.Header) > 0 {
			req.SetHeaders(router.Header)
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router req final send headers: %v", pid, router.Header))
		} else {
			req.SetHeaders(up.Header)
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router req final send headers: %v", pid, up.Header))
		}

		// 处理请求体转换 - 检查 router.EnableTransform
		if router.EnableTransform && up.Transform != nil && len(bodyBytes) > 0 {
			var requestBody map[string]interface{}
			if err := jsoniter.Unmarshal(bodyBytes, &requestBody); err == nil {
				transformedBody := transformRequestBody(requestBody, up.Transform.RequestMap)
				req.SetBody(transformedBody)
			}
		} else if len(bodyBytes) > 0 {
			req.SetBody(bodyBytes)
		}

		// query params
		queryParams := c.Request.URL.Query()
		for key, values := range queryParams {
			for _, value := range values {
				req.SetQueryParam(key, value)
			}
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router req final send querys: %v", pid, queryParams))
		}
		if err2 := c.Request.ParseForm(); err2 != nil {
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router parse www form failed: %v", pid, queryParams))
		} else {
			formData := c.Request.PostForm
			if formData != nil && len(formData) > 0 {
				req.SetFormDataFromValues(formData)
			}
		}
		formData, tErr := c.MultipartForm()
		if tErr == nil && formData != nil {
			for key, values := range formData.Value {
				for _, value := range values {
					req.SetMultipartFormData(map[string]string{key: value})
				}
			}
			for key, values := range formData.File {
				for _, value := range values {
					var fileOpen multipart.File
					fileOpen, tErr = value.Open()
					req.SetFileReader(key, value.Filename, fileOpen)
				}
			}
			logger.Debug(c, fmt.Sprintf("[%s] dynamic router req final send formdata: %v", pid, formData))
		}
		if router.Sse {
			req.SetDoNotParseResponse(true)
		}
		reqUrl := up.Host + tmpPath
		resp, err := req.Execute(c.Request.Method, reqUrl)
		if err != nil {
			lastErr = fmt.Sprintf("up network connect failed: %s",
				strings.ReplaceAll(strings.ReplaceAll(err.Error(), reqUrl, ""), up.Host, ""))

			recordDynamicRouterError(c, model.LogTypeSystemErr, lastErr, map[string]interface{}{
				"upstream_id":   up.Id,
				"upstream_host": up.Host,
			}, bodyBytes)

			logger.SysError("dynamic upstream req failed: " + up.Host + tmpPath + " - " + err.Error())
			continue
		}
		if router.OkMatch != nil {
			if !router.Sse {
				if !dynamicRouterRespMatched(router.OkMatch, resp) {
					lastErr = "not ok match: " + string(resp.Body())
					c.Set(ctxkey.ChannelName, up.Id)
					c.Set(ctxkey.ChannelId, -1)
					recordDynamicRouterError(c, model.LogTypeSystemInfo, lastErr, map[string]interface{}{
						"upstream_id":   up.Id,
						"response_body": string(resp.Body()),
					}, bodyBytes)

					logger.SysError("dynamic upstream ok match failed: " + up.Host + tmpPath +
						", resp code: " + utils.ToString(resp.StatusCode()) +
						" , resp body: " + string(resp.Body()))
					continue
				}
			}
		} else {
			if resp.StatusCode() >= 500 {
				lastErr = "up server error: " + string(resp.Body())

				recordDynamicRouterError(c, model.LogTypeSystemErr, lastErr, map[string]interface{}{
					"upstream_id":   up.Id,
					"status_code":   resp.StatusCode(),
					"response_body": string(resp.Body()),
				}, bodyBytes)

				logger.SysError("dynamic upstream resp code >= 500: " + up.Host + tmpPath +
					" ,body: " + string(resp.Body()))
				continue
			}
		}
		for key, values := range resp.Header() {
			for _, value := range values {
				c.Header(key, value)
			}
		}

		// 用于计费的响应数据
		var billingData []byte
		var allStreamData []string

		if router.Sse {
			defer resp.RawResponse.Body.Close()
			scanner := bufio.NewScanner(resp.RawResponse.Body)

			// 用于收集流式响应中的计费信息
			var lastBillingData []byte

			for scanner.Scan() {
				_res := scanner.Bytes()
				if len(_res) == 0 {
					continue
				}

				// 收集所有流数据用于日志记录
				allStreamData = append(allStreamData, string(_res))

				// 动态检查是否包含配置的计费字段的数据块
				if len(router.QuotaFields) > 0 {
					dataStr := string(_res)
					if strings.HasPrefix(dataStr, "data: ") {
						jsonStr := strings.TrimPrefix(dataStr, "data: ")
						if jsonStr != "[DONE]" && gjson.Valid(jsonStr) {
							// 检查是否包含任何配置的计费字段
							hasAnyField := false
							parsedData := gjson.Parse(jsonStr)

							for _, fieldConfig := range router.QuotaFields {
								if fieldConfig.Field != "" {
									fieldValue := parsedData.Get(fieldConfig.Field)
									if fieldValue.Exists() && fieldValue.Type != gjson.Null {
										hasAnyField = true
										break
									}
								}
							}

							// 如果包含任何计费字段，保存这个数据块
							if hasAnyField {
								lastBillingData = []byte(jsonStr)
								logger.Debug(c, fmt.Sprintf("[%s] 流式响应中发现计费信息: %s", pid, jsonStr))
							}
						}
					}
				}

				if _, wErr := c.Writer.Write([]byte(string(_res) + "\n\n")); wErr != nil {
					logger.SysError("dynamic sse write failed: " + wErr.Error())
					logger.SysError("dynamic sse write failed and response is : " + string(_res))
				} else {
					c.Writer.Flush()
				}
			}

			// 设置用于计费的数据
			billingData = lastBillingData
		} else {
			c.Data(resp.StatusCode(), resp.Header().Get("Content-Type"), resp.Body())
			// 设置用于计费的数据
			billingData = resp.Body()
		}

		// 统一的计费处理逻辑
		if subNum > 0 && router.SubMatch != nil {
			shouldCharge := false

			if router.Sse {
				// 流式响应：检查是否成功获取到了计费信息
				shouldCharge = len(billingData) > 0
			} else {
				// 非流式响应：检查响应是否匹配
				shouldCharge = dynamicRouterRespMatched(router.SubMatch, resp)
			}

			if shouldCharge {
				userId := c.GetInt("id")
				if userId == 0 {
					userId = -1
				}
				tokenId := c.GetInt("token_id")

				// 使用之前已经获取的分组倍率和折扣信息（避免重复获取）
				// userGroup, groupRatio, topupConvertRatio, userDiscount 已在预扣费检查中获取

				// 计算基础计费金额和明细
				var calcQuota float64
				var fieldDetails []model.FieldBillingDetail
				var billingType string

				// 使用多字段计费（如果配置了）
				if len(router.QuotaFields) > 0 && len(billingData) > 0 {
					calcQuota, fieldDetails = calculateMultiFieldQuotaWithDetails(billingData, router.QuotaFields)
					billingType = "dynamic_fields"
					logger.Debug(c, fmt.Sprintf("[%s] 使用多字段计费，基础计费: %f, 字段数量: %d", pid, calcQuota, len(fieldDetails)))
				} else {
					calcQuota = subNum * 500000
					billingType = "fixed"
					// 为固定计费创建一个简单的明细
					fieldDetails = []model.FieldBillingDetail{
						{
							Field:       "fixed_price",
							Value:       subNum,
							Multiplier:  1.0,
							BaseUnit:    500000,
							FieldQuota:  calcQuota,
							Description: "固定计费",
						},
					}
				}

				// 应用分组倍率、充值转换费率和用户折扣
				finalCalcQuota := calcQuota * groupRatio * topupConvertRatio * userDiscount * routeDiscountRatio

				logger.Debug(c, fmt.Sprintf("[%s] 计费详情 - 基础: %f, 分组倍率: %f, 充值转换: %f, 用户折扣: %f, 路由折扣: %f, 最终: %f",
					pid, calcQuota, groupRatio, topupConvertRatio, userDiscount, routeDiscountRatio, finalCalcQuota))

				// integer-safe cast
				quota, _ := goutil.ToInt64(finalCalcQuota)
				baseQuota, _ := goutil.ToInt64(calcQuota)

				sErr := model.CacheUpdateUserQuota(context.Background(), userId)
				if sErr != nil {
					logger.SysError("error update user quota cache: " + sErr.Error())
				}
				sErr = model.PostConsumeTokenQuota(tokenId, quota)
				if sErr != nil {
					logger.SysError("error consuming token remain quota: " + sErr.Error())
				}
				sErr = model.CacheUpdateUserQuota(context.Background(), userId)
				if sErr != nil {
					logger.SysError("error update user quota cache: " + sErr.Error())
				}
				if quota != 0 {
					token, _ := model.GetTokenById(tokenId)
					tokenName := c.GetString("token_name")
					endTime := helper.GetTimestamp()

					var requestDuration int64
					if router.Sse {
						requestDuration = endTime - startTime // 流式响应没有resp.Time()
					} else {
						requestDuration = resp.Time().Milliseconds() / 1000
					}
					totalDuration := endTime - startTime

					durationForLogContent := requestDuration
					switch config.LogDurationType {
					case 1:
						durationForLogContent = requestDuration
					case 3:
						durationForLogContent = totalDuration
					}

					logContent := fmt.Sprintf("%s%s: %s, 调整 %d → %d, 分组倍率 %.2g, 充值转换率 %.4g, 用户折扣率 %.2f, 路由折扣率 %.2f, 用时 %d秒",
						"[extra]", reqPath, router.Desc, quota, quota, groupRatio, topupConvertRatio, userDiscount, routeDiscountRatio, durationForLogContent)

					// 构造计费明细
					billingDetail := &model.BillingDetail{
						BillingType:       billingType,
						BasePrice:         calcQuota,
						BaseUnit:          500000,
						Route:             reqPath,
						FieldDetails:      fieldDetails,
						GroupRatio:        groupRatio,
						TopupConvertRatio: topupConvertRatio,
						UserDiscount:      userDiscount,
						RouteDiscount:     routeDiscountRatio,
						BaseQuota:         baseQuota,
						FinalQuota:        quota,
					}

					// 构造包含计费明细的 other 字段
					other := helper.ConstructLogOtherWithBilling([]int{-1}, 1.0, -1000, groupRatio,
						1.0, topupConvertRatio, userDiscount, nil, helper.GetRequestType(reqPath, router.Group.Id), 1, nil, billingDetail)

					createdLog := model.RecordConsumeLogByDetailIfZeroQuota(
						context.Background(),
						"",                              // requestId
						c.ClientIP(),                    // ip
						c.RemoteIP(),                    // remoteIp
						c.GetHeader("X-Forwarded-For"),  // xForwardedFor
						c.GetHeader("X-Real-IP"),        // xRealIp
						c.GetHeader("CF-Connecting-IP"), // cfConnectingIp
						userId,                          // userId
						1,                               // channelId
						0,                               // promptTokens
						0,                               // completionTokens
						router.Group.Id,                 // model
						tokenName,                       // tokenName
						token.Key,                       // tokenKey
						"",                              // tokenGroup
						up.Id,                           // channelName
						int(quota),                      // quota
						int(quota),                      // costQuota
						requestDuration,
						requestDuration, // responseFirstByteDuration
						totalDuration,
						router.Sse, // isStream
						logContent,
						other,
					)

					helper.SafeGoroutine(func() {
						requestBody := string(bodyBytes)
						var responseBody string
						var keyResponseInfo string

						if router.Sse {
							// 将所有流数据合并作为响应体
							responseBody = strings.Join(allStreamData, "\n")
							keyResponseInfo = string(billingData) // 使用计费数据作为关键响应信息
						} else {
							responseBody = string(resp.Body())
							keyResponseInfo = responseBody
						}

						model.RecordLogExtend(context.Background(), createdLog,
							requestBody,
							responseBody,
							"",
							up.Host+tmpPath,
							keyResponseInfo,
							reqPath,
						)

						optimizer.RecordConsumeLog(createdLog)
					})

					model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
					channelId := c.GetInt("channel_id")
					model.UpdateChannelUsedQuota(channelId, quota)

					// 异步检查逻辑（仅非流式响应支持）
					if !router.Sse && router.Check != nil {
						if router.Check.Path != "" {
							cgs := gjson.ParseBytes(resp.Body())
							go dynamicRouterAsyncCheck(router, up, &cgs, userId, provisionalQuota, c.GetInt("token_id"), token, tokenName, reqPath, groupRatio, topupConvertRatio, userDiscount, routeDiscountRatio, c.ClientIP(), c.RemoteIP(), c.GetHeader("X-Forwarded-For"), c.GetHeader("X-Real-IP"), c.GetHeader("CF-Connecting-IP"))
						}
					}
				}
			}
		}
		return
	}
	if lastErr != "" {
		lastErr = utils2.ReplaceLinksAndIPs(lastErr)
	} else {
		lastErr = "try all upstream is failed, please contact us, thanks"
	}

	recordDynamicRouterError(c, model.LogTypeDownstreamError, lastErr, nil, bodyBytes)

	c.JSON(http.StatusNotAcceptable, map[string]interface{}{
		"code":    -1,
		"message": lastErr,
	})
	return
}

func dynamicRouterAsyncCheck(
	router *dynamicGroupListItem,
	upstream *dynamicGroupUpstream,
	cgs *gjson.Result,
	userId int,
	quota int64, // provisional quota debited at enqueue time
	tokenId int,
	token *model.Token,
	tokenName, oldReqPath string,
	groupRatio float64,
	topupConvertRatio float64,
	userDiscount float64,
	routeDiscountRatio float64,
	clientIP, remoteIP, xForwardedFor, xRealIP, cfConnectingIP string,
) {
	startTime := helper.GetTimestamp()
	rc := router.Check
	if rc == nil {
		return
	}

	/* ---------------- 构造轮询请求 ---------------- */
	checkPath := router.Group.Base + rc.Path
	if upstream.TrimPrefix != "" {
		checkPath = strings.TrimPrefix(checkPath, upstream.TrimPrefix)
	}
	for k, src := range rc.PathVar {
		checkPath = strings.Replace(checkPath, ":"+k, cgs.Get(src).String(), 1)
	}
	// build query & body
	var queryData map[string]string
	var bodyData string
	if len(rc.Query) > 0 {
		queryData = make(map[string]string)
		for k, src := range rc.Query {
			queryData[k] = cgs.Get(src).String()
		}
	}
	if len(rc.Body) > 0 {
		tmp := make(map[string]interface{})
		for k, src := range rc.Body {
			tmp[k] = cgs.Get(src).Value()
		}
		if b, _ := jsoniter.Marshal(tmp); len(b) > 0 {
			bodyData = string(b)
		}
	}
	loadParams := func(r *resty.Request) {
		if len(queryData) > 0 {
			r.SetQueryParams(queryData)
		}
		if bodyData != "" {
			r.SetBody(bodyData)
		}
		if len(router.Header) > 0 {
			r.SetHeaders(router.Header)
		} else {
			r.SetHeaders(upstream.Header)
		}
	}
	reqURL := upstream.Host + checkPath

	/* ------------------- 輪詢 ------------------- */
	deadline := time.Now().Add(time.Duration(rc.Timeout) * time.Second)
	var (
		hasResult bool
		isOK      bool
		finalResp *resty.Response
		errorMsg  string
		errTries  int
	)
	for time.Now().Before(deadline) {
		time.Sleep(time.Duration(rc.Interval) * time.Second)
		req := _dyReq.R()
		loadParams(req)
		resp, err := req.Execute(rc.Method, reqURL)
		if err != nil {
			logger.SysError("dynamic check request error: " + err.Error())
			if rc.ErrTry > 0 {
				errTries++
				if errTries > rc.ErrTry {
					hasResult = true
					errorMsg = "任务结果检测执行超过最大错误重试次数"
					break
				}
			}
			continue
		}
		if rc.OkMatch != nil && dynamicRouterRespMatched(rc.OkMatch, resp) {
			hasResult = true
			isOK = true
			finalResp = resp
			break
		}
		if rc.ErrorMatch != nil && dynamicRouterRespMatched(rc.ErrorMatch, resp) {
			hasResult = true
			if rc.ErrField != "" {
				errorMsg = gjson.ParseBytes(resp.Body()).Get(rc.ErrField).String()
			}
			if errorMsg == "" {
				errorMsg = "未知错误"
			}
			break
		}
	}
	if !hasResult {
		errorMsg = "任务结果检测执行超时"
	}

	/* ---------------- 成功路径: 调整真实计费 ---------------- */
	if isOK {
		realQuota := quota // default keep provisional
		if len(rc.SubPriceMatches) > 0 && finalResp != nil {
			for _, spm := range rc.SubPriceMatches {
				if dynamicRouterRespMatched(spm.Rule, finalResp) {
					if len(spm.QuotaFields) > 0 {
						calc := calculateMultiFieldQuota(finalResp.Body(), spm.QuotaFields)
						// 应用分组倍率、充值转换费率和用户折扣
						realQuota, _ = goutil.ToInt64(calc * groupRatio * topupConvertRatio * userDiscount * routeDiscountRatio)
					} else {
						// 应用分组倍率、充值转换费率和用户折扣
						realQuota = int64(spm.Price * 500000 * groupRatio * topupConvertRatio * userDiscount * routeDiscountRatio)
					}
					break
				}
			}
		}
		// 余下逻辑: 与旧版一致, 用 realQuota 替代 quota
		diff := realQuota - quota
		if diff != 0 {
			if err := model.PostConsumeTokenQuota(tokenId, diff); err != nil {
				logger.SysError("[async adjust] quota adjust failed: " + err.Error())
			}
			_ = model.CacheUpdateUserQuota(context.Background(), userId)
			endTime := helper.GetTimestamp()
			duration := endTime - startTime
			flag := "[extra]"
			if diff < 0 {
				flag = "[refund]"
			}
			logContent := fmt.Sprintf("%s%s: %s, 调整 %d → %d, 分组倍率 %.2g, 充值转换率 %.4g, 用户折扣率 %.2f, 路由折扣率 %.2f, 用时 %d秒",
				flag, oldReqPath, router.Desc, quota, realQuota, groupRatio, topupConvertRatio, userDiscount, routeDiscountRatio, duration)
			other := helper.ConstructLogOther([]int{-1}, 1.0, -1000, 1.0,
				1.0, 1.0, 1.0, nil, helper.GetRequestType(oldReqPath, router.Group.Id), 1, nil)
			createdLog := model.RecordConsumeLogByDetailIfZeroQuota(context.Background(), "", clientIP, remoteIP, xForwardedFor, xRealIP, cfConnectingIP, userId, 1, 0, 0, router.Group.Id, tokenName, token.Key, "", upstream.Id, int(diff), int(diff), 0, 0, 0, false, logContent, other)
			helper.SafeGoroutine(func() {
				respBody := ""
				if finalResp != nil {
					respBody = string(finalResp.Body())
				}
				model.RecordLogExtend(context.Background(), createdLog, "", respBody, "", reqURL, "", checkPath)
				optimizer.RecordConsumeLog(createdLog)
			})
			model.UpdateUserUsedQuotaAndRequestCount(userId, diff)
		}
		return
	}

	/* ---------------- 失败分支: 全额返还 ---------------- */
	_ = model.PostConsumeTokenQuota(tokenId, -quota)
	_ = model.CacheUpdateUserQuota(context.Background(), userId)
	endTime := helper.GetTimestamp()
	duration := endTime - startTime
	logContent := fmt.Sprintf("[返还]%s: %s, 用时 %d秒", oldReqPath, errorMsg, duration)
	// 使用专门的退款日志记录函数，确保日志类型为LogTypeRefund
	createdLog := model.RecordRefundLogByDetailIfZeroQuota(context.Background(), "", clientIP, remoteIP, xForwardedFor, xRealIP, cfConnectingIP, userId, 1, 0, 0, router.Group.Id, tokenName, token.Key, "[refund] "+upstream.Id, -int(quota), 0, 0, duration, false, logContent)
	helper.SafeGoroutine(func() {
		model.RecordLogExtend(context.Background(), createdLog, "", "", errorMsg, reqURL, "", checkPath)
		optimizer.RecordConsumeLog(createdLog)
	})
	model.UpdateUserUsedQuotaAndRequestCount(userId, -quota)
}

func dynamicRouterRespMatched(rule *dynamicMatchRule, resp *resty.Response) bool {
	var andMatch, orMatch bool
	if len(rule.And) > 0 {
		andMatch = dynamicRuleMatched(rule.And, true, resp, nil)
	}
	if !rule.AllMatch && andMatch {
		return true
	}
	if len(rule.Or) > 0 {
		orMatch = dynamicRuleMatched(rule.Or, false, resp, nil)
	}
	if rule.AllMatch {
		if andMatch && orMatch {
			return true
		}
	} else {
		if andMatch || orMatch {
			return true
		}
	}
	return false
}

func dynamicRouterReqMatched(rule *dynamicMatchRule, c *gin.Context) bool {
	var andMatch, orMatch bool
	if len(rule.And) > 0 {
		andMatch = dynamicRuleMatched(rule.And, true, nil, c)
	}
	if !rule.AllMatch && andMatch {
		return true
	}
	if len(rule.Or) > 0 {
		orMatch = dynamicRuleMatched(rule.Or, false, nil, c)
	}
	if rule.AllMatch {
		if andMatch && orMatch {
			return true
		}
	} else {
		if andMatch || orMatch {
			return true
		}
	}
	return false
}

func dynamicRuleMatched(matchRules []string, isAnd bool, resp *resty.Response, req *gin.Context) bool {
	var bodyGs *gjson.Result
	var orMatch bool
	for _, r := range matchRules {
		if !isAnd && orMatch {
			return true
		}
		rules := strings.Split(r, ":")
		if len(rules) < 2 {
			if isAnd {
				return false
			}
		}
		if rules[0] == "http_status" {
			if resp != nil {
				if utils.ToString(resp.StatusCode()) != rules[1] {
					if isAnd {
						return false
					}
				} else {
					orMatch = true
				}
			}
		} else if rules[0] == "json" {
			if bodyGs == nil {
				var bodyBytes []byte
				if resp == nil {
					var sErr error
					bodyBytes, sErr = io.ReadAll(req.Request.Body)
					if sErr != nil {
						logger.SysError("req sub match Error reading request body")
						return false
					}
				} else {
					bodyBytes = resp.Body()
				}
				gs := gjson.ParseBytes(bodyBytes)
				bodyGs = &gs
			}
			ms := strings.Split(rules[1], "=")
			if len(ms) != 2 {
				if isAnd {
					return false
				}
			}
			if !bodyGs.Get(ms[0]).Exists() {
				if isAnd {
					return false
				}
			}
			if bodyGs.Get(ms[0]).String() != ms[1] {
				if isAnd {
					return false
				}
			} else {
				orMatch = true
			}
		} else if rules[0] == "form" {
			if req != nil {
				ms := strings.Split(rules[1], "=")
				if len(ms) != 2 {
					if isAnd {
						return false
					}
				}
				formVal, b := req.GetPostForm(ms[0])
				if !b {
					if isAnd {
						return false
					}
				}
				if formVal != ms[1] {
					if isAnd {
						return false
					}
				} else {
					orMatch = true
				}
			}
		} else if rules[0] == "query" {
			if req != nil {
				ms := strings.Split(rules[1], "=")
				if len(ms) != 2 {
					if isAnd {
						return false
					}
				}
				query, b := req.GetQuery(ms[0])
				if !b {
					if isAnd {
						return false
					}
				}
				if query != ms[1] {
					if isAnd {
						return false
					}
				} else {
					orMatch = true
				}
			}
		}
	}
	if !isAnd && !orMatch {
		return false
	}
	return true
}

// 新增请求体转换函数
func transformRequestBody(original map[string]interface{}, mapping map[string]interface{}) map[string]interface{} {
	// 如果映射为空，直接返回原始请求体
	if mapping == nil || len(mapping) == 0 {
		return original
	}

	result := make(map[string]interface{})

	// 先应用默认值
	if defaults, ok := mapping["_defaults"].(map[string]interface{}); ok {
		for key, val := range defaults {
			result[key] = val
		}
	}

	// 获取需要排除的字段
	excludeFields := make(map[string]bool)
	if exclude, ok := mapping["_exclude"].([]interface{}); ok {
		for _, field := range exclude {
			if strField, ok := field.(string); ok {
				excludeFields[strField] = true
			}
		}
	}

	// 应用映射规则
	for targetKey, mapRule := range mapping {
		// 跳过特殊配置字段
		if targetKey == "_defaults" || targetKey == "_exclude" {
			continue
		}

		switch rule := mapRule.(type) {
		case string:
			// 处理 JSONPath 映射
			if strings.HasPrefix(rule, "$.") {
				path := strings.TrimPrefix(rule, "$.")
				if val, ok := original[path]; ok && !excludeFields[path] {
					result[targetKey] = val
				}
			} else {
				// 直接值映射
				result[targetKey] = rule
			}
		case map[string]interface{}:
			// 处理值映射表
			if originalVal, ok := original[targetKey]; ok && !excludeFields[targetKey] {
				if mappedVal, exists := rule[fmt.Sprint(originalVal)]; exists {
					result[targetKey] = mappedVal
				}
			}
		}
	}

	return result
}

// 修改重新加载路由的函数
func ReloadDynamicRoutes(router *gin.Engine) error {
	_dynamicRouterMutex.Lock()
	defer _dynamicRouterMutex.Unlock()

	// 清除现有路由映射
	_dynamicRouterMap = cmap.New[*dynamicGroupListItem]()

	// 从数据库重新加载
	dbGroups, err := loadDynamicRouterFromDB()
	if err != nil {
		return err
	}

	// 更新全局配置
	_dynamicGroups = dbGroups

	// 重新构建路由映射，但不重新注册路由
	if len(_dynamicGroups) > 0 {
		for i := range _dynamicGroups {
			group := _dynamicGroups[i]

			// 更新模型映射
			if len(group.List) > 0 {
				for j := range group.List {
					item := group.List[j]
					item.Group = group
					modelId := group.Id + "-" + item.Title
					config.DynamicRouterModelMap[modelId] = item.Sub

					// 保存模型对应的角色列表
					config.DynamicRouterModelRoles[modelId] = group.Roles
				}
			}

			// 更新路由映射
			for j := range group.List {
				item := group.List[j]
				item.Group = group
				pathId := item.Method + ":" + group.Base + item.Path
				_dynamicRouterMap.Set(pathId, item)
			}
		}
	}

	_routerLastUpdated = time.Now()
	return nil
}
