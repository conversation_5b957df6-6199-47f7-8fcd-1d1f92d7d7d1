{"垫图功能入参示例": {"说明": "以下是VEO 3垫图功能的各种入参格式，可直接用于API调试", "1_图片生成视频_base64": {"model": "veo-3.0-generate-preview", "prompt": "让图片中的花朵在微风中轻摆，添加自然的动态效果", "duration_seconds": 8, "aspect_ratio": "16:9", "sample_count": 1, "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=", "image_mime_type": "image/jpeg", "enhance_prompt": true, "generate_audio": true, "add_watermark": true, "include_rai_reason": true}, "2_图片生成视频_GCS_URI": {"model": "veo-3.0-generate-preview", "prompt": "基于参考图片生成动态视频，保持画面风格一致", "duration_seconds": 10, "aspect_ratio": "9:16", "sample_count": 1, "image": "gs://my-bucket/reference-image.jpg", "image_mime_type": "image/jpeg", "enhance_prompt": true, "generate_audio": false, "add_watermark": false}, "3_视频垫图_最后一帧_base64": {"model": "veo-3.0-generate-preview", "prompt": "基于最后一帧图片，生成后续的视频内容，保持场景连贯性", "duration_seconds": 5, "aspect_ratio": "16:9", "sample_count": 1, "last_frame": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==", "last_frame_mime_type": "image/png", "enhance_prompt": true, "generate_audio": true, "add_watermark": true}, "4_视频垫图_最后一帧_GCS_URI": {"model": "veo-3.0-generate-preview", "prompt": "从最后一帧开始，继续生成流畅的视频内容", "duration_seconds": 8, "aspect_ratio": "16:9", "sample_count": 1, "last_frame": "gs://my-bucket/last-frame.jpg", "last_frame_mime_type": "image/jpeg", "enhance_prompt": true, "generate_audio": false}, "5_视频延长_base64": {"model": "veo-3.0-generate-preview", "prompt": "延长现有视频，保持动作和场景的连贯性", "duration_seconds": 10, "aspect_ratio": "16:9", "sample_count": 1, "video": "data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAACKBtZGF0AAAC...", "video_mime_type": "video/mp4", "enhance_prompt": true, "generate_audio": true, "add_watermark": false}, "6_视频延长_GCS_URI": {"model": "veo-3.0-generate-preview", "prompt": "基于现有视频内容，生成后续片段", "duration_seconds": 12, "aspect_ratio": "16:9", "sample_count": 1, "video": "gs://my-bucket/source-video.mp4", "video_mime_type": "video/mp4", "enhance_prompt": true, "generate_audio": true}, "7_组合垫图功能": {"model": "veo-3.0-generate-preview", "prompt": "结合图片和最后一帧，创建一个连贯的视频序列", "duration_seconds": 15, "aspect_ratio": "16:9", "sample_count": 1, "image": "gs://my-bucket/reference-image.jpg", "image_mime_type": "image/jpeg", "last_frame": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...", "last_frame_mime_type": "image/png", "negative_prompt": "模糊，低质量，不连贯", "seed": 12345, "enhance_prompt": true, "generate_audio": true, "add_watermark": false, "include_rai_reason": true, "person_generation": "allow_adult"}, "8_高级参数示例": {"model": "veo-3.0-generate-preview", "prompt": "专业级视频生成，包含所有高级参数", "duration_seconds": 20, "aspect_ratio": "21:9", "sample_count": 2, "image": "gs://my-bucket/hd-reference.jpg", "image_mime_type": "image/jpeg", "negative_prompt": "低分辨率，噪点，失真，不自然的动作", "seed": 98765, "enhance_prompt": true, "generate_audio": true, "add_watermark": false, "include_rai_reason": true, "person_generation": "allow_all", "temperature": 0.7, "top_p": 0.9}}, "OpenAI兼容格式": {"说明": "如果您的API遵循OpenAI格式，可以使用以下结构", "messages格式": {"model": "veo-3.0-generate-preview", "messages": [{"role": "user", "content": [{"type": "text", "text": "基于这张图片生成一个8秒的视频，让场景动起来"}, {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."}}]}], "duration_seconds": 8, "aspect_ratio": "16:9", "enhance_prompt": true}, "多模态内容": {"model": "veo-3.0-generate-preview", "messages": [{"role": "user", "content": [{"type": "text", "text": "基于参考图片和最后一帧，生成连贯的视频内容"}, {"type": "image_url", "image_url": {"url": "gs://my-bucket/reference.jpg"}}, {"type": "last_frame_url", "last_frame_url": {"url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."}}]}], "duration_seconds": 10, "aspect_ratio": "16:9"}, "视频延长格式": {"model": "veo-3.0-generate-preview", "messages": [{"role": "user", "content": [{"type": "text", "text": "延长这个视频，保持动作连贯"}, {"type": "video_url", "video_url": {"url": "gs://my-bucket/source-video.mp4"}}]}], "duration_seconds": 12, "aspect_ratio": "16:9"}}, "调试提示": {"base64编码注意事项": ["图片base64: data:image/jpeg;base64,{base64_content}", "视频base64: data:video/mp4;base64,{base64_content}", "确保base64内容完整，不要截断"], "GCS_URI格式": ["格式: gs://bucket-name/path/to/file", "确保服务账号有读取权限", "文件必须存在且可访问"], "MIME类型支持": {"图片": ["image/jpeg", "image/png", "image/webp"], "视频": ["video/mp4", "video/quicktime", "video/avi"]}, "参数限制": {"duration_seconds": "5-20秒", "aspect_ratio": ["16:9", "9:16", "1:1", "4:3", "21:9"], "sample_count": "1-4个样本"}, "常见错误": ["base64格式错误: 检查data:前缀和编码完整性", "GCS权限问题: 确保服务账号有存储读取权限", "文件大小限制: 图片<10MB，视频<100MB", "MIME类型不匹配: 确保文件类型与mime_type一致"]}}