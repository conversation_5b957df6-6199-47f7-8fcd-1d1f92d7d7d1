package router

import (
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/controller"
	"github.com/songquanpeng/one-api/controller/auth"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/middleware/redis_armor"
)

func SetApiRouter(router *gin.Engine) {
	router.Use(middleware.CORS())
	apiRouter := router.Group("/api")
	apiRouter.Use(gzip.Gzip(gzip.DefaultCompression))
	apiRouter.Use(middleware.GlobalAPIRateLimit())
	{
		apiRouter.POST("/fix_database", middleware.RootAuth(), controller.FixDatabase) //这个要管理员权限
		apiRouter.POST("/captcha", middleware.CriticalRateLimit(), controller.GetCaptcha)
		apiRouter.POST("/checkin_captcha", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.GetCheckinCaptcha)
		apiRouter.GET("/version", controller.GetVersion)
		apiRouter.GET("/status", controller.GetStatus)
		apiRouter.GET("/models", middleware.UserAuth(), controller.DashboardListModels)
		apiRouter.GET("/adminStatus", middleware.AdminAuth(), redis_armor.Shield(300, redis_armor.AdminStatusCacheKey), controller.GetAdminStatus)
		apiRouter.GET("/notice", controller.GetNotice)
		apiRouter.GET("/about", controller.GetAbout)
		apiRouter.GET("/pricing", middleware.JWTAuth(), middleware.OptionalUserAuth(), controller.GetPrice)
		apiRouter.GET("/document", controller.GetDocument)
		apiRouter.GET("/midjourney", controller.GetMidjourney)
		apiRouter.GET("/home_page_content", controller.GetHomePageContent)
		apiRouter.GET("/verification", middleware.CriticalRateLimit(), middleware.DynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), controller.SendEmailVerification)
		apiRouter.GET("/send_email_with_registrations", middleware.CriticalRateLimit(), middleware.DynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), controller.SendEmailWithRegistrations)
		apiRouter.GET("/reset_password", middleware.CriticalRateLimit(), middleware.DynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), controller.SendPasswordResetEmail)
		apiRouter.GET("/test_email", middleware.CriticalRateLimit(), middleware.RootAuth(), controller.SendTestEmail)
		apiRouter.POST("/proxy_email", middleware.RootAuth(), controller.ProxyEmail)
		apiRouter.GET("/test_wx_pusher", middleware.CriticalRateLimit(), middleware.RootAuth(), controller.SendTestWxPusher)
		apiRouter.GET("/test_qy_wx_bot_push", middleware.CriticalRateLimit(), middleware.RootAuth(), controller.SendTestQyWxBotPush)
		apiRouter.POST("/user/reset", middleware.CriticalRateLimit(), controller.ResetPassword)
		apiRouter.GET("/oauth/github", middleware.CriticalRateLimit(), auth.GitHubOAuth)
		apiRouter.GET("/oauth/google", middleware.CriticalRateLimit(), auth.GoogleOAuth)
		apiRouter.GET("/oauth/oidc", middleware.CriticalRateLimit(), auth.OidcAuth)
		apiRouter.GET("/oauth/lark", middleware.CriticalRateLimit(), auth.LarkOAuth)
		apiRouter.GET("/oauth/state", middleware.CriticalRateLimit(), auth.GenerateOAuthCode)
		apiRouter.GET("/oauth/wechat", middleware.CriticalRateLimit(), auth.WeChatAuth)
		apiRouter.GET("/oauth/wechat/bind", middleware.CriticalRateLimit(), middleware.UserAuth(), auth.WeChatBind)
		apiRouter.GET("/oauth/email/bind", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.EmailBind)
		apiRouter.GET("/oauth/sms/bind", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.SMSBind)
		apiRouter.GET("/send_sms", middleware.CriticalRateLimit(), middleware.DynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), controller.SendSMS)
		apiRouter.GET("/oauth/telegram/login", middleware.CriticalRateLimit(), controller.TelegramLogin)
		apiRouter.GET("/oauth/telegram/bind", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.TelegramBind)
		apiRouter.GET("/all_models", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.GetAllModels)
		apiRouter.POST("/topup", middleware.AdminAuth(), controller.AdminTopUp)
		// 添加代理API路由
		apiRouter.POST("/proxy", middleware.JWTAuth(), middleware.UserAuth(), controller.ProxyAPI)

		userRoute := apiRouter.Group("/user")
		{
			userRoute.POST("/register", middleware.CriticalRateLimit(), middleware.RegisterDynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), controller.Register)
			userRoute.POST("/login", middleware.CriticalRateLimit(), middleware.DynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), controller.Login)
			userRoute.POST("/login/refresh", middleware.JWTCrossLoginCheck(), middleware.JWTAuth(), controller.RefreshToken)
			//sms login，短信登录，此时如果能拿到准确的验证码，说明已经通过了 send_sms的验证，所以不再需要再次验证
			userRoute.POST("/login/sms", middleware.CriticalRateLimit(), controller.SMSLogin)
			userRoute.POST("/checkin", middleware.CheckinEnabledCheck(), middleware.CriticalRateLimit(), middleware.CheckinDynamicVerificationMiddleware(&common.CustomVerificationTypesConfig), middleware.JWTAuth(), middleware.UserAuth(), controller.Checkin)
			userRoute.GET("/logout", controller.Logout)
			userRoute.GET("/epay/notify", controller.EpayNotify)

			selfRoute := userRoute.Group("/")
			selfRoute.Use(middleware.JWTAuth(), middleware.UserAuth())
			{
				selfRoute.GET("/dashboard", controller.GetUserDashboard)
				selfRoute.GET("/self", controller.GetSelf)
				selfRoute.GET("/self/quota", controller.GetSelfQuota)
				selfRoute.PUT("/self", controller.UpdateSelf)
				selfRoute.POST("/self/update_password", controller.UpdatePassword)
				selfRoute.DELETE("/self", controller.DeleteSelf)
				// 用户超时配置路由
				//selfRoute.GET("/timeout_configs", controller.GetUserTimeoutConfigs)
				//selfRoute.GET("/timeout_config", controller.GetUserTimeoutConfig)
				//selfRoute.POST("/timeout_config", controller.SetUserTimeoutConfig)
				//selfRoute.DELETE("/timeout_config", controller.DeleteUserTimeoutConfig)
				//2024-02-18 修改/token 为 /access_token，避免产生歧义
				selfRoute.POST("/access_token", controller.GenerateAccessToken)
				// 新的访问令牌管理API
				selfRoute.POST("/access_tokens", middleware.RootAuth(), controller.CreateNewAccessToken)
				selfRoute.GET("/access_tokens", controller.GetUserAccessTokens)
				selfRoute.PUT("/access_tokens/disable/:id", controller.DisableAccessToken)
				selfRoute.PUT("/access_tokens/enable/:id", controller.EnableAccessToken)
				selfRoute.DELETE("/access_tokens/:id", controller.DeleteAccessToken)

				selfRoute.GET("/aff", controller.GetAffCode)
				selfRoute.POST("/topup", controller.TopUp)
				// 添加支付宝当面付相关路由
				selfRoute.POST("/topup/alipay_f2f", controller.AlipayF2FPay)
				selfRoute.GET("/topup/alipay_f2f/status", controller.AlipayF2FStatus)
				// 添加随行付聚合支付相关路由
				selfRoute.POST("/topup/suixingpay", controller.SuixingpayPay)
				selfRoute.GET("/topup/suixingpay/status", controller.SuixingpayStatus)
				selfRoute.POST("/pay", controller.RequestEpay)
				selfRoute.POST("/amount", controller.RequestAmount)
				selfRoute.POST("/aff_transfer", controller.TransferAffQuota)
				selfRoute.GET("/available_model", controller.GetAvailableModel)
				selfRoute.GET("/available_models_by_groups", controller.GetAvailableModelsByGroups)
				selfRoute.POST("/transfer", controller.Transfer)
				selfRoute.GET("/transfer/fee", controller.GetTransferFee)
				//selfRoute.POST("/redPacket", controller.ReceiveRedPacket)
				//存在问题，暂时不开放
				selfRoute.GET("/redPacket/status", controller.GetRedPacketStatus)
				selfRoute.GET("/available_models", controller.GetUserAvailableModels)
				// 添加新的时区更新路由
				selfRoute.POST("/update_timezone", controller.UpdateUserTimezone)

				// 用户通知设置相关路由
				selfRoute.GET("/notification_setting", controller.GetUserNotificationSetting)
				selfRoute.PUT("/notification_setting", controller.UpdateUserNotificationSetting)
				selfRoute.GET("/notification_events", controller.GetAvailableNotificationEvents)
				selfRoute.POST("/notification_test", controller.TestNotification)
			}

			// 移除管理员超时配置路由，将它们移动到独立的管理员路由组
			adminRoute := userRoute.Group("/")
			adminRoute.Use(middleware.RootAuth([]int64{middleware.PermissionUser}...))
			{
				adminRoute.GET("/", controller.GetAllUsers)
				adminRoute.GET("/count", controller.CountAllUsers)
				//adminRoute.GET("/search", controller.SearchUsers)
				adminRoute.GET("/:id", controller.GetUser)
				adminRoute.POST("/", controller.CreateUser)
				adminRoute.POST("/manage", controller.ManageUser)
				adminRoute.POST("/admin_topup", controller.AdminTopUp)                                    //管理员手动为用户充值
				adminRoute.POST("/admin_update_quota_expire_time", controller.AdminUpdateQuotaExpireTime) //管理员手动为用户修改余额过期时间
				adminRoute.PUT("/", controller.UpdateUser)
				adminRoute.DELETE("/:id", controller.DeleteUser)
				adminRoute.POST("/reset_redis_quota/:id", controller.ResetUserRedisQuota) // 重置用户Redis余额缓存
			}
			// 新增代理商路由组
			agencyRoute := userRoute.Group("/agency")
			agencyRoute.Use(middleware.AgencyAuth())
			{
				agencyRoute.GET("/", controller.GetAgencyUsers)
				agencyRoute.GET("/count", controller.CountAgencyUsers)
				agencyRoute.GET("/:id", controller.GetAgencyUser)
				agencyRoute.POST("/", controller.CreateAgencyUser)
				agencyRoute.POST("/manage", controller.ManageUser)
				agencyRoute.PUT("/", controller.UpdateAgencyUser)
				agencyRoute.DELETE("/:id", controller.DeleteAgencyUser)
			}
		}
		userExRoute := apiRouter.Group("/user_ex")
		{
			selfExRoute := userExRoute.Group("/")
			selfExRoute.Use(middleware.JWTAuth(), middleware.UserAuth())
			{
				//管理员获取全部/指定用户扩展信息，需要管理员权限
				//selfExRoute.GET("/", controller.GetAllUserExtendExceptChatRecord)
				//selfExRoute.GET("/:user_id", controller.GetUserExExceptChatRecordById)
				selfExRoute.GET("/self", controller.GetSelfUserExExceptChatRecord)
				selfExRoute.POST("/self/create", controller.CreateSelfUserEx)
				selfExRoute.GET("/self/chat_record", controller.GetSelfChatRecord)
				selfExRoute.POST("/self/chat_record", controller.SaveSelfChatRecord)
				selfExRoute.DELETE("/self/chat_record", controller.DeleteSelfChatRecord)
				selfExRoute.GET("/self/education_certification_status", controller.GetSelfEducationCertification)
			}
		}

		// 套餐配置
		packagePlanRoute := apiRouter.Group("/packagePlan")
		packagePlanRoute.Use(middleware.JWTAuth(), middleware.UserAuth())
		{
			packagePlanRoute.GET("/", controller.GetAllPackagePlans)
			packagePlanRoute.GET("/count", controller.CountAllPackagePlans)
			packagePlanRoute.GET("/:id", middleware.RootAuth([]int64{middleware.PermissionPackagePlan}...), controller.GetPackagePlan)
			packagePlanRoute.POST("/", middleware.RootAuth([]int64{middleware.PermissionPackagePlan}...), controller.AddPackagePlan)
			packagePlanRoute.PUT("/", middleware.RootAuth([]int64{middleware.PermissionPackagePlan}...), controller.UpdatePackagePlan)
			packagePlanRoute.DELETE("/:id", middleware.RootAuth([]int64{middleware.PermissionPackagePlan}...), controller.DeletePackagePlan)
			packagePlanRoute.DELETE("/deleteByIds", middleware.RootAuth([]int64{middleware.PermissionPackagePlan}...), controller.DeletePackagePlanByIds)
		}
		// 套餐实例
		packagePlanInstanceRoute := apiRouter.Group("/packagePlanInstance")
		packagePlanInstanceRoute.Use(middleware.UserAuth())
		{
			packagePlanInstanceRoute.GET("/", controller.GetAllPackagePlanInstances)
			packagePlanInstanceRoute.GET("/count", controller.CountAllPackagePlanInstances)
			packagePlanInstanceRoute.GET("/:id", controller.GetPackagePlanInstance)
			packagePlanInstanceRoute.POST("/", controller.AddPackagePlanInstance)
			packagePlanInstanceRoute.PUT("/", controller.UpdatePackagePlanInstance)
			packagePlanInstanceRoute.PUT("/disable/:id", controller.DisablePackagePlanInstance)
			packagePlanInstanceRoute.PUT("/enable/:id", controller.EnablePackagePlanInstance)
			packagePlanInstanceRoute.DELETE("/:id", controller.DeletePackagePlanInstance)
			packagePlanInstanceRoute.DELETE("/deleteByIds", controller.DeletePackagePlanInstanceByIds)
		}

		optionRoute := apiRouter.Group("/option")
		optionRoute.Use(middleware.RootAuth([]int64{middleware.PermissionSetting}...))
		{
			optionRoute.GET("/", controller.GetOptions)
			optionRoute.PUT("/", controller.UpdateOption)
		}
		channelRoute := apiRouter.Group("/channel")
		channelRoute.Use(middleware.RootAuth([]int64{middleware.PermissionChannel}...))
		{
			channelRoute.GET("/", controller.GetAllChannels)
			channelRoute.GET("/search", controller.SearchChannels)
			channelRoute.GET("/count", controller.CountChannels)
			channelRoute.GET("/models", controller.ListModels)
			channelRoute.GET("/:id", controller.GetChannel)
			channelRoute.GET("/test", controller.TestChannels)
			channelRoute.GET("/test/:id", controller.TestChannel)
			channelRoute.GET("/testAdvance/:id", controller.TestChannelAdvance)
			channelRoute.POST("/customTest", controller.CustomTest)
			channelRoute.GET("/update_balance", controller.UpdateAllChannelsBalance)
			channelRoute.GET("/update_balance/:id", controller.UpdateChannelBalance)
			channelRoute.POST("/", controller.AddChannel)
			channelRoute.PUT("/", controller.UpdateChannel)
			//弃用，使用 DeleteChannelByType
			//channelRoute.DELETE("/disabled", controller.DeleteDisabledChannel)
			channelRoute.DELETE("/delete_disabled_channel_by_type/:type", controller.DeleteChannelByType)
			channelRoute.DELETE("/delete_channel_by_disable_reason/:reason", controller.DeleteChannelByDisableReason)
			channelRoute.DELETE("/deleteByIds", controller.DeleteChannelByIds)
			channelRoute.DELETE("/:id", controller.DeleteChannel)
			channelRoute.GET("/count_by_status", controller.CountChannelByStatus)
			channelRoute.GET("/upstream_models/:id", controller.FetchUpstreamModels)
			channelRoute.GET("/metrics/scores", controller.GetChannelMetricsScores)
			channelRoute.GET("/metrics/models", controller.GetAvailableMetricsModels)
			channelRoute.GET("/statistics", controller.GetChannelStatistics)
			channelRoute.GET("/creation_speed_statistics", controller.GetChannelCreationSpeedStatistics)
			channelRoute.GET("/creation_rpm", controller.GetChannelCreationRPM)
		}
		abilityRoute := apiRouter.Group("/ability")
		abilityRoute.Use(middleware.RootAuth([]int64{middleware.PermissionAbility}...))
		{
			abilityRoute.GET("/", controller.GetAllAbilities)
			abilityRoute.GET("/count", controller.CountAbilities)
			abilityRoute.POST("/", controller.AddAbility)
			abilityRoute.PUT("/", controller.UpdateAbility)
			abilityRoute.PUT("/toggle-enabled", controller.ToggleAbilityEnabled)
			abilityRoute.DELETE("/deleteByIds", controller.DeleteAbilitiesByIds)
		}
		channelGroupRoute := apiRouter.Group("/channelGroup")
		channelGroupRoute.Use(middleware.RootAuth([]int64{middleware.PermissionChannel}...))
		{
			channelGroupRoute.GET("/", controller.GetAllChannelGroups)
			channelGroupRoute.GET("/count", controller.CountChannelGroups)
			channelGroupRoute.GET("/search", controller.SearchChannelGroups)
			channelGroupRoute.GET("/models", controller.ListModels)
			channelGroupRoute.GET("/:id", controller.GetChannelGroup)
			channelGroupRoute.POST("/", controller.AddChannelGroup)
			channelGroupRoute.PUT("/", controller.UpdateChannelGroup)
			channelGroupRoute.DELETE("/disabled", controller.DeleteDisabledChannelGroup)
			channelGroupRoute.DELETE("/:id", controller.DeleteChannelGroup)
			channelGroupRoute.POST("/addChannels", controller.AddChannelsToGroup)
		}
		tokenRoute := apiRouter.Group("/token")
		tokenRoute.Use(middleware.JWTAuth(), middleware.UserAuth())
		{
			tokenRoute.GET("/", controller.GetAllTokens)
			tokenRoute.GET("/count", controller.CountTokens)
			tokenRoute.GET("/search", controller.SearchTokens)
			//tokenRoute.GET("/export", controller.ExportTokens)
			tokenRoute.GET("/:id", controller.GetToken)
			tokenRoute.GET("/default", controller.GetDefaultToken)
			tokenRoute.POST("/", controller.AddToken)
			tokenRoute.POST("/batch", controller.AddTokenBatch)
			tokenRoute.PUT("/", controller.UpdateToken)
			tokenRoute.DELETE("/:id", controller.DeleteTokenOrRefreshInitialToken)
			tokenRoute.DELETE("/deleteByIds", controller.DeleteTokenByIds)
			tokenRoute.POST("/admin_topup", middleware.RootAuth(), controller.AdminTopUpToken) //管理员手动为令牌充值

		}
		redemptionRoute := apiRouter.Group("/redemption")
		redemptionRoute.Use(middleware.RootAuth([]int64{middleware.PermissionRedemption}...))
		{
			redemptionRoute.GET("/", controller.GetAllRedemptions)
			redemptionRoute.GET("/count", controller.CountAllRedemptions)
			redemptionRoute.GET("/search", controller.SearchRedemptions)
			redemptionRoute.GET("/:id", controller.GetRedemption)
			redemptionRoute.POST("/", controller.AddRedemption)
			redemptionRoute.PUT("/", controller.UpdateRedemption)
			redemptionRoute.DELETE("/:id", controller.DeleteRedemption)
			redemptionRoute.DELETE("/deleteByIds", controller.DeleteRedemptionByIds)

		}
		logRoute := apiRouter.Group("/log")
		logRoute.Use(middleware.JWTAuth())
		logRoute.GET("/", middleware.UserAuth(), controller.GetAllLogs)
		logRoute.GET("/count", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.LogsCountCacheKey), controller.CountAllLogs)
		logRoute.GET("/detail", middleware.RootAuth([]int64{middleware.PermissionLogDetail}...), controller.GetLogDetailById)
		logRoute.DELETE("/", middleware.RootAuth(), controller.DeleteHistoryLogs)
		logRoute.GET("/stat", middleware.UserAuth(), controller.GetLogsStat)
		logRoute.GET("/model_usage", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.LogsModelUsageCacheKey), controller.GetAllLogsModelUsage)
		logRoute.GET("/getDailyUsageStatsByDimension", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.DailyUsageStatsCacheKey), controller.GetAllDailyUsageStatsByDimension)
		logRoute.GET("/self/stat", middleware.UserAuth(), controller.GetLogsSelfStat)
		logRoute.GET("/self/model_usage", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.UserLogsModelUsageCacheKey), controller.GetUserLogsModelUsage)
		logRoute.GET("/self/getDailyUsageStatsByDimension", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.UserDailyUsageStatsCacheKey), controller.GetUserDailyUsageStatsByDimension)
		logRoute.GET("/search", middleware.UserAuth(), controller.SearchAllLogs)
		logRoute.GET("/self", middleware.UserAuth(), controller.GetUserLogs)
		logRoute.GET("/self/topup", middleware.UserAuth(), controller.GetUserTopupLogs)
		logRoute.GET("/self/search", middleware.UserAuth(), controller.SearchUserLogs)
		logRoute.GET("/self/count", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.UserLogsCountCacheKey), controller.CountUserLogs)
		logRoute.POST("/transferDataToOptimizer", middleware.RootAuth(), optimizer.TransferDataToOptimizer)
		logRoute.POST("/testOptimizerConnection", middleware.RootAuth(), optimizer.TestOptimizerConnection)
		logRoute.GET("/syncProgress", middleware.RootAuth(), optimizer.GetSyncProgress)
		logDetailRoute := apiRouter.Group("/logDetail")
		logDetailRoute.Use(middleware.JWTAuth(), middleware.RootAuth([]int64{middleware.PermissionLog}...))
		logDetailRoute.DELETE("/", middleware.RootAuth([]int64{middleware.PermissionLogDetail}...), controller.DeleteHistoryLogDetails)
		logDetailRoute.GET("/stat", middleware.RootAuth([]int64{middleware.PermissionLogDetail}...), middleware.DWDetailLogSwitchCheck(), controller.GetLogDetailsStat)
		logDetailRoute.GET("/self/stat", middleware.UserAuth(), middleware.DWDetailLogSwitchCheck(), controller.GetLogDetailsSelfStat)
		logDetailRoute.POST("/truncate", middleware.RootAuth([]int64{middleware.PermissionLogDetail}...), controller.TruncateLogExtendsTable)

		dataRoute := apiRouter.Group("/data")
		dataRoute.GET("/", middleware.AdminAuth(), controller.GetModelUsageData)
		dataRoute.GET("/self", middleware.UserAuth(), controller.GetUserQuotaDates)
		dataRoute.GET("/model_stats", middleware.UserAuth(), controller.GetModelUsageStats)
		dataRoute.DELETE("/", middleware.RootAuth(), controller.DeleteQuotaDataByTime)
		dataRoute.POST("/truncate", middleware.RootAuth(), controller.TruncateQuotaData)

		groupProGroup := apiRouter.Group("/groupPro")
		groupProGroup.Use(middleware.UserAuth())
		groupProGroup.GET("/selectable", controller.GetSelectableGroups)

		adminGroupProGroup := groupProGroup.Group("/")
		adminGroupProGroup.Use(middleware.AdminAuth())
		{
			adminGroupProGroup.GET("/", controller.GetAllGroups)
			adminGroupProGroup.GET("/count", controller.CountAllGroups)
			adminGroupProGroup.GET("/:id", controller.GetGroup)
			adminGroupProGroup.POST("/", controller.AddGroup)
			adminGroupProGroup.PUT("/", controller.UpdateGroup)
			adminGroupProGroup.DELETE("/:id", controller.DeleteGroup)
		}

		groupRoute := apiRouter.Group("/group")
		groupRoute.Use(middleware.AdminAuth())
		{
			groupRoute.GET("/", controller.GetGroups)
		}
		agencyGroupRoute := apiRouter.Group("/group/agency")
		agencyGroupRoute.Use(middleware.AgencyAuth())
		{
			agencyGroupRoute.GET("/", controller.GetGroups)
		}
		mjRoute := apiRouter.Group("/mj")
		mjRoute.GET("/self", middleware.UserAuth(), controller.GetUserMidjourney)
		mjRoute.GET("/self/count", middleware.UserAuth(), redis_armor.Shield(300, redis_armor.UserMidjourneyCountCacheKey), controller.GetUserMidjourneyCount)
		mjRoute.GET("/", middleware.RootAuth(), controller.GetAllMidjourney)
		mjRoute.GET("/count", middleware.RootAuth([]int64{middleware.PermissionLog}...), redis_armor.Shield(300, redis_armor.MidjourneyCountCacheKey), controller.GetAllMidjourneyCount)
		// 添加新的删除路由
		mjRoute.DELETE("/", middleware.RootAuth(), controller.DeleteMidjourneyLogs)
		// 添加新的刷新单个任务路由
		mjRoute.POST("/refresh/:mjId", middleware.UserAuth(), controller.RefreshMidjourneyTask)

		// 任务管理路由（基于new-api架构）
		taskRoute := apiRouter.Group("/task")
		taskRoute.Use(middleware.UserAuth())
		{
			taskRoute.GET("/", controller.GetUserTasks)
			taskRoute.GET("/statistics", controller.GetTaskStatistics)
			taskRoute.GET("/suno/:task_id", controller.GetSunoTaskResults)
		}

		dashboardRoute := apiRouter.Group("/dashboard")
		dashboardRoute.Use(middleware.AdminAuth())
		{
			dashboardRoute.GET("/", controller.GetDashboardSummary)
		}

		sensitiveWordRoute := apiRouter.Group("/sensitiveWord")
		sensitiveWordRoute.Use(middleware.RootAuth([]int64{middleware.PermissionSetting}...))
		{
			sensitiveWordRoute.GET("/", controller.GetAllSensitiveWords)
			sensitiveWordRoute.GET("/count", controller.CountSensitiveWords)
			sensitiveWordRoute.POST("/", controller.AddSensitiveWord)
			sensitiveWordRoute.POST("/batch", controller.AddBatchSensitiveWord)
			sensitiveWordRoute.POST("/load", controller.LoadAllSensitiveWords)
			sensitiveWordRoute.PUT("/", controller.UpdateSensitiveWord)
			sensitiveWordRoute.DELETE("/:id", controller.DeleteSensitiveWord)
			sensitiveWordRoute.DELETE("/deleteByIds", controller.DeleteSensitiveWordByIds)
		}

		jobRoute := apiRouter.Group("/job")
		jobRoute.Use(middleware.RootAuth([]int64{middleware.PermissionSetting}...))
		{
			jobRoute.GET("/", controller.GetAllJobs)
			jobRoute.GET("/count", controller.CountJobs)
			jobRoute.POST("/", controller.AddJob)
			jobRoute.PUT("/", controller.UpdateJob)
			jobRoute.DELETE("/:id", controller.DeleteJob)
			jobRoute.DELETE("/deleteByIds", controller.DeleteJobByIds)
		}

		redPacketRoute := apiRouter.Group("/redPacket")
		redPacketRoute.Use(middleware.RootAuth())
		{
			redPacketRoute.GET("/", controller.GetAllRedPackets)
			redPacketRoute.DELETE("/:id", controller.DeleteRedPacket)
			redPacketRoute.POST("/", controller.CreateRedPacket)
		}

		customPromptRoute := apiRouter.Group("/customPrompt")
		customPromptRoute.Use(middleware.RootAuth([]int64{middleware.PermissionSetting}...))
		{
			customPromptRoute.GET("/", controller.GetAllCustomPrompts)
			customPromptRoute.GET("/count", controller.CountCustomPrompts)
			customPromptRoute.POST("/", controller.InsertCustomPrompts)
			customPromptRoute.PUT("/", controller.UpdateCustomPrompts)
			customPromptRoute.DELETE("/:id", controller.DeleteCustomPrompts)
		}

		// Agency routes for authenticated users (including agents)
		agencyRoute := apiRouter.Group("/agency")
		// 获取当前用户的代理商信息（如果是代理商）
		agencyRoute.GET("/self", middleware.JWTAuth(), middleware.UserAuth(), controller.GetSelfAgency)
		agencyRoute.Use(middleware.JWTAuth(), middleware.AgencyAuth())
		{
			// stats
			agencyRoute.GET("/stats", controller.GetAgencyStats)

			// 更新代理商信息（仅限代理商自己）
			agencyRoute.PUT("/self", controller.UpdateSelfAgency)

			// 获取代理商下属用户列表（仅限代理商）
			agencyRoute.GET("/users", controller.GetAgencyUsers)

			// 设置代理商自定义价格（仅限代理商）
			agencyRoute.POST("/price", controller.SetAgencyPrice)

			// 获取代理商自定义价格（仅限代理商）
			agencyRoute.GET("/price", controller.GetAgencyPrice)

			// 设置代理商自定义首页（仅限代理商）
			agencyRoute.POST("/homepage", controller.SetAgencyHomepage)

			// 获取代理商自定义首页（仅限代理商）
			agencyRoute.GET("/homepage", controller.GetAgencyHomepage)

			// 设置代理商logo（仅限代理商）
			agencyRoute.POST("/logo", controller.SetAgencyLogo)

			// 获取代理商logo（仅限代理商）
			agencyRoute.GET("/logo", controller.GetAgencyLogo)

			// 获取代理商佣金结算记录（代理商用户可查看自己的记录）
			agencyRoute.GET("/commission-settlements", controller.GetAgencyCommissionSettlements)

			// 获取代理商佣金结算统计（代理商用户可查看自己的统计）
			agencyRoute.GET("/settlement-stats", controller.GetAgencyCommissionSettlementStats)
		}

		// 代理商路由 获取当前用户的现金交易记录
		agencyCashTransactionRoute := apiRouter.Group("/agency/cash-transaction")
		agencyCashTransactionRoute.Use(middleware.JWTAuth(), middleware.UserAuth())
		{
			// 获取代理商佣金记录
			agencyCashTransactionRoute.GET("/commissions", controller.GetAgencyCommissions)

			// 统计代理商佣金记录数量
			agencyCashTransactionRoute.GET("/commissions/count", controller.CountAgencyCommissions)
		}

		// Admin routes for managing agencies
		adminAgencyRoute := apiRouter.Group("/admin/agency")
		adminAgencyRoute.Use(middleware.RootAuth())
		{
			// 创建代理商（仅限管理员）
			adminAgencyRoute.POST("/", controller.CreateAgency)

			// 获取所有代理商列表（仅限管理员）
			adminAgencyRoute.GET("/", controller.GetAllAgencies)

			// 获取代理商数量（仅限管理员）
			adminAgencyRoute.GET("/count", controller.CountAgencies)

			// 获取特定代理商信息（仅限管理员）
			adminAgencyRoute.GET("/:id", controller.GetAgency)

			// 删除代理商（仅限管理员）
			adminAgencyRoute.DELETE("/:id", controller.DeleteAgency)

			// 更新代理商信息（仅限管理员）
			adminAgencyRoute.PUT("/:id", controller.UpdateAgency)

			// 代理商佣金结算（仅限管理员）
			adminAgencyRoute.POST("/:id/settle-commission", controller.SettleAgencyCommission)

			// 获取所有代理商的佣金结算记录（仅限管理员）
			adminAgencyRoute.GET("/commission-settlements", controller.GetAllAgencyCommissionSettlements)

			// 获取管理员结算统计（仅限管理员）
			adminAgencyRoute.GET("/settlement-stats", controller.GetAdminSettlementStats)
		}

		redisRoute := apiRouter.Group("/redis")
		redisRoute.Use(middleware.RootAuth())
		{
			redisRoute.GET("/keys", controller.GetRedisKeys)
			redisRoute.POST("/get", controller.GetRedisValue)
			redisRoute.GET("/search/:prefix", controller.SearchRedisKeys)
			redisRoute.POST("/delete", controller.DeleteRedisKey)
			redisRoute.POST("/ua-analysis", controller.GetUserAgentAnalysis)
		}

		// 充值记录路由 - 添加在管理员相关的路由附近
		apiRouter.GET("/topup/records", middleware.AdminAuth(), controller.GetTopUpRecords)         // 管理员获取充值记录
		apiRouter.GET("/topup/self/records", middleware.UserAuth(), controller.GetUserTopUpRecords) // 用户获取自己的充值记录

		// 敏感词命中记录相关接口
		sensitiveWordHitGroup := apiRouter.Group("/sensitiveWordHit")
		{
			sensitiveWordHitGroup.GET("/", controller.GetSensitiveWordHits)
			sensitiveWordHitGroup.DELETE("/:id", controller.DeleteSensitiveWordHit)
			sensitiveWordHitGroup.DELETE("/batch", controller.DeleteSensitiveWordHitsByTime)
		}

		// 服务器日志相关路由
		serverLogRoute := apiRouter.Group("/server-log")
		serverLogRoute.Use(middleware.RootAuth())
		{
			// WebSocket 连接获取实时日志
			serverLogRoute.GET("/tail", controller.TailServerLog)
			// 获取最近的n行日志
			serverLogRoute.GET("/recent", controller.GetRecentServerLog)
			// 获取日志文件列表
			serverLogRoute.GET("/files", controller.GetServerLogFiles)
			// 下载指定的日志文件
			serverLogRoute.GET("/download/:filename", controller.DownloadServerLogFile)
		}

		// 添加支付宝回调接口不需要认证
		apiRouter.POST("/alipay/callback", controller.AlipayF2FCallback)
		// 添加随行付回调接口不需要认证
		apiRouter.POST("/suixingpay/callback", controller.SuixingpayCallback)

		// 管理员专用超时配置路由组，放在 userRoute 外面
		adminTimeoutRoute := apiRouter.Group("/admin")
		adminTimeoutRoute.Use(middleware.JWTAuth(), middleware.RootAuth())
		{
			// 管理员超时配置管理路由
			adminTimeoutRoute.GET("/user-timeout-configs", controller.GetAllUserTimeoutConfigs)          // 获取所有用户的超时配置
			adminTimeoutRoute.GET("/user-timeout-config/:user_id", controller.GetUserTimeoutConfigs)     // 获取指定用户的所有超时配置
			adminTimeoutRoute.POST("/user-timeout-config", controller.SetUserTimeoutConfig)              // 设置用户超时配置
			adminTimeoutRoute.PUT("/user-timeout-config/:id", controller.UpdateUserTimeoutConfig)        // 更新用户超时配置（新增）
			adminTimeoutRoute.DELETE("/user-timeout-config/:id", controller.DeleteUserTimeoutConfigByID) // 删除用户超时配置
			adminTimeoutRoute.POST("/user-timeout-config/load", controller.LoadAllUserTimeoutConfigs)    // 装载所有超时配置到内存缓存
		}
	}
}
