git add .
git commit -m "feat: 改进 Suno 错误处理并兼容多种响应格式

🔧 错误处理改进:
- 将所有错误信息直接返回给用户，便于问题排查和调试
- 保留详细的后台日志记录，包含敏感信息用于管理员调试
- 改进 HTTP 请求错误处理，增加状态码检查和详细日志
- 优化 JSON 解析错误处理，记录原始响应内容
- 截断过长的日志内容，避免日志文件过大

📋 响应格式兼容性:
- 兼容 Suno API 的两种响应格式：
  * 复杂对象格式: {\"data\": {\"task_id\": \"xxx\", \"status\": \"SUCCESS\", ...}}
  * 简单字符串格式: {\"data\": \"task-id-string\"}
- 新增 parseSunoResponseData() 函数自动识别和解析不同格式
- 修改 SunoResponse.Data 为 interface{} 类型支持动态解析
- 更新所有使用响应数据的代码，使用统一的解析接口

🛠️ 技术改进:
- 增强 submitSunoTask() 函数的错误处理和日志记录
- 改进 fetchSunoTaskStatus() 函数的网络错误处理
- 优化流式响应处理，支持多种数据格式
- 添加详细的 HTTP 状态码检查和错误分类

🎯 用户体验:
- 用户现在可以看到具体的错误信息而不是通用提示
- 网络问题、认证问题、参数问题等都有明确的错误描述
- 便于用户自行解决简单问题或向技术支持提供准确信息

🔍 调试支持:
- 后台日志包含完整的请求/响应信息和上下文
- 错误日志包含 BaseURL、Model、TaskID 等关键调试信息
- 响应内容自动截断，避免日志过长影响性能

🔄 向后兼容:
- 完全兼容现有的复杂响应格式
- 新增对简单响应格式的支持
- 不影响现有功能和 API 调用
